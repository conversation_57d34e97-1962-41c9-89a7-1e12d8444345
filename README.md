### Introduction

This document documents the key aspects of Medxoom's data warehouse. Specifically, it will have the design philosophy and things to remember for whoever is working on this. It will also have the most common workflow for creating data assets for reporting. This document is a living document and will get updated as we add more components to our workflow/toolset. Our goal behind building a data warehouse is to support 'data-as-a-product'.

Test



### Tools used

- dbt-core : For managing data pipeline code
- BigQuery : Data Warehouse
- Any other GCP utility such as Cloud-Run to create a staging table in BigQuery.
- python, SQL
  - devs working on this are encouraged to create a fresh virtual env locally for installing python packages.



### Common Terminology

We'll use some terms that persist across the board. Following are the common/recurring terms:

- <a name = 'dtl'>Data Team Lead</a> : Someone with the privileges to deploy a pipeline to prod BQ atleast and approve PRs
- <a name = "dataset">Dataset</a> : A generic term used to refer to either a view or a table. Basically a 'data artifact' in our data warehouse.
- <a  name = "dw">Data Warehouse</a>: Where all the data artifacts live. 
- <a name = "dbt model">dbt model</a> : a .sql file containing SQL-inspired code in the dbt project that's basically a set of rules that either define a table or a view.



### Design

High level architecture [here](https://lucid.app/lucidchart/397e1e19-4460-4074-be78-ad610a061a66/edit?page=gIJq3ijRNkBf&invitationId=inv_f367b6f4-4ac0-4e80-a9b1-53a33c72d6bb#) .

We're focusing on developing a **'lakehouse'** rather than a conventional warehouse. It differs from the traditional design of cleaning the data before actually loading it in the warehouse, something known as **ETL** . We'll do **ELT** instead where we stage all raw data sources in BigQuery either as a native table or as an external table. This allows us to not skip any data points and have all the data sources in one single place so they can be referenced later. All the data assets ultimately would be derived from the raw data. So we have 3 zones or 'layers':

- **Landing_Zone**
  - Only contains raw data.
  - Contains data replicated from MySQL DB instances.
  - Is the 'default' dataset for a dbt-core model to get materialized into. Learn more about dbt-core 'models' [here](https://docs.getdbt.com/docs/introduction).
- **Silver_Zone**
  - Contains 'intermediate' data. Meaning, data that's not fully quality controlled but is still better than Landing_Zone data in terms of having filters like 'Active' = 1 , non-terminated eligibilities ,etc.
  - This layer is meant to serve as a place for storing 'views' created out of source tables from the Landing_Zone. Models up-stream should use these 'views' rather than referencing the raw tables directly in the Landing_Zone.
  - Can support ad-hoc reports that are not business critical or where there is no concrete agreement on the rules that define a data model.
  - Can serve as a building block for higher quality data.
- **Gold_Layer**
  - This is where the highest quality datasets will live either as a view or a table.
  - These will be used to power reports such as 'Billable Subscriber Counts per Billing Partner' etc.
  - Once a data pipeline has been created by stitching together multiple models in a dev env , each model within it has to be subject to a set of test cases that can be built in dbt itself before it qualifies for Gold_Layer. 



### Workflow

All data that gets promoted up to the Gold_Layer ideally should go through the following workflow. We'll assume that the ultimate output of all work done by the data team is to create a Quality Checked ['Dataset'](#dataset)  or a set of [Datasets](#dataset) that power some report:

- A story is created with UAT criteria set. The UAT criteria are typically the business rules that must be followed for all the data points within the 
- A new branch is created off of the Mx.DataWarehouse repo that has the story number in it's name. Eg. 1234-create-billing-report
- A data team dev checks out this branch and puts all the code relevant for this story in either a single .sql file or multiple .sql files(these .sql files are essentially dbt 'models') and puts them in the appropriate folder/layer of the dbt-core project.
- creates test cases and embeds them within the models created above and finally outputs a model into the gold_layer.
- Opens a PR for the Data Team Lead to approve.
- The branch is pulled down by the Data Team Lead deploys this in the BQ prod env.
- A UAT member either downloads the data artifact from prod or views the report if a report is being powered by this data artifact and does their testing.
- Once approved from the UAT member, the PR gets merged finally into the master branch and the associated work item is closed.



The workflow is similar to how the dev team has been doing things so far with the exception that the data artifact has to be created in BQ prod with prod data first for the UAT member to test it since all data in the dev environments is dummy data. Eg. of a scenario demonstrating why this is important is:

Consider a case when we want to exclude 'demo' accounts such as the "Appleseed" family from the data altogether, this would require a test case to be put in place that says something like `where name not like '%Appleseed%'` but this might result in no data being outputted in the dev env. Which is fine. So unless we deploy to prod, where real data points live, we won't be able to see the expected output.

### Useful commands

## Generate model schemas
dbt  --quiet run-operation generate_model_yaml --args "{"model_names": ["active_eligibilities_registration_per_partner"]}" > models/gold_layer/active_eligibilities_registration_per_partner.yml

## Run model tests
dbt test --project-dir Analytics/

## Run model build
dbt build --project-dir Analytics

## Run model run (actual creation)
dbt run --project-dir Analytics