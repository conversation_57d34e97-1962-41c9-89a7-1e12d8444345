Below is the complete "Medxoom Data Warehouse Architecture, Design and Nomenclature Standards" document, incorporating all sections and updates discussed so far.

---

# Medxoom Data Warehouse Architecture, Design and Nomenclature Standards

---

**Table of Contents**

1. [Introduction](#introduction)  
2. [Environment Overview](#environment-overview)  
3. [ODS (Operational Data Store)](#ods-operational-data-store)  
4. [Data Warehouse (DW) Layer & Processing Guidelines](#data-warehouse-dw-layer--processing-guidelines)  
 4.1. [DW Layer Characteristics](#dw-layer-characteristics)  
 4.2. [DW Processing](#dw-processing)  
5. [DM (Data Mart) Processing](#dm-data-mart-processing)  
 5.1. [DM Process Characteristics](#dm-process-characteristics)  
6. [Reporting](#reporting)  
7. [Time Zone](#time-zone)  
8. [General Naming Conventions](#general-naming-conventions)  
 8.1. [Naming Principles](#naming-principles)  
 8.2. [Project & Dataset Naming (BigQuery)](#project--dataset-naming-bigquery)  
 8.3. [Table & View Naming](#table--view-naming)  
9. [DM (Data Mart/Data Model) Specifics](#dm-data-martdata-model-specifics)  
10. [DBT Coding and Naming Conventions](#dbt-coding-and-naming-conventions)  
11. [DBT Project Structure & Coding Standards](#dbt-project-structure--coding-standards)  
 11.1. [Folder Structure](#folder-structure)  
 11.2. [Model File Naming](#model-file-naming)  
12. [Seed Files Management & Processing](#seed-files-management--processing)  
 12.1. [Seed File Maintenance](#seed-file-maintenance)  
 12.2. [Processing Seeds in DBT](#processing-seeds-in-dbt)  
13. [Best Practices for Deployment & Version Control](#best-practices-for-deployment--version-control)  
14. [Processing](#processing)  
 14.1. [Data Flow Overview](#data-flow-overview)  
  14.1.1. [MySQL to ODS](#mysql-to-ods)  
  14.1.2. [ODS to DW](#ods-to-dw)  
  14.1.3. [DW to DM/Gold](#dw-to-dmgold)  
15. [Summary & Appendices](#summary--appendices)  
 15.1. [Summary](#summary)  
 15.2. [Appendices](#appendices)  
  - [Appendix A: Naming Examples](#appendix-a---naming-examples)  
  - [Appendix B: Sample dbt_project.yml Seed Configuration](#appendix-b---sample-dbt_projectyml-seed-configuration)  

---

## 1. Introduction

This document defines the standard naming conventions, coding guidelines, and seed file management practices for our BigQuery and DBT environment. It is designed to ensure consistency, maintainability, and scalability by establishing clear standards for naming objects, organizing data layers, and managing processing and timestamps.

---

## 2. Environment Overview

- **BigQuery:**  
  Our primary data platform hosting raw, historical, and transformed data.

- **DBT (Data Build Tool):**  
  Our framework for developing, testing, and documenting SQL models.

- **Data Layers:**  
  - **ODS (Operational Data Store):**  
    Contains data extracted from the OLTP system (MySQL) and refreshed every 3 hours.
  - **DW (Data Warehouse) Layer:**  
    Receives data from ODS every three hours and maintains full historical snapshots of data changes.
  - **DM (Data Mart) / Silver & Gold Layers:**  
    The DM process populates the data into the Gold layer for reporting. (Note: All tables that were previously in the Silver layer have now been moved to the Gold layer.)

- **Seed Files:**  
  Static CSV datasets maintained under version control and loaded via DBT for reference data or configuration.

---

## 3. ODS (Operational Data Store)

- **Source System:**  
  The ODS layer extracts data from the OLTP system (MySQL) and refreshes every 3 hours.
- **Purpose:**  
  Acts as the staging area for raw operational data before it is moved into the DW layer.
- **Note:**  
  Reporting is not performed directly from the ODS.

---

## 4. Data Warehouse (DW) Layer & Processing Guidelines

### 4.1. DW Layer Characteristics

- **Separation from ODS:**  
  The DW layer is distinct from the ODS and receives data every three hours.
- **Historical Maintenance:**  
  DW tables maintain a full history of data. When a record is updated, the previous version is ended (by setting an end date) and a new record is inserted.
- **T_BATCH_CONTROL:**  
  The DW layer contains control tables such as `T_BATCH_CONTROL`, which holds the current business date used to govern data movement from DW to DM.
- **Processing Views:**  
  DW views are used to filter and process data into the DM layers based on the batch control date. Future views may support querying across multiple tables to display data as of any user-specified date.

### 4.2. DW Processing

- **Frequency:**  
  The DW process runs every three hours to capture historical snapshots from the ODS.
- **Data Capture:**  
  As data is refreshed from the OLTP system, the DW layer captures a snapshot, preserves history, and applies necessary versioning.
- **Usage:**  
  The DW layer serves as the foundation for all downstream processes, but it is not used directly for reporting.

---

## 5. DM (Data Mart) Processing

### 5.1. DM Process Characteristics

- **Frequency:**  
  The DM process runs once per day, after the business day has ended.
- **Batch Control Update:**  
  After successful execution, the DM process updates the batch control date in the `T_BATCH_CONTROL` table to the next day. This date represents the business date for which processing is performed.
- **Data Flow:**  
  The updated batch control date drives processing from DW to DM, ensuring that data for one calendar day is processed at a time.
- **Backlog Handling:**  
  If a backlog exists, the support team must run the process for each backlogged day.
- **Outcome:**  
  The DM process prepares cleansed, dimensional, and aggregated data. With the migration, all tables are now in the Gold layer and serve as the primary source for reporting.

- **Periodic Gold Processing Batches:**  
  In addition to the daily DM process, periodic batches update Gold tables (e.g., every Monday, every 3rd day of the month, etc.) to maintain a historical record of reported data.

---

## 6. Reporting

- **Primary Reporting Tool:**  
  Sigma Reporting (https://www.sigmacomputing.com/) is used for all reporting.
- **Reporting Data Sources:**  
  Reporting is executed exclusively from the Gold layer.
  - **Gold Layer:** Contains curated, business-ready tables. Legacy Gold tables (originally prefixed with `T_` and containing `_GLD`) have been migrated here.
- **Exceptional Reporting:**  
  If reporting directly from DW or ODS is absolutely necessary, dedicated views should be created in the Gold layer to expose the required data in a controlled manner.

---

## 7. Time Zone

- **UTC Standard:**  
  All timestamps across tables and processes are maintained in UTC. This includes DBT’s `DBT_VALID_FROM` and `DBT_VALID_TO` fields, ensuring consistency and avoiding time zone-related discrepancies.

---

## 8. General Naming Conventions

### 8.1. Naming Principles

- **Clarity & Consistency:**  
  Names must be descriptive and follow a consistent pattern.
- **Simplicity:**  
  Use concise names that clearly convey the object’s purpose.
- **Lowercase & Underscores:**  
  Use lowercase letters with underscores for separation (e.g., `customer_orders`).

### 8.2. Project & Dataset Naming (BigQuery)

- **Project Name:**  
  Reflects the business domain or team (e.g., `mycompany-analytics`).
- **Dataset Names:**  
  - **Raw Layer:** Prefix with `raw_` (e.g., `raw_sales`).
  - **Staging Layer:** Prefix with `stg_` (e.g., `stg_marketing`).
  - **DW Layer:** Contains historical snapshots and control tables (e.g., `dw_enterprise`).
  - **Gold Layer:** Contains curated, BI-ready tables (e.g., `gold_reporting`).

### 8.3. Table & View Naming

- **Data Mart Tables:**  
  - **Dimensions:** Must start with `D_` (e.g., `D_customer`, `D_product`).
  - **Facts:** Must start with `F_` (e.g., `F_sales`, `F_transactions`).
- **Legacy Gold Tables:**  
  - Tables originally prefixed with `T_` and containing `_GLD` should be migrated to the Gold layer and renamed appropriately (e.g., from `T_sales_GLD` to `gold_sales_summary`).
- **DW Tables:**  
  - Include control and historical tables such as `T_BATCH_CONTROL`.
- **Views:**  
  - All views must be prefixed with `V_` (e.g., `V_active_customers`).
  - **Usage Guidelines:**  
    - DW views process data from the DW layer into the DM layers by incorporating the batch date from `T_BATCH_CONTROL`.
    - Future views may support multi-table joins to display data as of any user-specified date.
    - Avoid creating physical tables unless performance issues necessitate them.
- **Columns:**  
  - Use clear, descriptive names (e.g., `customer_id`, `order_date`).
  - Avoid reserved keywords and maintain consistent naming for keys (e.g., `id` for primary keys and `<table>_id` for foreign keys).

---

## 9. DM (Data Mart/Data Model) Specifics

- **Subject Areas:**  
  Organize DM objects by business domains (e.g., `dm_customer`, `dm_product`).
- **Granularity and Aggregation:**  
  - Maintain fact tables at the lowest grain possible to support detailed analysis.
  - Build aggregate tables on top of these detailed fact tables for performance or reporting needs (e.g., `dm_sales_monthly`).
- **Naming:**  
  Use prefixes (`F_` for facts, `D_` for dimensions) to clearly distinguish table types.

---

## 10. DBT Coding and Naming Conventions

- **General Guidelines:**  
  - **Formatting:**  
    - Capitalize SQL keywords (e.g., `SELECT`, `FROM`, `WHERE`).
    - Use consistent indentation (2 or 4 spaces) for nested statements.
    - Break long SQL statements into multiple lines for improved readability.
  - **Comments:**  
    - Include a header comment in each file with its purpose, authorship, and modification history.
    - Use inline comments (`--`) to explain complex or non-obvious logic.
- **Model File Naming:**  
  - Use names that mirror the corresponding BigQuery objects.
  - For incremental models, append `_inc` to the file name (e.g., `F_orders_inc.sql`).
  - Include version numbers when applicable (e.g., `D_customer_v2.sql`).
- **Macro, Test, and Documentation Conventions:**  
  - Place custom macros in a dedicated `macros/` folder.
  - Write tests inline with models or in a designated tests folder.
  - Maintain clear and up-to-date documentation using DBT’s built-in documentation features.

---

## 11. DBT Project Structure & Coding Standards

### 11.1. Folder Structure

- **Models:**  
  Organize models by layer:
  - `models/staging`
  - `models/dw` (for DW layer tables and views)
  - `models/silver` (if needed; note that all production tables now reside in Gold)
  - `models/gold` (for Gold layer and migrated Gold tables)
  - `models/marts` (for data mart and aggregated models)
- **Seeds:**  
  Store seed files under a dedicated folder (e.g., `data/seeds` or `seeds/`).
- **Macros & Tests:**  
  Place custom macros in `macros/` and tests either inline with models or in a dedicated tests folder.
- **Documentation:**  
  Maintain a `docs/` directory for additional project documentation.

### 11.2. Model File Naming

- Follow the naming conventions described in Section 8.
- Ensure model files reflect their corresponding BigQuery objects and layers.

---

## 12. Seed Files Management & Processing

### 12.1. Seed File Maintenance

- **Format & Storage:**  
  - Seed files must be maintained in CSV format and stored in the designated seeds directory (e.g., `data/seeds`).
  - Use clear, versioned naming conventions (e.g., `lookup_countries.csv`, `config_parameters.csv`).
- **Data Quality:**  
  - Regularly validate the contents of seed files to ensure data integrity.
  - Implement automated DBT tests to verify that seed data conforms to expected formats and value ranges.

### 12.2. Processing Seeds in DBT

- **Loading Seeds:**  
  - Run the DBT CLI command `dbt seed` to load seed files into BigQuery.
  - Ensure seed files are processed in the correct order, particularly when dependencies exist.
- **Configuration:**  
  - Define seed file settings in the `dbt_project.yml` file. For example:
    ```yaml
    seeds:
      my_project:
        lookup_countries:
          file: data/lookup_countries.csv
          quote_columns: true
          column_types:
            country_code: string
            country_name: string
    ```
- **Version Control & Change Management:**  
  - Maintain seed files under version control.
  - Document any changes to seed data, particularly those that may affect downstream processes.
- **Testing:**  
  - Use DBT tests to ensure that seed data meets predefined criteria (e.g., no null values in key columns, valid data ranges).

---

## 13. Best Practices for Deployment & Version Control

- **Branching Strategy:**  
  - Develop new models on feature branches and merge into the main branch after thorough code reviews.
- **Automated Testing:**  
  - Integrate automated DBT tests and custom SQL validations into the CI/CD pipeline.
- **Documentation:**  
  - Leverage DBT’s documentation features to maintain current metadata for models and columns.
- **Review Process:**  
  - Implement peer reviews for all new models, seed file updates, and SQL code modifications.

---

## 14. Processing

### 14.1. Data Flow Overview

The data processing pipeline moves data through the following stages:

1. **MySQL to ODS**  
2. **ODS to DW**  
3. **DW to DM/Gold**

#### 14.1.1. MySQL to ODS

- **Source:**  
  Data is extracted from the OLTP system (MySQL) and loaded into the ODS.
- **Frequency:**  
  The extraction and loading occur every 3 hours.
- **Purpose:**  
  The ODS acts as a staging area for raw operational data before any transformation or historical versioning.

#### 14.1.2. ODS to DW

- **Data Transfer:**  
  Data from the ODS is moved into the Data Warehouse (DW) layer.
- **Frequency:**  
  This process runs every 3 hours, ensuring that all updates are captured.
- **Historical Maintenance:**  
  DW tables preserve a full history of the data; when records are updated, previous versions are ended (with an end date) and new versions are inserted.
- **Purpose:**  
  The DW layer consolidates, versions, and preserves the history of operational data, forming the foundation for downstream processing.

#### 14.1.3. DW to DM/Gold

- **Daily DM Process:**  
  - **Batch Processing:**  
    Data is processed one calendar day at a time from the DW layer.
  - **T_BATCH_CONTROL:**  
    This control table governs the movement from DW to DM.  
    - **Operation:**  
      - After a successful DM process run for a given day, T_BATCH_CONTROL is updated to the next calendar day.
      - If there is a backlog, the support team must run the DM process for each backlogged day.
- **Periodic Gold Processing Batches:**  
  - **Purpose:**  
    In addition to daily processing, periodic batches (e.g., every Monday, every 3rd day of the month) update Gold tables to maintain a historical record of reported data.
  - **Operation:**  
    These batches update the Gold layer based on additional business rules or reporting requirements, preserving historical snapshots of reported data.

---

## 15. Summary & Appendices

### 15.1. Summary

This document provides a comprehensive guide to ensure consistency and maintainability across Medxoom’s BigQuery and DBT projects. Our architecture is built on a clear separation of data layers—ODS, DW, DM (Gold)—with defined processing frequencies. The DW process runs every three hours to capture historical snapshots from ODS, while the DM process runs once per day after the business day, updating the batch control date in the `T_BATCH_CONTROL` table for downstream processing. Periodic Gold processing batches further maintain a historical record of reported data. All timestamps are maintained in UTC, and reporting is executed through Sigma Reporting using data from the Gold layer.

### 15.2. Appendices

#### Appendix A: Naming Examples

- **Dataset Names:**  
  - DW Layer: `dw_enterprise`  
  - Gold Layer: `gold_reporting`
- **Table Names:**  
  - **DW Control Table:** `T_BATCH_CONTROL`  
  - **DM Dimensions:** `D_customer`, `D_product`  
  - **DM Facts:** `F_sales`, `F_transactions`  
  - **Legacy Gold (migrated):** From `T_sales_GLD` → `gold_sales_summary`
- **Views:**  
  - DW Views (for DM processing): `V_dw_sales_by_batch`  
  - Reporting Views (in Gold layer): `V_active_customers`
- **Model Files:**  
  - `models/dw/T_BATCH_CONTROL.sql`  
  - `models/gold/gold_sales_summary.sql`  
  - (Other models as appropriate, e.g., `models/marts/dm_sales_monthly.sql`)
- **Seed Files:**  
  - `data/seeds/lookup_countries.csv`
- **Batch Processing:**  
  - The DM process updates the `T_BATCH_CONTROL` table after a successful run, setting the batch control date to the next day (the business date for processing).

#### Appendix B: Sample dbt_project.yml Seed Configuration

```yaml
name: my_project
version: '1.0'
config-version: 2

# Seed configuration
seeds:
  my_project:
    lookup_countries:
      file: data/lookup_countries.csv
      quote_columns: true
      column_types:
        country_code: string
        country_name: string
```

---

This document serves as the definitive guide for all development and operations teams within Medxoom's BigQuery and DBT environment. Regular reviews and updates are encouraged as business requirements and technologies evolve.

--- 

Please review this complete version and let me know what additional changes or refinements you’d like to make next.