Please update the DBT YAML model documentation to meet the following standards:

1. Model Name: [name]
2. Description including:
   - Purpose of the table
   - Data grain
   - Direct Sources tables or views
   - Indirect Sources tables or views (on which the direct source tables or views depend)
   - Key business rules or filters

3. Configuration:
   - Materialization type
   - Any specific tags or configs

4. Transformations section describing:
   - Major data transformations

	Include these transformations like:

	"
      - name: base_join_structure
        description: >
          Joins member metrics with policy and user information:
          - Starts with L1 member metrics as the base
          - Links to base policy data for user identification
          - Connects to base user data for registration details
          - Adds latest login information
        joins:
          - join: V_Base_Policy_DW
            type: left
            relationship: many_to_one
            sql: "l1.Elig_Id = po.EligibilityId"
          
          - join: V_Base_User_DW
            type: left
            relationship: one_to_one
            sql: "po.UserId = bu.Id"
          
          - join: V_Latest_Login_Attempt_DW
            type: left
            relationship: one_to_one
            sql: "lg.User_Id = bu.Id"
	"
   - Business logic steps
   - Processing rules



5. Columns section with:
   - Logical grouping of columns (e.g., Keys, Metrics, Audit)
   - For each column:
     - name
     - description
     - data type
     - source_column
     - source_table
     - transformation logic (if any)

6. Meta section with:
   - owner
   - updated_at
   - depends_on list

Please exclude any table-level or column-level tests.
Follow the standard DBT YAML v2 format.