resource "google_cloud_scheduler_job" "dbt_daily_runner_scheduler" {
  name      = var.name
  schedule  = var.schedule
  time_zone = var.time_zone

  retry_config {
    retry_count = 1
  }

  http_target {
    http_method = "POST"
    uri         = "https://${var.region}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${var.gcp_project}/jobs/${var.runner_name}:run"
    body        = base64encode("")


    headers = {
      "User-Agent"   = "Google-Cloud-Scheduler"
      "Content-Type" = "application/json",
      "schedule"     = "daily"
    }

    oauth_token {
      service_account_email = var.service_account_email
    }
  }
}