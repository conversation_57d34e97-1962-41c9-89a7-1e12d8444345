resource "google_dataform_repository" "mx-dataform-repo" {
  project  = var.gcp_project
  provider = google-beta
  region   = "us-central1"
  name     = "dataform_repository"
}

resource "google_project_iam_member" "mx-dataform-default-sa-bqjobuser-permission" {
  role       = "roles/bigquery.admin"
  member     = "serviceAccount:service-${var.gcp_project_number}@gcp-sa-dataform.iam.gserviceaccount.com"
  project    = var.gcp_project
  depends_on = [google_dataform_repository.mx-dataform-repo]

}

resource "google_dataform_repository_release_config" "dataform_full_repo_release" {
  provider = google-beta

  region     = google_dataform_repository.mx-dataform-repo.region
  project    = google_dataform_repository.mx-dataform-repo.project
  repository = google_dataform_repository.mx-dataform-repo.name

  name          = "dataform_full_repo_release"
  git_commitish = "main"
  cron_schedule = "0 19 * * *"
  time_zone     = "America/New_York"

  code_compilation_config {
    default_database = var.gcp_project
    default_schema   = var.silver_dataset_id
    default_location = "US"
    assertion_schema = var.assertion_dataset_id
    database_suffix  = ""
    schema_suffix    = ""
    table_prefix     = ""
  }
}

resource "google_dataform_repository_workflow_config" "useremails_workflow" {
  provider = google-beta

  project        = google_dataform_repository.mx-dataform-repo.project
  region         = google_dataform_repository.mx-dataform-repo.region
  repository     = google_dataform_repository.mx-dataform-repo.name
  name           = "useremails_workflow"
  release_config = google_dataform_repository_release_config.dataform_full_repo_release.id

  invocation_config {
    included_targets {
      database = var.gcp_project
      schema   = var.silver_dataset_id
      name     = "user_emails_dashboard"
    }
    included_targets {
      database = var.gcp_project
      schema   = var.assertion_dataset_id
      name     = "${var.silver_dataset_id}_user_emails_dashboard_assertions_rowConditions"
    }
    included_targets {
      database = var.gcp_project
      schema   = var.assertion_dataset_id
      name     = "${var.silver_dataset_id}_user_emails_dashboard_assertions_uniqueKey_0"
    }

    transitive_dependencies_included = true
    transitive_dependents_included   = true
    service_account                  = "service-${var.gcp_project_number}@gcp-sa-dataform.iam.gserviceaccount.com"

  }

  cron_schedule = "0 12 * * FRI"
  time_zone     = "America/New_York"
}

resource "google_dataform_repository_workflow_config" "usercounts_dashboard_workflow" {
  provider = google-beta

  project        = google_dataform_repository.mx-dataform-repo.project
  region         = google_dataform_repository.mx-dataform-repo.region
  repository     = google_dataform_repository.mx-dataform-repo.name
  name           = "usercounts_dashboard_workflow"
  release_config = google_dataform_repository_release_config.dataform_full_repo_release.id

  invocation_config {
    included_targets {
      database = var.gcp_project
      schema   = var.silver_dataset_id
      name     = "usercounts"
    }

    transitive_dependencies_included = true
    transitive_dependents_included   = true
    service_account                  = "service-${var.gcp_project_number}@gcp-sa-dataform.iam.gserviceaccount.com"

  }

  cron_schedule = "0 0 * * *"
  time_zone     = "America/New_York"
}

resource "google_dataform_repository_workflow_config" "allied_member_benefits_data_workflow" {
  provider = google-beta

  project        = google_dataform_repository.mx-dataform-repo.project
  region         = google_dataform_repository.mx-dataform-repo.region
  repository     = google_dataform_repository.mx-dataform-repo.name
  name           = "allied_member_benefits_data_workflow"
  release_config = google_dataform_repository_release_config.dataform_full_repo_release.id

  invocation_config {
    included_targets {
      database = var.gcp_project
      schema   = var.silver_dataset_id
      name     = "allied_member_benefits"
    }

    transitive_dependencies_included = true
    transitive_dependents_included   = true
    service_account                  = "service-${var.gcp_project_number}@gcp-sa-dataform.iam.gserviceaccount.com"

  }

  cron_schedule = "0 21 * * *"
  time_zone     = "America/New_York"
}

resource "google_dataform_repository_workflow_config" "allied_orphaned_claims_data_workflow" {
  provider = google-beta

  project        = google_dataform_repository.mx-dataform-repo.project
  region         = google_dataform_repository.mx-dataform-repo.region
  repository     = google_dataform_repository.mx-dataform-repo.name
  name           = "allied_orphaned_claims_data_workflow"
  release_config = google_dataform_repository_release_config.dataform_full_repo_release.id

  invocation_config {
    included_targets {
      database = var.gcp_project
      schema   = var.silver_dataset_id
      name     = "allied_orphaned_claims"
    }

    transitive_dependencies_included = true
    transitive_dependents_included   = true
    service_account                  = "service-${var.gcp_project_number}@gcp-sa-dataform.iam.gserviceaccount.com"

  }

  cron_schedule = "0 21 * * *"
  time_zone     = "America/New_York"
}

resource "google_dataform_repository_workflow_config" "allied_orphaned_accums_data_workflow" {
  provider = google-beta

  project        = google_dataform_repository.mx-dataform-repo.project
  region         = google_dataform_repository.mx-dataform-repo.region
  repository     = google_dataform_repository.mx-dataform-repo.name
  name           = "allied_orphaned_accums_data_workflow"
  release_config = google_dataform_repository_release_config.dataform_full_repo_release.id

  invocation_config {
    included_targets {
      database = var.gcp_project
      schema   = var.silver_dataset_id
      name     = "allied_orphaned_accums"
    }

    transitive_dependencies_included = true
    transitive_dependents_included   = true
    service_account                  = "service-${var.gcp_project_number}@gcp-sa-dataform.iam.gserviceaccount.com"

  }

  cron_schedule = "0 21 * * *"
  time_zone     = "America/New_York"
}

resource "google_dataform_repository_workflow_config" "allied_groups_to_networks_data_workflow" {
  provider = google-beta

  project        = google_dataform_repository.mx-dataform-repo.project
  region         = google_dataform_repository.mx-dataform-repo.region
  repository     = google_dataform_repository.mx-dataform-repo.name
  name           = "allied_groups_to_networks_data_workflow"
  release_config = google_dataform_repository_release_config.dataform_full_repo_release.id

  invocation_config {
    included_targets {
      database = var.gcp_project
      schema   = var.silver_dataset_id
      name     = "allied_gpf_groups_benefits_networks"
    }

    transitive_dependencies_included = true
    transitive_dependents_included   = true
    service_account                  = "service-${var.gcp_project_number}@gcp-sa-dataform.iam.gserviceaccount.com"

  }

  cron_schedule = "0 21 * * *"
  time_zone     = "America/New_York"
}

resource "google_dataform_repository_workflow_config" "allied_groups_to_modules_data_workflow" {
  provider = google-beta

  project        = google_dataform_repository.mx-dataform-repo.project
  region         = google_dataform_repository.mx-dataform-repo.region
  repository     = google_dataform_repository.mx-dataform-repo.name
  name           = "allied_groups_to_modules_data_workflow"
  release_config = google_dataform_repository_release_config.dataform_full_repo_release.id

  invocation_config {
    included_targets {
      database = var.gcp_project
      schema   = var.silver_dataset_id
      name     = "allied_gpf_groups_modules"
    }

    transitive_dependencies_included = true
    transitive_dependents_included   = true
    service_account                  = "service-${var.gcp_project_number}@gcp-sa-dataform.iam.gserviceaccount.com"

  }

  cron_schedule = "0 21 * * *"
  time_zone     = "America/New_York"
}

resource "google_dataform_repository_workflow_config" "allied_group_plan_documents_data_workflow" {
  provider = google-beta

  project        = google_dataform_repository.mx-dataform-repo.project
  region         = google_dataform_repository.mx-dataform-repo.region
  repository     = google_dataform_repository.mx-dataform-repo.name
  name           = "allied_group_plan_documents_data_workflow"
  release_config = google_dataform_repository_release_config.dataform_full_repo_release.id

  invocation_config {
    included_targets {
      database = var.gcp_project
      schema   = var.silver_dataset_id
      name     = "allied_gpf_groupplandocs"
    }

    transitive_dependencies_included = true
    transitive_dependents_included   = true
    service_account                  = "service-${var.gcp_project_number}@gcp-sa-dataform.iam.gserviceaccount.com"

  }

  cron_schedule = "0 21 * * *"
  time_zone     = "America/New_York"
}
