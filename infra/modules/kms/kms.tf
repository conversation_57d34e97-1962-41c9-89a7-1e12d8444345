resource "google_kms_key_ring" "bigquery_datawarehouse_keyring" {
  name     = format("%s", "${var.company}-${var.env}-bigquery-datawarehouse-global-key-ring")
  location = "global"
}

resource "google_kms_crypto_key" "bigquery_datawarehouse_key" {
  name            = format("%s", "${var.company}-${var.env}-bigquery-datawarehouse-global-key")
  key_ring        = google_kms_key_ring.bigquery_datawarehouse_keyring.id
  rotation_period = "7776000s" // 90 days

  lifecycle {
    prevent_destroy = false
  }
}

resource "google_kms_crypto_key_iam_member" "bq_kms_policy" {
  crypto_key_id = google_kms_crypto_key.bigquery_datawarehouse_key.id
  role          = "roles/cloudkms.cryptoKeyEncrypterDecrypter"
  member        = "serviceAccount:${var.bigquery_service_account_email}"
}
