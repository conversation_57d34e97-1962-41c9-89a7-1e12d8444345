data "google_project" "project" {
}

resource "google_bigquery_dataset" "mx-bigquery-dataset" {
  dataset_id    = "${var.company}_dbtlog_${var.env}"
  friendly_name = "${var.company}.dbtlog.${var.env}"
  description   = ""
  location      = "US"
}

resource "google_bigquery_dataset_iam_member" "mx-bigquery-data-editor" {
  dataset_id = "${var.company}_dbtlog_${var.env}"
  role       = "roles/bigquery.dataEditor"
  member     = "projectWriters"
}

resource "google_bigquery_dataset_iam_member" "mx-bigquery-data-viewer" {
  dataset_id = "${var.company}_dbtlog_${var.env}"
  role       = "roles/bigquery.dataViewer"
  member     = "projectReaders"
}

resource "google_bigquery_dataset_iam_member" "mx-bigquery-data-reader" {
  dataset_id = "${var.company}_dbtlog_${var.env}"
  role       = "roles/bigquery.dataOwner"
  member     = "projectOwners"
}

resource "google_logging_project_sink" "dbt-log" {
  name        = "dbt-log-bigquery-sink"
  destination = "bigquery.googleapis.com/projects/${var.gcp_project}/datasets/${google_bigquery_dataset.mx-bigquery-dataset.dataset_id}"

  filter = "resource.type =~ \"cloud_run*.\" resource.labels.service_name =~ \".*dbt.*\" logName=~\".*stdout\""

  unique_writer_identity = true

  bigquery_options {
    use_partitioned_tables = true
  }
}

resource "google_bigquery_dataset_iam_member" "dbt-log-sink-bigquery-data-editor" {
  depends_on = [google_logging_project_sink.dbt-log]
  dataset_id = google_bigquery_dataset.mx-bigquery-dataset.dataset_id
  role       = "roles/bigquery.dataEditor"
  member     = "serviceAccount:service-${data.google_project.project.number}@gcp-sa-logging.iam.gserviceaccount.com"
}

output "out_dbtlog" { value = google_bigquery_dataset.mx-bigquery-dataset }
