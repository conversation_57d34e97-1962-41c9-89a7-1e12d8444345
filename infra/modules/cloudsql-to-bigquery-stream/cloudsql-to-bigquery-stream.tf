resource "google_datastream_stream" "cloudsql-to-bigquery-stream-v2" {
  display_name  = "MX MySQL Database to BigQuery"
  location      = var.default_region
  stream_id     = "${var.company}-mysql-database-to-biquery-stream-v2"
  desired_state = "RUNNING"

  source_config {
    source_connection_profile = var.source_connection_profile
    mysql_source_config {
      include_objects {
        mysql_databases {
          database = "mx.fileprocessing"
          mysql_tables {
            table = "Accumulator"
          }
          mysql_tables {
            table = "Eligibility"
          }
          mysql_tables {
            table = "ClaimDetail"
          }
          mysql_tables {
            table = "ClaimHeader"
          }
          mysql_tables {
            table = "CptLookup"
          }
        }
        mysql_databases {
          database = "mx.insurance"
          mysql_tables {
            table = "Benefit"
          }
          mysql_tables {
            table = "BenefitServiceTypeProcedureCode"
          }
          mysql_tables {
            table = "BenefitTier"
          }
          mysql_tables {
            table = "Group"
          }
          mysql_tables {
            table = "Payer"
          }
          mysql_tables {
            table = "Plan"
          }
          mysql_tables {
            table = "Policy"
          }
          mysql_tables {
            table = "Document"
          }
          mysql_tables {
            table = "GroupDocument"
          }
          mysql_tables {
            table = "PlanDocument"
          }
          mysql_tables {
            table = "SapphireCostTransparencyPlan"
          }
          mysql_tables {
            table = "SapphirePlanTierNetwork"
          }
          mysql_tables {
            table = "SapphireProviderSearchNetwork"
          }
          mysql_tables {
            table = "PlanTierNetwork"
          }
          mysql_tables {
            table = "Network"
          }
          mysql_tables {
            table = "Concent"
          }
        }
        mysql_databases {
          database = "mx.authentication"
          mysql_tables {
            table = "LoginAttempt"
          }
          mysql_tables {
            table = "User"
          }
        }
        mysql_databases {
          database = "mx.partnerorganization"
          mysql_tables {
            table = "Partner"
          }
          mysql_tables {
            table = "PartnerGroup"
          }
          mysql_tables {
            table = "PartnerUser"
          }
        }
        mysql_databases {
          database = "mx.comdata"
          mysql_tables {
            table = "VirtualCard"
          }
        }
        mysql_databases {
          database = "mx.configuration"
          mysql_tables {
            table = "BrandConfig"
          }
          mysql_tables {
            table = "MemberContact"
          }
          mysql_tables {
            table = "ModuleConfig"
          }
          mysql_tables {
            table = "TerminologyConfig"
          }
          mysql_tables {
            table = "WebhookConfig"
          }
          mysql_tables {
            table = "Module"
          }
        }
        mysql_databases {
          database = "mx.identity"
          mysql_tables {
            table = "Person"
          }
          mysql_tables {
            table = "PersonRelationship"
          }
        }
        mysql_databases {
          database = "mx.partnerallied"
          mysql_tables {
            table = "ClaimLog"
          }
          mysql_tables {
            table = "ClaimLogDetail"
          }
          mysql_tables {
            table = "ExplicitConsent"
          }
          mysql_tables {
            table = "PaperlessEOBElection"
          }
          mysql_tables {
            table = "PersonToExternalId"
          }
        }
        mysql_databases {
          database = "mx.payment"
          mysql_tables {
            table = "HealthServiceRequestUpdateNotificationLog"
          }
          mysql_tables {
            table = "LobCheck"
          }
          mysql_tables {
            table = "LobCheckRequestStatus"
          }
          mysql_tables {
            table = "LobEvent"
          }
          mysql_tables {
            table = "LobTrackingEvent"
          }
          mysql_tables {
            table = "PaymentCard"
          }
          mysql_tables {
            table = "PaymentCardRequest"
          }
          mysql_tables {
            table = "PaymentCardRequestStatus"
          }
          mysql_tables {
            table = "PaymentCardStatus"
          }
          mysql_tables {
            table = "Transaction"
          }
          mysql_tables {
            table = "TransactionFee"
          }
          mysql_tables {
            table = "TransactionFeeGroup"
          }
        }
        mysql_databases {
          database = "mx.profile"
          mysql_tables {
            table = "Profile"
          }
        }
        mysql_databases {
          database = "mx.providersearch"
          mysql_tables {
            table = "InsuranceNetwork"
          }
          mysql_tables {
            table = "PlanInsurance"
          }
          mysql_tables {
            table = "Network"
          }
          mysql_tables {
            table = "PlanNetwork"
          }
          mysql_tables {
            table = "PlanTierNetwork"
          }
          mysql_tables {
            table = "PlanTierRibbonInsurance"
          }
        }
        mysql_databases {
          database = "mx.javelina"
          mysql_tables {
            table = "Accumulator"
          }
          mysql_tables {
            table = "ClaimDetail"
          }
          mysql_tables {
            table = "ClaimHeader"
          }
          mysql_tables {
            table = "Eligibility"
          }
        }
      }
    }
  }

  destination_config {
    destination_connection_profile = var.destination_connection_profile
    bigquery_destination_config {
      data_freshness = "10800s"
      single_target_dataset {
        dataset_id = "${var.gcp_project}:${var.dataset_id}"
      }
    }
  }

  backfill_all {}

}
