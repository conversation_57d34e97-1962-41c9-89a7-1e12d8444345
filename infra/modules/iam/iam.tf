data "google_compute_default_service_account" "default" {
}

resource "google_project_iam_member" "mx-cloudsqltobq-cdc-service-account-cloudsql-permission_v2" {
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:devops@${var.gcp_project}.iam.gserviceaccount.com"
  project = var.gcp_project
}

resource "google_project_iam_member" "mx-cloudstorageadmin-permission" {
  role    = "roles/storage.objectAdmin"
  member  = "serviceAccount:devops@${var.gcp_project}.iam.gserviceaccount.com"
  project = var.gcp_project
}

resource "google_project_iam_member" "mx-dataform-admin-permission" {
  role    = "roles/dataform.admin"
  member  = "serviceAccount:devops@${var.gcp_project}.iam.gserviceaccount.com"
  project = var.gcp_project
}

resource "google_project_iam_member" "bqadmin" {
  project = var.gcp_project
  role    = "roles/bigquery.admin"
  member  = "serviceAccount:devops@${var.gcp_project}.iam.gserviceaccount.com"
}

resource "google_project_iam_member" "mx-datastream-admin-permission" {
  role    = "roles/datastream.admin"
  member  = "serviceAccount:devops@${var.gcp_project}.iam.gserviceaccount.com"
  project = var.gcp_project
}

resource "google_project_iam_member" "mx-computenetwork-viewer-permission" {
  role    = "roles/compute.networkViewer"
  member  = "serviceAccount:devops@${var.gcp_project}.iam.gserviceaccount.com"
  project = var.gcp_project
}

resource "google_service_account" "mx-datawarehouse-service-account" {
  account_id   = "${var.company}-datawarehouse-${var.env}"
  display_name = "${var.company}-datawarehouse-${var.env}"
  project      = var.gcp_project
}

resource "google_project_iam_member" "datawarehouse_serviceacc_bqadmin" {
  project = var.gcp_project
  role    = "roles/bigquery.admin"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
}

resource "google_project_iam_member" "mx-service-account-run-invoker" {
  role    = "roles/run.invoker"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
  project = var.gcp_project
}

resource "google_project_iam_member" "mx-datawarehouse-service-account-logwriter-permission" {
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
  project = var.gcp_project
}

resource "google_project_iam_member" "mx-datawarehouse-service-account-cloudtrace-permission" {
  role    = "roles/cloudtrace.agent"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
  project = var.gcp_project
}

resource "google_project_iam_member" "mx-datawarehouse-service-account-cloudsql-permission" {
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
  project = var.gcp_project
}

resource "google_project_iam_member" "mx-datawarehouse-service-account-cloudstorage-permission" {
  role    = "roles/storage.objectUser"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
  project = var.gcp_project
}

resource "google_project_iam_member" "cloud_job_scheduler_permission" {
  project = var.gcp_project
  role    = "roles/cloudscheduler.jobRunner"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
}

resource "google_project_iam_member" "workflow_scheduler_permission" {
  project = var.gcp_project
  role    = "roles/workflows.admin"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
}

// This is needed for Sigma
resource "google_project_iam_member" "service_account_user" {
  project = var.gcp_project
  role    = "roles/iam.serviceAccountUser"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
}
// Give permission to the SFTP server to view and list files in this bucket
resource "google_storage_bucket_iam_member" "sftp-account-bucket-permission" {
  count  = var.company == "adig" ? 0 : 1
  bucket = var.allied_data_reconciliation_bucket_name
  role   = "roles/storage.objectViewer"
  member = "serviceAccount:<EMAIL>"
}

resource "google_project_iam_member" "cloud_run_secret_manager_secret_accessor" {
  project = var.gcp_project
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
}
resource "google_project_iam_member" "cloud_run_invoker" {
  project = var.gcp_project
  role    = "roles/run.invoker"
  member  = "serviceAccount:${google_service_account.mx-datawarehouse-service-account.email}"
}
