resource "google_cloud_run_v2_service" "mx_dataprocesing_batch_control" {
  name     = "${var.company}-dataprocessing-batch-control"
  location = var.region
  ingress  = "INGRESS_TRAFFIC_ALL"

  template {
    scaling {
      max_instance_count = 5
    }
    service_account = var.service_account_email
    containers {
      image = "gcr.io/${var.gcr_image_data_processing_batch_control}:${var.gcr_data_processing_batch_control_image_version}"

      resources {
        limits = {
          cpu    = "8"
          memory = "20000Mi"
        }
      }
      env {
        name  = "ENVIRONMENT_NAME"
        value = var.env
      }
      env {
        name  = "NAMESPACE"
        value = var.namespace
      }
      env {
        name  = "PROJECT_ID"
        value = var.gcp_project
      }
      env {
        name  = "DD_ENV"
        value = var.env
      }
      env {
        name  = "DD_SERVICE"
        value = "dbt-monthly-run"
      }
      env {
        name  = "DD_VERSION"
        value = var.gcr_data_processing_batch_control_image_version
      }
      env {
        name  = "DD_LOG_LEVEL"
        value = "ERROR"
      }
      env {
        name = "DD_API_KEY"
        value_source {
          secret_key_ref {
            secret  = var.datadog_api_key_secret_id
            version = "latest"
          }
        }
      }
      dynamic "env" {
        for_each = var.batch_control_enable_data_dog ? [] : [1]
        content {
          name  = "DD_TRACE_SAMPLE_RATE"
          value = "0"
        }
      }
      env {
        name  = "DD_SITE"
        value = var.datadog_site
      }
      env {
        name  = "DD_LOGS_ENABLED"
        value = "true"
      }
      env {
        name  = "DD_LOGS_INJECTION"
        value = "true"
      }
      env {
        name  = "DD_PLUGIN_ENABLED"
        value = "true"
      }
    }
  }
}
resource "google_cloud_run_v2_service" "mx_dbt_wrapper" {
  name     = "${var.company}-dbt-wrapper"
  location = var.region
  ingress  = "INGRESS_TRAFFIC_ALL"

  template {
    scaling {
      max_instance_count = 5
    }
    service_account = var.service_account_email
    containers {
      image = "gcr.io/${var.gcr_image_dbt_wrapper}:${var.gcr_dbt_wrapper_image_version}"

      resources {
        limits = {
          cpu    = "8"
          memory = "20000Mi"
        }
      }
      env {
        name  = "ENVIRONMENT_NAME"
        value = var.env
      }
      env {
        name  = "NAMESPACE"
        value = var.namespace
      }
      env {
        name  = "PROJECT_ID"
        value = var.gcp_project
      }
      env {
        name  = "DD_ENV"
        value = var.env
      }
      env {
        name  = "DD_SERVICE"
        value = "dbt-monthly-run"
      }
      env {
        name  = "DD_VERSION"
        value = var.gcr_image_dbt_wrapper
      }
      env {
        name  = "DD_LOG_LEVEL"
        value = "ERROR"
      }
      env {
        name = "DD_API_KEY"
        value_source {
          secret_key_ref {
            secret  = var.datadog_api_key_secret_id
            version = "latest"
          }
        }
      }
      dynamic "env" {
        for_each = var.batch_control_enable_data_dog ? [] : [1]
        content {
          name  = "DD_TRACE_SAMPLE_RATE"
          value = "0"
        }
      }
      env {
        name  = "DD_SITE"
        value = var.datadog_site
      }
      env {
        name  = "DD_LOGS_ENABLED"
        value = "true"
      }
      env {
        name  = "DD_LOGS_INJECTION"
        value = "true"
      }
      env {
        name  = "DD_PLUGIN_ENABLED"
        value = "true"
      }
    }
  }
}

resource "google_cloud_run_v2_service" "mx_dbt_documentation" {
  name     = "${var.company}-dbt-document"
  location = var.region
  ingress  = "INGRESS_TRAFFIC_ALL"

  template {
    scaling {
      max_instance_count = 1
    }
    service_account = var.service_account_email
    containers {
      image = "gcr.io/${var.gcr_image_dbt_documentation}:${var.gcr_dbt_documentation_image_version}"

      resources {
        limits = {
          cpu    = "8"
          memory = "20000Mi"
        }
      }
      env {
        name  = "ENVIRONMENT_NAME"
        value = var.env
      }
      env {
        name  = "NAMESPACE"
        value = var.namespace
      }
      env {
        name  = "PROJECT_ID"
        value = var.gcp_project
      }
      env {
        name  = "DD_ENV"
        value = var.env
      }
      env {
        name  = "DD_SERVICE"
        value = "dbt-monthly-run"
      }
      env {
        name  = "DD_VERSION"
        value = var.gcr_image_dbt_documentation
      }
      env {
        name  = "DD_LOG_LEVEL"
        value = "ERROR"
      }
      env {
        name = "DD_API_KEY"
        value_source {
          secret_key_ref {
            secret  = var.datadog_api_key_secret_id
            version = "latest"
          }
        }
      }
      dynamic "env" {
        for_each = var.batch_control_enable_data_dog ? [] : [1]
        content {
          name  = "DD_TRACE_SAMPLE_RATE"
          value = "0"
        }
      }
      env {
        name  = "DD_SITE"
        value = var.datadog_site
      }
      env {
        name  = "DD_LOGS_ENABLED"
        value = "true"
      }
      env {
        name  = "DD_LOGS_INJECTION"
        value = "true"
      }
      env {
        name  = "DD_PLUGIN_ENABLED"
        value = "true"
      }
    }
  }
}