variable "region" {
  default = ""
}
variable "company" {
}
variable "gcp_project" {
  default = ""
}
variable "env" {
  default = ""
}
variable "gcr_image_data_processing_batch_control" {
  default = ""
}
variable "gcr_data_processing_batch_control_image_version" {
  default = ""
}
variable "gcr_image_dbt_wrapper" {
  default = ""
}
variable "gcr_dbt_wrapper_image_version" {
  default = ""
}
variable "gcr_image_dbt_documentation" {
  default = ""
}
variable "gcr_dbt_documentation_image_version" {
  default = ""
}
variable "batch_control_enable_data_dog" {
  default = true
}
variable "datadog_site" {
  default = ""
}
variable "datadog_api_key_secret_id" {
  default = ""
}
variable "service_account_email" {
  default = ""
}
variable "namespace" {
}
