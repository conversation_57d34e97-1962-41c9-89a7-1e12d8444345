resource "google_datastream_connection_profile" "source_connection_profile_v2" {
  display_name          = "Mx Database source connection profile"
  location              = var.region
  connection_profile_id = "${var.company}-database-source-profile"

  mysql_profile {
    hostname = var.mysql_host_ip
    username = var.db_username
    password = var.db_password
  }
  private_connectivity {
    private_connection = google_datastream_private_connection.mx-datastream-private-connection.id
  }
}
resource "google_datastream_connection_profile" "destination_connection_profile" {
  display_name          = "Mx BigQuery destination profile"
  location              = var.region
  connection_profile_id = "${var.company}-bigquery-destination-profile"

  bigquery_profile {}

  private_connectivity {
    private_connection = google_datastream_private_connection.mx-datastream-private-connection.id
  }
}
resource "google_datastream_private_connection" "mx-datastream-private-connection" {
  display_name          = "${var.company}-common-${var.env}-datastream-private-connection"
  private_connection_id = "${var.company}-common-${var.env}-ds-private-conn"
  vpc_peering_config {
    vpc    = var.vpc_id
    subnet = var.ds_private_conn_subnet
  }
  location = var.region
}
