variable "region" {
  default = ""
}
variable "gcp_project" {
  default = ""
}
variable "data_warehouse_job_name" {
  default = ""
}
variable "datamart_job_name" {
  default = ""
}
variable "datamart_dimension_job_name" {
  default = ""
}
variable "datamart_fact_job_name" {
  default = ""
}
variable "dm_schedule" {
  default = ""
}
variable "dw_schedule" {
  default = ""
}
variable "time_zone" {
  default = ""
}
variable "service_account_email" {
  default = ""
}
variable "service_account_id" {
  default = ""
}
variable "batch_control_service_uri" {
  default = ""
}
variable "dbt_wrapper_service_uri" {
  default = ""  
}