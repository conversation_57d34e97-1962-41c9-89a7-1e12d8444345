resource "google_workflows_workflow" "datawarehouse_workflow" {
  name        = "datawarehouse_workflow"
  region      = "${var.region}"
  description = "Workflow to process the data warehouse"
  service_account = var.service_account_id
  # Note that $$ is needed for Terraform
  source_contents = <<EOF
  main:
      params: [event]
      steps:
          - init:
              assign:
                  - batch_control_service_url: "${var.batch_control_service_uri}"
                  - dbt_wrapper_service_url: "${var.dbt_wrapper_service_uri}"
          - get_dw_batch_status:
              call: http.get
              args:
                url: $${batch_control_service_url + "/get_latest_batch_execution_date/MxDataWarehouseDailyBuild/batchtype/Daily" }
                timeout: 1800
                auth:
                  type: OIDC
                  audience: $${batch_control_service_url}
              result: dw_batch_execution
          - warm_up_dbt_wrapper:
              call: http.get
              args:
                url: $${dbt_wrapper_service_url}
                timeout: 1800
                auth:
                  type: OIDC
              result: warm_up_dbt_wrapper_result
          - build_datawarehouse:
              call: http.post
              args:
                url: $${dbt_wrapper_service_url + "/dbt_build"}
                auth:
                  type: OIDC
                  audience: $${dbt_wrapper_service_url}
                timeout: 1800
                body:
                  select_tag: "tag:MxDataWarehouseDailyBuild"
                  batch_control_id: "MxDataWarehouseDailyBuild"
                  batch_control_dt: ""
              result: build_datawarehouse_result
          - finish:
              return:
                data_warehouse_job_result: $${build_datawarehouse_result}
  EOF
}

resource "google_cloud_scheduler_job" "datawarehouse_workflow_scheduler" {
  name      = "datawarehouse_workflow_schedule"
  schedule  = var.dw_schedule
  time_zone = var.time_zone

  retry_config {
    retry_count = 1
  }

  http_target {
    http_method = "POST"
    uri         = "https://workflowexecutions.googleapis.com/v1/projects/${var.gcp_project}/locations/${var.region}/workflows/${google_workflows_workflow.datawarehouse_workflow.name}/executions"
    body        = base64encode("")


    headers = {
      "User-Agent"   = "Google-Cloud-Scheduler"
      "Content-Type" = "application/json",
      "schedule"     = "daily"
    }

    oauth_token {
      service_account_email = var.service_account_email
    }
  }
}

resource "google_workflows_workflow" "datamart_workflow" {
  name        = "datamart_workflow"
  region      = "${var.region}"
  description = "Workflow to process the datamart jobs"
  service_account = var.service_account_id
  # Note that $$ is needed for Terraform
  source_contents = <<EOF
  main:
      params: [event]
      steps:
          - init:
              assign:
                  - batch_control_service_url: "${var.batch_control_service_uri}"
                  - dbt_wrapper_service_url: "${var.dbt_wrapper_service_uri}"
          - warm_up_dbt_wrapper:
              call: http.get
              args:
                url: $${dbt_wrapper_service_url}
                timeout: 1800
                auth:
                  type: OIDC
              result: warm_up_dbt_wrapper_result
          - get_does_dm_batch_need_to_run:
              call: http.get
              args:
                url: $${batch_control_service_url + "/does_batch_need_to_run/MxDataMartDailyBuild/batchtype/Daily" }
                auth:
                  type: OIDC
                  audience: $${batch_control_service_url}
              result: get_does_dm_batch_need_to_run_result
          - check_if_dm_needs_to_be_run:
              switch:
                - condition : $${get_does_dm_batch_need_to_run_result.body.run_job == True}
                  next: build_common
              next: finish
          - build_common:
              call: http.post
              args:
                url: $${dbt_wrapper_service_url + "/dbt_build"}
                auth:
                  type: OIDC
                  audience: $${dbt_wrapper_service_url}
                timeout: 1800
                body:
                  select_tag: "+models/silver/common"
                  exclude_tag: "decouple_from_datamart"
                  batch_control_id: "MxDataMartDailyBuild"
                  batch_control_dt: $${get_does_dm_batch_need_to_run_result.body.next_execution_date}
              result: build_common_result
          - check_if_common_build_was_success:
              switch:
                - condition: $${build_common_result.code == 200}
                  next: build_intermediate
              next: finish
          - build_intermediate:
              call: http.post
              args:
                url: $${dbt_wrapper_service_url + "/dbt_build"}
                auth:
                  type: OIDC
                  audience: $${dbt_wrapper_service_url}
                timeout: 1800
                body:
                  select_tag: "+models/intermediate/"
                  exclude_tag: "exclude_when_incremental,decouple_from_datamart"
                  batch_control_id: "MxDataMartDailyBuild"
                  batch_control_dt: $${get_does_dm_batch_need_to_run_result.body.next_execution_date}
              result: build_intermediate_result
          - check_if_intermediate_build_was_success:
              switch:
                - condition: $${build_intermediate_result.code == 200}
                  next: build_facts
              next: finish
          - build_facts:
              call: http.post
              args:
                url: $${dbt_wrapper_service_url + "/dbt_build"}
                auth:
                  type: OIDC
                  audience: $${dbt_wrapper_service_url}
                timeout: 1800
                body:
                  select_tag: "models/silver/member"
                  batch_control_id: "MxDataMartDailyBuild"
                  batch_control_dt: $${get_does_dm_batch_need_to_run_result.body.next_execution_date}
              result: build_fact_result
          - check_if_facts_build_was_success:
              switch:
                - condition: $${build_fact_result.code == 200}
                  next: build_gold
              next: finish
          - build_gold:
              call: http.post
              args:
                url: $${dbt_wrapper_service_url + "/dbt_build"}
                auth:
                  type: OIDC
                  audience: $${dbt_wrapper_service_url}
                timeout: 1800
                body:
                  select_tag: "models/gold/daily"
                  batch_control_id: "MxDataMartDailyBuild"
                  batch_control_dt: $${get_does_dm_batch_need_to_run_result.body.next_execution_date}
              result: build_gold_result
          - check_if_gold_build_was_success:
              switch:
                - condition: $${build_gold_result.code == 200}
                  next: update_dm_batch_status
              next: finish
          - update_dm_batch_status:
              call: http.post
              args:
                url: $${batch_control_service_url + "/increment_batch_execution_date"}
                auth:
                  type: OIDC
                  audience: $${batch_control_service_url}
                body:
                  batch_control_id: "MxDataMartDailyBuild"
                  batch_type: "Daily"
              result: update_dw_batch_result
          - finish:
              return:
                data_warehouse_job_result: $${update_dw_batch_result}
  EOF
}

resource "google_cloud_scheduler_job" "datamart_workflow_scheduler" {
  name      = "datamart_workflow_schedule"
  schedule  = var.dm_schedule
  time_zone = var.time_zone

  retry_config {
    retry_count = 1
  }

  http_target {
    http_method = "POST"
    uri         = "https://workflowexecutions.googleapis.com/v1/projects/${var.gcp_project}/locations/${var.region}/workflows/${google_workflows_workflow.datamart_workflow.name}/executions"
    body        = base64encode("")


    headers = {
      "User-Agent"   = "Google-Cloud-Scheduler"
      "Content-Type" = "application/json",
      "schedule"     = "daily"
    }

    oauth_token {
      service_account_email = var.service_account_email
    }
  }
}

resource "google_cloud_scheduler_job" "dbt_monday_run" {
  
  name      = "dbt_monday_run"
  schedule  = "0 9 * * 1" # Every Monday at 9:00 AM
  time_zone = var.time_zone

  retry_config {
    retry_count = 5
  }

  http_target {
    http_method = "POST"
    uri         = "${var.dbt_wrapper_service_uri}/dbt_build"
    body        = base64encode("{\"select_tag\": \"models/gold/monday\",\"batch_control_id\": \"MxDataWarehouseDailyBuild_Monday\",\"batch_control_dt\":\"\"}")


    headers = {
      "User-Agent"   = "Google-Cloud-Scheduler"
      "Content-Type" = "application/json",
      "schedule"     = "daily"
    }

    oidc_token {
      service_account_email = var.service_account_email
    }
  }
}

resource "google_cloud_scheduler_job" "dbt_sixth_of_month_run" {
  
  name      = "dbt_sixth_of_month_run"
  schedule  = "0 3 6 * *" # Every 6th of the month at 3:00 AM
  time_zone = var.time_zone

  retry_config {
    retry_count = 5
  }

  http_target {
    http_method = "POST"
    uri         = "${var.dbt_wrapper_service_uri}/dbt_build"
    body        = base64encode("{\"select_tag\": \"models/gold/Allstate_Registrations\",\"batch_control_id\": \"MxDataWarehouseDailyBuild_6Th\",\"batch_control_dt\":\"\"}")


    headers = {
      "User-Agent"   = "Google-Cloud-Scheduler"
      "Content-Type" = "application/json",
      "schedule"     = "daily"
    }

    oidc_token {
      service_account_email = var.service_account_email
    }
  }
}