data "google_client_config" "current" {}

provider "google" {
  project = var.gcp_project
  region  = var.default_region
}

locals {
  enable_data_dog = var.env == "prod" ? true : false
}

data "google_bigquery_default_service_account" "default_bigquery_service_account" {
}

data "google_project" "mx_project" {
}

data "google_compute_network" "vpc" {
  name = format("%s", "${var.company}-${var.env}-vpc")
}

data "google_compute_instance" "sql_auth_proxy_host" {
  name = "${var.company}-sql-auth-proxy-host-master"
  zone = var.cloudsql_master_zone
}

module "kms" {
  source                         = "./modules/kms"
  company                        = var.company
  env                            = var.env
  bigquery_service_account_email = data.google_bigquery_default_service_account.default_bigquery_service_account.email
}

module "iam" {
  source                                 = "./modules/iam"
  gcp_project                            = var.gcp_project
  company                                = var.company
  env                                    = var.env
  allied_data_reconciliation_bucket_name = google_storage_bucket.allied_data_reconciliation.name
}

module "datastream" {
  source                 = "./modules/data-stream"
  region                 = var.default_region
  db_username            = var.db_username
  db_password            = var.db_password
  company                = var.company
  env                    = var.env
  ds_private_conn_subnet = var.ds_private_conn_subnet
  vpc_id                 = data.google_compute_network.vpc.id
  mysql_host_ip          = data.google_compute_instance.sql_auth_proxy_host.network_interface.0.network_ip
}

module "ods_v2" {
  source        = "./modules/bigquery-dataset"
  dataset_id    = "ODS"
  description   = "This is a landing zone for all data from MySQL"
  location      = "US"
  friendly_name = "OperationalDataStore"
  kms_key_name  = module.kms.out_key_id
  user_by_email = module.iam.service_account_email
}

module "silver" {
  source        = "./modules/bigquery-dataset"
  dataset_id    = "Silver"
  description   = "This is a dataset that contains the data mart for reporting"
  location      = "US"
  friendly_name = "Silver Layer"
  kms_key_name  = module.kms.out_key_id
  user_by_email = module.iam.service_account_email
}

module "gold" {
  source        = "./modules/bigquery-dataset"
  dataset_id    = "Gold"
  description   = "This dataset contains data that can be directly used for reporting in Sigma.It's plug and play"
  location      = "US"
  friendly_name = "Gold Layer"
  kms_key_name  = module.kms.out_key_id
  user_by_email = module.iam.service_account_email
}

module "silver_layer" {
  source        = "./modules/bigquery-dataset"
  dataset_id    = "Silver_Zone"
  description   = "This is a dataset for housing all the dataform generated assets such as views and tables etc. It should only have cleaned and QAed data"
  location      = "US"
  friendly_name = "Silver Zone"
  kms_key_name  = module.kms.out_key_id
  user_by_email = module.iam.service_account_email
}

module "assertions" {
  source        = "./modules/bigquery-dataset"
  dataset_id    = "assertions"
  description   = "This is a dataset for housing all the assertions that go into any dataform pipeline"
  location      = "US"
  friendly_name = "assertions"
  kms_key_name  = module.kms.out_key_id
  user_by_email = module.iam.service_account_email
}

module "warehouse" {
  source        = "./modules/bigquery-dataset"
  dataset_id    = "Warehouse"
  description   = "This layer stores all incremental changes to the source data model"
  location      = "US"
  friendly_name = "Warehouse"
  kms_key_name  = module.kms.out_key_id
  user_by_email = module.iam.service_account_email
}

resource "google_bigquery_table" "batch_control" {
  dataset_id = module.warehouse.dataset_id
  table_id   = "T_Batch_Control"
  encryption_configuration {
    kms_key_name = module.kms.out_key_id
  }
  schema = jsonencode(
    [
      {
        "name" : "Batch_Id",
        "mode" : "REQUIRED",
        "type" : "STRING"
      },
      {
        "name" : "Batch_Type",
        "mode" : "REQUIRED",
        "type" : "STRING"
      },
      {
        "name" : "Batch_Control_Dt",
        "mode" : "REQUIRED",
        "type" : "DATE"
      }
  ])
}

module "cloudsql-to-bigquery-stream" {
  source                         = "./modules/cloudsql-to-bigquery-stream"
  gcp_project                    = var.gcp_project
  default_region                 = var.default_region
  source_connection_profile      = module.datastream.out_source_connection_profile_id
  destination_connection_profile = module.datastream.out_destination_connection_profile_id
  dataset_id                     = module.ods_v2.dataset_id
  company                        = var.company
}

module "dataform" {
  source               = "./modules/dataform"
  gcp_project          = var.gcp_project
  gcp_project_number   = data.google_project.mx_project.number
  silver_dataset_id    = module.silver_layer.dataset_id
  assertion_dataset_id = module.assertions.dataset_id
}

resource "google_storage_bucket" "allied_data_reconciliation" {
  name          = "${var.company}-allied-data-reconciliation-${var.env}"
  location      = "US"
  force_destroy = true
}


resource "google_cloud_run_v2_job" "export_allied_data_to_gcs" {
  name     = "export-allied-data-to-gcs"
  location = var.default_region

  template {
    template {
      containers {
        image = "gcr.io/${var.gcr_image}:${var.gcr_image_version}"

        resources {
          limits = {
            cpu    = "8"
            memory = "20000Mi"
          }
        }

        env {
          name  = "GCS_BUCKET_ALLIED"
          value = google_storage_bucket.allied_data_reconciliation.name
        }
        env {
          name  = "BQ_DATASET_ID"
          value = module.silver_layer.dataset_id
        }
        env {
          name  = "BQ_ELIG_TABLE_ID"
          value = "allied_member_benefits"
        }
        env {
          name  = "BQ_CLAIMS_TABLE_ID"
          value = "allied_orphaned_claims"
        }
        env {
          name  = "BQ_ACCUMS_TABLE_ID"
          value = "allied_orphaned_accums"
        }
        env {
          name  = "BQ_GPF_GROUPSTONETWORKS_TABLE_ID"
          value = "allied_gpf_groups_benefits_networks"
        }
        env {
          name  = "BQ_GPF_GROUPSTOMODULES_TABLE_ID"
          value = "allied_gpf_groups_modules"
        }
        env {
          name  = "BQ_GPF_GROUPSANDPLAN_DOCUMENTS_TABLE_ID"
          value = "allied_gpf_groupplandocs"
        }
        env {
          name  = "PROJECT_ID"
          value = var.gcp_project
        }


      }
      service_account = module.iam.service_account_email
      timeout         = "7200s"
    }
  }

  lifecycle {
    ignore_changes = [
      launch_stage,
    ]
  }

}

module "export_allied_data_to_GCS_scheduler" {
  source                = "./modules/job-scheduler"
  gcp_project           = var.gcp_project
  service_account_email = module.iam.service_account_email
  name                  = "export_allied_data_to_GCS_scheduler_scheduler"
  schedule              = "30 23 * * *"
  time_zone             = "America/New_York"
  runner_name           = google_cloud_run_v2_job.export_allied_data_to_gcs.name
  region                = var.default_region
}

module "data_warehouse_workflow" {
  source                    = "./modules/dataprocessing-workflow"
  region                    = var.default_region
  gcp_project               = var.gcp_project
  dm_schedule               = "0 1 * * *"
  dw_schedule               = "0 */3 * * *"
  time_zone                 = "UTC"
  service_account_email     = module.iam.service_account_email
  service_account_id        = module.iam.service_account_id
  batch_control_service_uri = module.data_processing_jobs.batch_control_service_uri
  dbt_wrapper_service_uri   = module.data_processing_jobs.dbt_wrapper_service_uri
}

module "data_processing_jobs" {
  source                                          = "./modules/dataprocessing-jobs"
  region                                          = var.default_region
  company                                         = var.company
  gcp_project                                     = var.gcp_project
  env                                             = var.env
  gcr_image_data_processing_batch_control         = var.gcr_image_data_processing_batch_control
  gcr_data_processing_batch_control_image_version = var.gcr_image_version
  datadog_site                                    = var.datadog_site
  datadog_api_key_secret_id                       = google_secret_manager_secret.datadog_api_key.secret_id
  service_account_email                           = module.iam.service_account_email
  gcr_dbt_wrapper_image_version                   = var.gcr_image_version
  gcr_image_dbt_wrapper                           = var.gcr_image_dbt_wrapper
  gcr_image_dbt_documentation                     = var.gcr_image_dbt_documentation
  gcr_dbt_documentation_image_version             = var.gcr_image_version
  namespace                                       = var.namespace
}


module "bigquery_log" {
  source      = "./modules/bigquery-log"
  gcp_project = var.gcp_project
  env         = var.env
  company     = var.company
}

resource "google_project_service" "workflows_api" {
  project = var.gcp_project
  service = "workflows.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy         = false
}

resource "google_project_service" "datastream_api" {
  project = var.gcp_project
  service = "datastream.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy         = false
}

