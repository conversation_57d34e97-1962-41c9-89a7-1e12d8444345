# Creates DataDog API Key
resource "google_secret_manager_secret" "datadog_api_key" {
  depends_on = [google_kms_crypto_key_iam_member.kms-secret-manager-binding]
  secret_id  = "${var.company}-datawarehouse-datadog-api-key-${var.env}"

  replication {
    auto {
      customer_managed_encryption {
        kms_key_name = module.kms.out_key_id
      }
    }
  }
}

resource "google_secret_manager_secret_version" "datadog_api_key" {
  secret      = google_secret_manager_secret.datadog_api_key.id
  secret_data = var.datadog_api_key
}

# Enable SecretManager Service Identity
resource "google_project_service_identity" "secretmanager" {
  provider = google-beta
  service  = "secretmanager.googleapis.com"
  project  = var.gcp_project
}

resource "google_kms_crypto_key_iam_member" "kms-secret-manager-binding" {
  crypto_key_id = module.kms.out_key_id
  role          = "roles/cloudkms.cryptoKeyEncrypterDecrypter"
  member        = "serviceAccount:${google_project_service_identity.secretmanager.email}"
}

resource "google_secret_manager_secret_iam_member" "datadog_api_key" {
  project   = var.gcp_project
  secret_id = google_secret_manager_secret.datadog_api_key.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${module.iam.service_account_email}"
}
