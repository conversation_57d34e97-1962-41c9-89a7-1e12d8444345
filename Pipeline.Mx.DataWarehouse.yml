trigger:
  - master

pool:
  vmImage: "ubuntu-latest"

parameters:
  - name: secureFiles
    type: object
    default:
      - secureFileName: "allieddigital-uat-dw-key.json"
        stepName: alliedDigitalUatKey
      - secureFileName: "allieddigital-prod-dw-key.json"
        stepName: alliedDigitalProdKey
      - secureFileName: "allieddigital-dev-dw-key.json"
        stepName: alliedDigitalDevKey

variables:
  - group: Mx.DataWarehouse
  - group: GlobalPipelineVariables
  - group: Environment.AlliedDigital.Dev
  - group: Environment.AlliedDigital.UAT
  - group: Environment.AlliedDigital.Prod
  

steps:
  - task: PublishBuildArtifacts@1
    inputs:
      PathtoPublish: "$(System.DefaultWorkingDirectory)/infra"
      ArtifactName: "Mx.DataWarehouse.Infra"
      publishLocation: "Container"
    displayName: "Publish Terraform Artifacts"

  - task: DownloadSecureFile@1
    name: gcp_cert
    displayName: "Download GCP Service Account Certificate"
    inputs:
      secureFile: "gcp_allieddigital_uat_service_account_creds.json"

  - script: |
      sudo apt-get install unzip
      wget https://releases.hashicorp.com/terraform/1.9.8/terraform_1.9.8_linux_amd64.zip
      unzip terraform_1.9.8_linux_amd64.zip
      rm terraform_1.9.8_linux_amd64.zip
      sudo mv terraform /usr/local/bin/
    failOnStderr: false
    workingDirectory: "$(System.DefaultWorkingDirectory)/infra"

    displayName: "Install terraform"

  - script: |
      export GOOGLE_APPLICATION_CREDENTIALS="$(gcp_cert.secureFilePath)"

      terraform init -backend-config="bucket=allieddigital-terraform-state-uat"
      terraform validate
      terraform plan -lock=False -var-file=./environments/variables.allieddigital-uat.tfvars 

      unset GOOGLE_APPLICATION_CREDENTIALS
    failOnStderr: true
    workingDirectory: "$(System.DefaultWorkingDirectory)/infra"
    displayName: "Validate Terraform"

  - ${{each secureFile in parameters.secureFiles}}:
      - template: templates/secure-files-template.yml
        parameters:
          secureFileName: ${{secureFile.secureFileName}}
          stepName: ${{secureFile.stepName}}

  - task: Docker@2
    displayName: "data_pipelines build and publish"
    inputs:
      containerRegistry: "GCR-Artifacts"
      repository: "$(Gcr.AlliedDigital.Image)"
      command: "buildAndPush"
      Dockerfile: "**/src/data_pipelines/dockerfile"
      tags: "$(Build.BuildNumber)"

  - task: Docker@2
    displayName: "Data Transformation Batch Control API"
    inputs:
      buildContext: "$(Build.Repository.LocalPath)"
      containerRegistry: "GCR-Artifacts"
      repository: "$(Gcr.AlliedDigital.DataTxBatchControlApi)"
      command: "buildAndPush"
      Dockerfile: "**/src/data_transformation_batch_control/dockerfile"
      tags: "$(Build.BuildNumber)"

  - task: Docker@2
    displayName: "DBT Wrapper API"
    inputs:
      buildContext: "$(Build.Repository.LocalPath)"
      containerRegistry: "GCR-Artifacts"
      repository: "$(Gcr.AlliedDigital.DbtWrapperApi)"
      command: "buildAndPush"
      Dockerfile: "**/src/dbt_wrapper/dockerfile"
      tags: "$(Build.BuildNumber)"

  - task: Docker@2
    displayName: "DBT Documents"
    inputs:
      buildContext: "$(Build.Repository.LocalPath)"
      containerRegistry: "GCR-Artifacts"
      repository: "$(Gcr.AlliedDigital.DbtDocumentation)"
      command: "buildAndPush"
      Dockerfile: "**/src/dbt_documentation/dockerfile"
      tags: "$(Build.BuildNumber)"