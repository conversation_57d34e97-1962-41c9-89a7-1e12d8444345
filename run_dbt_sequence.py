import os
from datetime import datetime, timedelta
import time

def get_auth_token():
    stream = os.popen('gcloud auth print-identity-token')
    return stream.read().strip()

def update_batch_control(client, date_str):
    query = f"""
    update `medxoom-prod.Warehouse.T_Batch_Control`
    set Batch_Control_Dt = date('{date_str}')
    where batch_id = 'MxDataMartDailyBuild'
    """
    
    job = client.query(query)
    job.result() 

def run_commands(client , date_str):
    dbt_url = "https://mx-dbt-wrapper-88322699222.us-west1.run.app/dbt_build"
    batch_url = "https://mx-batch-control-88322699222.us-west1.run.app/increment_batch_execution_date"
    auth_token = get_auth_token()
    
    # First command - Update batch control
    print("Updating batch control...")
    update_batch_control(client, date_str)    

    print(f"\nUpdating batch control for date: {date_str}")
    batch_command = f'curl -d \'{batch_data}\' -H "Content-Type: application/json" -H "Authorization: Bearer {auth_token}" -X POST {batch_url}'
    batch_result = os.system(batch_command)
    
    if batch_result == 0:
        # Second command - DBT build
        dbt_data = f'{{"select_tag": "D_Module_Config D_Event D_Access_Mode D_Event_Region","batch_control_id": "Custom_Run","batch_control_dt":"{date_str}"}}'
        
        print(f"Running DBT build for date: {date_str}")
        dbt_command = f'curl -d \'{dbt_data}\' -H "Content-Type: application/json" -H "Authorization: Bearer {auth_token}" -X POST {dbt_url}'
        dbt_result = os.system(dbt_command)
        
        if dbt_result == 0:
            print(f"Successfully completed processing for date: {date_str}")
            return True
        else:
            print(f"DBT build failed for date: {date_str}")
            return False
    else:
        print(f"Failed to update batch control for date: {date_str}")
        return False

def main():

    # Initialize BigQuery client
    credentials = service_account.Credentials.from_service_account_file(
        '/Users/<USER>/Medxoom/medxoom-prod-37b489489944.json'  # Replace with your service account key path
    )
    
    client = bigquery.Client(
        credentials=credentials,
        project='medxoom-prod'
    )

    

    start_date = datetime(2024, 12, 19)
    end_date = datetime(2025, 4, 29)
    current_date = start_date
    
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        
        # Run commands and wait for completion
        success = run_commands(client , date_str)
        
        if success:
            current_date += timedelta(days=1)
            # Add a small delay between iterations to ensure API stability
            time.sleep(2)
        else:
            print(f"Stopping execution due to failure on date: {date_str}")
            break

if __name__ == "__main__":
    main()