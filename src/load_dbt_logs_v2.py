import os
import json
import subprocess
from google.cloud import bigquery
from google.cloud import storage
from google.oauth2 import service_account
import re

# Configuration
BQ_PROJECT_ID = "medxoom-dev-fb5"
BQ_DATASET_ID = "Warehouse"
BQ_TABLE_ID = "dbt_logs_combined"
SERVICE_ACCOUNT_FILE = "C:\MxFiles_temp\data\medxoom-dev-fb5-efec69e84c6d.json"
# Configuration
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Parent of 'src/'
ANALYTICS_DIR = os.path.join(BASE_DIR, "Analytics")
DBT_COMMAND = 'dbt build --select +models/silver/models/Dimensions --exclude tag:decouple_from_datamart --target dev_FB5 --log-format json'
LOGS_PATH = os.path.join(ANALYTICS_DIR, "logs")
LOG_FILE = os.path.join(LOGS_PATH, "combined_logs.json")
DBT_STDOUT_LOG = os.path.join(LOGS_PATH, "dbt_stdout.log")
GCS_BUCKET_NAME = "dbt-dm-logs"
GCS_OBJECT_NAME = "dbt_logs/combined_logs.json"

def get_storage_client():
    """
    Creates a Google Cloud Storage client using a service account JSON file.
    """
    credentials = service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE)
    return storage.Client(credentials=credentials, project=BQ_PROJECT_ID)

def extract_row_count(status_string):
    """
    Extracts the number of rows from a status string like:
    'MERGE (31.2k rows, 100.8 MiB processed)'.

    Args:
        status_string (str): The status string to parse.

    Returns:
        int: The number of rows as an integer.
    """
    try:

        match = re.search(r"([\d\.]+[kK]?)\s+rows", status_string)
        if match:
            rows = match.group(1)
            # Handle 'k' or 'K' for thousands
            if 'k' in rows.lower():
                return int(float(rows.lower().replace('k', '')) * 1000)
            return int(rows)
        
    except Exception as e:

        return 0

def get_bigquery_client():
    """
    Creates a BigQuery client using a service account JSON file.
    """
    credentials = service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE)
    return bigquery.Client(credentials=credentials, project=BQ_PROJECT_ID)

def create_table_if_not_exists(client):
    """
    Creates the BigQuery table if it does not exist.
    """
    table_id = f"{BQ_PROJECT_ID}.{BQ_DATASET_ID}.{BQ_TABLE_ID}"
    schema = [
        bigquery.SchemaField("model_name", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("node_status", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("start_time", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("end_time", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("schema", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("row_count", "STRING", mode="NULLABLE"),
    ]

    try:
        client.get_table(table_id)
        print(f"Table {table_id} already exists.")
    except Exception:
        table = bigquery.Table(table_id, schema=schema)
        client.create_table(table)
        print(f"Table {table_id} created.")

def extract_and_combine_logs():
    """
    Reads the LOG_FILE and extracts the required fields from relevant JSON lines.
    Creates one row for each occurrence of 'node_info' and relevant fields.
    """
    rows = []

    with open(LOG_FILE, "r") as file:
        for line in file:
            try:
                log_entry = json.loads(line)

                # Extract values from JSON if available
                data = log_entry.get("data", {})
                node_info = data.get("node_info", {})

                # If node_info exists, create a row with relevant values
                if node_info:
                    row = {
                        "model_name": node_info.get("node_name"),
                        "node_status": node_info.get("node_status"),
                        "start_time": node_info.get("node_started_at"),
                        "end_time": node_info.get("node_finished_at"),
                        "schema": None,
                        "row_count": None,
                    }

                    # Extract schema from node_relation if available
                    node_relation = node_info.get("node_relation", {})
                    if node_relation:
                        if node_relation.get("schema"):
                            schema = node_relation.get("schema")
                            if schema != 'Silver':
                                continue
                            row["schema"] = node_relation.get("schema")

                    # Extract row count from status if available
                    if "status" in data:
                        row["row_count"] = extract_row_count(data["status"])

                    # Append the row to the rows list
                    rows.append(row)

            except json.JSONDecodeError:
                print(f"Skipping invalid JSON line: {line.strip()}")

    return rows

def insert_rows_into_bigquery(client, rows):
    """
    Inserts multiple rows into the BigQuery table.

    Args:
        client: BigQuery client instance.
        rows (list of dict): List of rows to insert into BigQuery.
    """
    table_id = f"{BQ_PROJECT_ID}.{BQ_DATASET_ID}.{BQ_TABLE_ID}"

    # Format rows for BigQuery insertAll API
    formatted_rows = [rows]

    # Insert rows into BigQuery
    errors = client.insert_rows_json(table_id, formatted_rows)
    
    if errors:
        print(f"Failed to insert rows: {errors}")
    else:
        print(f"Successfully inserted {len(rows)} rows into BigQuery.")


def load_combined_row_to_bigquery():
    """
    Combines rows from the log file and loads them into BigQuery.
    """
    client = get_bigquery_client()
    create_table_if_not_exists(client)

    combined_row = extract_and_combine_logs()

    for row in combined_row:

        insert_rows_into_bigquery(client, row)

def run_dbt_command():
    """
    Changes to the Analytics folder, runs the DBT command, captures stdout, and writes it to a log file.
    """
    original_dir = os.getcwd()
    try:
        print(f"Changing directory to {ANALYTICS_DIR}...")
        os.chdir(ANALYTICS_DIR)

        print("Running DBT command...")
        result = subprocess.run(DBT_COMMAND.split(), capture_output=True, text=True)

        # Write stdout to the DBT_STDOUT_LOG file
        with open(DBT_STDOUT_LOG, "w") as f:
            f.write(result.stdout)

        print(f"DBT command output saved to {DBT_STDOUT_LOG}.")

        if result.returncode != 0:
            print("DBT command failed!")
            print(result.stderr)
            raise Exception("DBT command execution failed.")
        print("DBT command executed successfully.")
    finally:
        print(f"Switching back to original directory {original_dir}...")
        os.chdir(original_dir)

def combine_logs():
    """
    Combines all JSON log files into a single JSON file.
    """
    print("Combining logs...")
    if not os.path.exists(LOGS_PATH):
        raise Exception(f"Log path '{LOGS_PATH}' does not exist.")
    
    combined_logs = []
    # for file_name in os.listdir(LOGS_PATH):
    #     if file_name.endswith(".json"):
    #         with open(os.path.join(LOGS_PATH, file_name), "r") as f:
    #             for line in f:
    #                 combined_logs.append(json.loads(line))
    
    # with open(LOG_FILE, "w") as f:
    #     for log in combined_logs:
    #         f.write(json.dumps(log) + "\n")
    
    # Include the DBT stdout log into the combined logs
    with open(DBT_STDOUT_LOG, "r") as f:
        dbt_output = f.readlines()
        for line in dbt_output:
            try:
                combined_logs.append(json.loads(line))
            except json.JSONDecodeError:
                # Handle non-JSON log entries gracefully
                combined_logs.append({"non_json_log": line.strip()})

    # Write back the complete combined logs
    with open(LOG_FILE, "w") as f:
        for log in combined_logs:
            f.write(json.dumps(log) + "\n")
    
    print(f"Logs combined into {LOG_FILE}.")


if __name__ == "__main__":

    try:
        # Run the DBT command
        # run_dbt_command()

        # # Combine JSON logs
        combine_logs()

        # Load logs into BigQuery
        load_combined_row_to_bigquery()
    except Exception as e:
        print(f"Error: {e}")
        
    
