-- Replace these with your actual project and dataset names
DECLARE src_project STRING DEFAULT 'allieddigital-prod';
DECLARE src_dataset STRING DEFAULT 'Warehouse';
DECLARE dest_project STRING DEFAULT 'allieddigital-dataops';
DECLARE dest_dataset STRING DEFAULT 'Warehouse';

-- Get a list of tables from the source dataset
DECLARE table_list ARRAY<STRING>;

SET table_list = (
  SELECT ARRAY_AGG(table_name)
  FROM `allieddigital-prod.Warehouse.INFORMATION_SCHEMA.TABLES`
  WHERE table_type = 'BASE TABLE'
);

-- Loop through each table in the list by unnesting as an alias
FOR table_record IN (SELECT t AS table_name FROM UNNEST(table_list) AS t) DO
  EXECUTE IMMEDIATE FORMAT("""
    DROP TABLE IF EXISTS `%s.%s.%s`
  """, dest_project, dest_dataset, table_record.table_name);

  EXECUTE IMMEDIATE FORMAT("""
    CREATE TABLE `%s.%s.%s` AS
    SELECT * FROM `%s.%s.%s`
  """, dest_project, dest_dataset, table_record.table_name, src_project, src_dataset, table_record.table_name);
END FOR;