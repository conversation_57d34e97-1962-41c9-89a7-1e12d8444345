with t1 as (
  select distinct 
      ch<PERSON><PERSON>,
       ch.<PERSON>,
       ch.<PERSON>,
       ch.EligibilityId as EligibilityId_inClaimHeader,
       e.Id as EligibilityId

  from `medxoom-prod.ODS.mx_fileprocessing_ClaimHeader` ch
  left join `medxoom-prod.ODS.mx_fileprocessing_Eligibility` e
  on ch.EligibilityId = e.Id

  where ch.<PERSON> = 'Allied'
)

select * from t1 where EligibilityId is null