select 
    distinct
    g.ExternalId as GroupExternalId
    ,g.name as GroupName
    ,pl.ExternalId as PlanExternalId
    ,doc.ExternalId as DocumentExternalId
    ,doc.DisplayName as DocumentDisplayName
    ,doc.Uri as DocumentUri
    ,doc.BenefitType as DocumentBenefitType
    ,doc.DocumentType
    ,doc.DocumentCreationDate
    ,case when doc.ExternalId is not null then 'P'
      else null
      end as GroupOrPlanDocument

from `medxoom-prod.ODS.mx_insurance_Group` g
join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
on g.Id = pg.GroupId

join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
on p.Id = pg.PartnerId

join `medxoom-prod.ODS.mx_insurance_Plan`  pl
on pl.GroupId = g.Id

left join `medxoom-prod.ODS.mx_insurance_PlanDocument`  pd
on pd.PlanId = pl.Id
and pd.Active = 1

left join `medxoom-prod.ODS.mx_insurance_Document`  doc
on pd.DocumentId = doc.Id
and doc.Active = 1

where p.name = 'Allied Benefit Systems, LLC'

and g.Active = 1
and pg.Active = 1
and p.Active = 1
and pl.Active = 1

-- union distinct

-- select 
--     distinct
--     g.ExternalId as GroupExternalId
--     ,g.name as GroupName
--     ,pl.ExternalId as PlanExternalId
--     ,doc.ExternalId as DocumentExternalId
--     ,doc.DisplayName as DocumentDisplayName
--     ,doc.Uri as DocumentUri
--     ,doc.BenefitType as DocumentBenefitType
--     ,doc.DocumentType
--     ,doc.DocumentCreationDate

-- from `medxoom-prod.ODS.mx_insurance_Group` g
-- join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
-- on g.Id = pg.GroupId

-- join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
-- on p.Id = pg.PartnerId

-- join `medxoom-prod.ODS.mx_insurance_Plan`  pl
-- on pl.GroupId = g.Id

-- left join `medxoom-prod.ODS.mx_insurance_GroupDocument`  gd
-- on g.Id = gd.GroupId
-- and gd.Active = 1

-- left join `medxoom-prod.ODS.mx_insurance_Document`  doc
-- on gd.DocumentId = doc.Id
-- and doc.Active = 1


-- where p.name = 'Allied Benefit Systems, LLC'

-- and g.Active = 1
-- and pg.Active = 1
-- and p.Active = 1
-- and pl.Active = 1