WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `medxoom-dataops`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)
,base_events AS (
    SELECT *  
    FROM `medxoom-dataops`.`mixpanel_data`.`mp_master_event`
    -- WHERE DATE(time) = (select batch_date.batch_date from batch_date)
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY mp_insert_id
        ORDER BY mp_mp_api_timestamp_ms
    ) = 1
)

,sessionized_events AS (

    WITH ordered_events AS (
        SELECT
            time,
            userid,
            mp_insert_id,
            LAG(time) OVER (PARTITION BY userid ORDER BY time) AS previous_time
            
        FROM base_events
    )
    ,flagged_events AS (
        SELECT
            time,
            userid,
            mp_insert_id,
            IF(previous_time IS NULL OR TIMESTAMP_DIFF(time, previous_time, MINUTE) > 30, 1, 0) AS new_session_flag
        FROM ordered_events
    )
    ,sessionized AS (
        SELECT
            time,
            userid,
            mp_insert_id,
            SUM(new_session_flag) OVER (PARTITION BY userid ORDER BY time) AS session_group
        FROM flagged_events
    )
    ,session_ids AS (
        SELECT
            userid,
            session_group,
            MIN(time) AS session_start_time,
            MAX(time) AS session_end_time,
            MD5(CONCAT(CAST(userid AS STRING), '-', CAST(session_group AS STRING), '-', CAST(MIN(time) AS STRING))) AS sessionid,
            TIMESTAMP_DIFF(MAX(time), MIN(time), MINUTE) AS session_length
        FROM sessionized
        GROUP BY userid, session_group
    )
    SELECT 
        s.time,
        s.userid,
        s.mp_insert_id,
        si.sessionid,
        si.session_length
    FROM sessionized s
    JOIN session_ids si
        ON s.userid = si.userid 
        AND s.session_group = si.session_group
    ORDER BY s.userid, s.time

)


,base AS (
    SELECT
        mp.userid,
        uri,
        title,
        mp.time AS Event_Time,
        DATE(mp.time) AS Event_Date,
        TIME(DATETIME(mp.time)) AS Time_Of_Day,
        DATE_TRUNC(DATE(mp.TIME), MONTH) AS year_month,
        path,
        name,
        CASE WHEN mp_wifi = True THEN 1 ELSE 0 END AS mp_wifi,
        mp_region,
        mp_os_version,
        mp_os,
        CASE WHEN mp_failure_reason IS NOT NULL THEN 1 ELSE 0 END AS Fail_Event_Indicator,
        mp_country_code,
        mp_city,
        mp_carrier,
        mp_browser_version,
        mp_browser,
        mp_brand,
        mp_anon_id,
        modal_name,
        mp.member_number,
        member,
        insurancetype,
        COALESCE(CAST(groupid AS INTEGER), ep.Group_Id) AS Group_Id,
        group_number,
        filters_applied,
        event_id,
        enddate,
        mp.email,
        documenturl,
        distinct_id,
        currentview,
        claimnumber,
        claimid,
        mp.mp_insert_id,
        IFNULL(moduleconfigid, "Not Applicable") AS Module_Config_Id,
        v_mc.Module_Id,
        ep.State_Cd,
        ep.Age,
        ep.Billing_Partner_Id,
        ep.Lob_Cd,
        ep.TPA_Name,
        CASE WHEN ep.Is_Terminated = 0 THEN 'No' ELSE 'Yes' End AS Termination_Status,
        ep.Elig_Termination_Date,
        mp_event_name AS Event_Name,
        programname AS Program_Name,
        cta_type AS CTA_Type,
        methodchanneltype AS Method_Channel_Type,
        CASE 
            WHEN mp_event_name LIKE 'InTake Form%' THEN 'Care+'
            WHEN mp_event_name LIKE '%FSA%' THEN 'Flex'
            WHEN mp_event_name LIKE '%Medical Form%' or mp_event_name LIKE '%Dental Form%' or mp_event_name LIKE '%Vision Form%' or lower(mp_event_name) like 'new%claim%submitted' or lower(mp_event_name) like 'submit claim%' THEN 'Claims'
            WHEN lower(mp_event_name) LIKE 'map%' or lower(mp_event_name) LIKE 'advocacy' THEN 'Member Advocacy'
            ELSE 'Unknown'
        END AS Event_Category_1,
        CASE 
            WHEN lower(mp_event_name) LIKE '%clicked%' THEN 'Clicked'
            WHEN lower(mp_event_name) LIKE '%failed%' THEN 'Failed'
            WHEN lower(mp_event_name) LIKE '%view%' THEN 'Viewed'
            WHEN lower(mp_event_name) LIKE '%submitted%' THEN 'Submitted'
            WHEN lower(mp_event_name) LIKE '%submitted%' and lower(mp_event_name) LIKE '%claim%' THEN 'Uploaded'
            WHEN lower(mp_event_name) LIKE '%downloaded%' THEN 'Downloaded'
            WHEN lower(mp_event_name) LIKE '%opened%' THEN 'Opened'
            WHEN lower(mp_event_name) LIKE '%requested%' THEN 'Requested'
            WHEN lower(mp_event_name) LIKE '%filtered%' THEN 'Filtered'
            WHEN lower(mp_event_name) LIKE '%visited%' THEN 'Visited'
            WHEN lower(mp_event_name) LIKE '%selected%' THEN 'Selected'
            WHEN lower(mp_event_name) LIKE '%shared%' THEN 'Shared'
            WHEN lower(mp_event_name) LIKE '%canceled%' THEN 'Canceled'
            WHEN lower(mp_event_name) LIKE '%continued%' THEN 'Continued'
            ELSE 'Not Applicable'
        END AS Event_Type,
        CASE 
            WHEN mp_event_name LIKE 'InTake Form%' THEN programname
            WHEN mp_event_name LIKE '%FSA%' and mp_event_name LIKE '%Dependent%' THEN 'Dependent Reimbursement Form'
            WHEN mp_event_name LIKE '%FSA%' and mp_event_name NOT LIKE '%Dependent%' THEN 'Subscriber Reimbursement Form'
            WHEN LOWER(mp_event_name) LIKE '%dental form%' THEN 'Dental Claims Form'
            WHEN LOWER(mp_event_name) LIKE '%vision form%' THEN 'Vision Claims Form'
            WHEN LOWER(mp_event_name) LIKE '%medical form%' THEN 'Medical Claims Form'
            WHEN LOWER(mp_event_name) LIKE '%internation%medical form%' THEN 'International Medical Claims Form'
            WHEN lower(mp_event_name) like 'new%claim%submitted' or lower(mp_event_name) like 'submit claim%' THEN 'Claim Submission'
                ELSE 'Not Applicable'
        END AS Event_Category_2,
        CASE 
            WHEN mp_os IN ('iOS', 'Android', 'iPadOS') AND mp_browser IS NOT NULL THEN 'Mobile Web'
            WHEN mp_os IN ('iOS', 'Android', 'iPadOS') AND mp_browser IS NULL THEN 'App'
            ELSE 'Web'
        END AS Portal_Access_Mode,
        'Member Portal' AS Portal_Name,
        mp_mp_api_timestamp_ms,
        vu.CreatedAt AS Registration_Date,
        DATE_DIFF(DATE(mp.time), DATE(vu.CreatedAt), DAY) AS Days_Since_Registered,
        CASE WHEN ep.Is_Terminated = 1 AND DATE(mp.time) > DATE(ep.Elig_Termination_Date) THEN DATE_DIFF(DATE(mp.time), DATE(ep.Elig_Termination_Date), DAY) ELSE null END AS Days_Since_Terminated,
        se.sessionid,
        se.session_length
    FROM base_events mp
    JOIN sessionized_events se
        ON mp.mp_insert_id = se.mp_insert_id
    LEFT JOIN `medxoom-dataops`.`Warehouse`.`V_Policy_User_DW` pu
        ON CAST(mp.userid AS STRING) = CAST(pu.UserId AS STRING)
    LEFT JOIN `medxoom-dataops`.`Warehouse`.`V_F_Member_Metrics_L1_All` ep
        ON ep.Elig_Id = pu.Elig_Id
    LEFT JOIN `medxoom-dataops`.`Warehouse`.`V_User_DW` vu
        ON pu.UserId = vu.Id
    LEFT JOIN `medxoom-dataops`.`Warehouse`.`V_Module_Config_DW` v_mc
        ON IFNULL(moduleconfigid, "Not Applicable") = CAST(v_mc.Module_Config_Id AS STRING)
    WHERE 
         mp_event_name <> '$identify'
        AND mp_event_name <> 'External Link Phone Viewed'
        AND mp_event_name <> 'Back To Login From Verification Page'
        AND mp_event_name <> 'Cancel Active Popup'
        --AND (lower(mp_event_name) NOT LIKE '%selected%' OR mp_event_name = 'Dashboard Widget Selected')
        AND impersonation = False
        AND pu.UserId IS NOT NULL
)
 

,chart_cats AS (
  SELECT 
      V.Name as Group_Name
      ,Vpa.name as Partner_Name
      ,vlob.Lob_Name
      ,case when base.EVENT_CATEGORY_1 = 'Claims' and (EVENT_TYPE = 'Downloaded' OR EVENT_TYPE = 'Submitted') THEN 'Claims'
        when base.EVENT_CATEGORY_1 = 'Flex' THEN 'Flex'
        when base.EVENT_CATEGORY_1 = 'Care+' THEN 'Care+'
        when base.EVENT_CATEGORY_1 = 'Member Advocacy' THEN 'Member Advocacy'
        when base.EVENT_NAME = 'Your Member Advocacy Call Now Clicked' THEN 'Member Advocacy'
        ELSE 'Not Applicable'
        END AS Chart_Category_1
      ,case when base.EVENT_CATEGORY_1 = 'Care+' then base.EVENT_CATEGORY_2
       when base.EVENT_CATEGORY_1 = 'Flex' then base.Event_type
       ELSE 'Not Applicable'
       END AS Chart_Category_2
      
      ,base.*

      
 FROM base 

 
  join   `medxoom-dataops`.`Warehouse`.`V_Base_Group_DW` V
  on base.Group_Id = V.Id
  
  left join   `medxoom-dataops`.`Warehouse`.`V_LOB_DW` vlob
  on base.Lob_Cd = vlob.Lob_Cd
  
  join   `medxoom-dataops`.`Warehouse`.`V_Base_Partner_DW` Vpa
  on base.Billing_Partner_Id = Vpa.Id
  where 
  base.event_category_1 in ('Flex', 'Care+', 'Claims', 'Member Advocacy')
  
  -- AND
  -- case when {{Lob-Name}} is not null
  -- THEN vlob.Lob_Name in {{Lob-Name}}
  -- ELSE true END
  -- AND 
  -- case when {{Group-Name}} is not null
  -- THEN V.Name in {{Group-Name}}
  -- ELSE true END
)

SELECT * FROM chart_cats where chart_category_1 in ( 'Care+', 'Claims', 'Member Advocacy') and chart_category_2 <> 'Failed'
--SELECT * FROM chart_cats where chart_category_1 in ( 'Care+') and chart_category_2 <> 'Failed'

--SELECT * FROM chart_cats where chart_category_1 in ('Member Advocacy') --d chart_category_2 <> 'Failed'
-- AND

-- (ARRAY_LENGTH({{Lob-Name}}) = 0 OR CITY IN UNNEST({{Lob-Name}}))

-- CASE WHEN  
-- {{Lob-Name}}[OFFSET(0)] IS NULL
-- THEN True  
-- ELSE Lob_Name in {{Lob-Name}} END