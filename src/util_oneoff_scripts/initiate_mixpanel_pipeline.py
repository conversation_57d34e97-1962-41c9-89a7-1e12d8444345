import requests
import base64
from pprint import pprint

SERVICE_ACCOUNT_SECRET_UAT = "OMB8wuFXVUTbjhQYljUx97r1x3nE043p"
SERVICE_ACCOUNT_USERNAME_UAT = "mixpanel_data_export_uat.e1da4f.mp-service-account"

# 1) Your Mixpanel service account credentials:
# SERVICE_ACCOUNT_USERNAME = "mixpanel_all_access.e5858d.mp-service-account"
# SERVICE_ACCOUNT_SECRET   = "7ZiTto7zAsDcA4VWpZ8FoM4NnsQJT0PD"


# auth_resp = requests.get(
#   'https://mixpanel.com/api/app/me', 
#   auth=(SERVICE_ACCOUNT_USERNAME, SERVICE_ACCOUNT_SECRET),
# )

# pprint(auth_resp.json())

url = "https://data.mixpanel.com/api/2.0/nessie/pipeline/create"

payload = {
    "project_id":3696664,
    "type": "bigquery",
    "trial": False,
    "sync":True,
    "data_source": "events",
    "frequency": "daily",
    "data_format": "json",
    "from_date":"2025-05-10",
    "bq_region": "US",
    "gcp_project":"allieddigital-uat",
    "bq_dataset_name":"mixpanel_data"
}


# # 4) Make the POST request with HTTP Basic Auth
response = requests.post(
    url,
    auth=(SERVICE_ACCOUNT_USERNAME_UAT, SERVICE_ACCOUNT_SECRET_UAT),
    json=payload,
)

# 5) Check response
if response.ok:
    print("Pipeline created:", response.json())
else:
    print("Error", response.status_code, response.text)



# headers = {
#     "accept": "application/json",
#     "content-type": "application/x-www-form-urlencoded",
#     "authorization": "Basic bWl4cGFuZWxfZGF0YV9leHBvcnRfcHJvZC5kOWVjYmQubXAtc2VydmljZS1hY2NvdW50Oks4V0ZjWTEyc0lsT296Tm1zOGQ4bm1zVVZCb2tKWHUx"
# }

# Your token string
# token = "bWl4cGFuZWxfYWxsX2FjY2Vzcy5lNTg1OGQubXAtc2VydmljZS1hY2NvdW50OjdaaVR0bzd6QXNEY0E0VldwWjhGb000Tm5zUUpUMFBE"

# # Convert the token string to bytes
# token_bytes = token.encode("utf-8")

# # Encode the bytes using base64
# base64_bytes = base64.b64encode(token_bytes)

# # Convert the base64-encoded bytes back to a string
# base64_token = base64_bytes.decode("utf-8")

# headers = {
#     "accept": "application/json",
#     "content-type": "application/x-www-form-urlencoded",
#     "authorization": "Basic "+base64_token
# }

# response = requests.post(url, data=payload, headers=headers)

# print(response.text)