#!/usr/bin/env python3

from google.cloud import bigquery
import json
import os

def extract_view_definition(service_account_path, project_id, dataset_id, view_id):
    """
    Extract BigQuery view definition using service account JSON
    """
    
    # Initialize the BigQuery client with service account
    client = bigquery.Client.from_service_account_json(
        service_account_path, 
        project=project_id
    )
    
    # Get the view reference
    view_ref = client.dataset(dataset_id).table(view_id)
    
    try:
        # Get the view object
        view = client.get_table(view_ref)
        
        if view.table_type != 'VIEW':
            print(f"Error: {view_id} is not a view, it's a {view.table_type}")
            return None
            
        # Extract view information
        view_info = {
            'project_id': view.project,
            'dataset_id': view.dataset_id,
            'view_id': view.table_id,
            'created': view.created.isoformat() if view.created else None,
            'modified': view.modified.isoformat() if view.modified else None,
            'description': view.description,
            'sql_query': view.view_query,
            'view_use_legacy_sql': view.view_use_legacy_sql
        }
        
        return view_info
        
    except Exception as e:
        print(f"Error accessing view: {e}")
        return None

def main():
    # Configuration
    SERVICE_ACCOUNT_PATH = "/Users/<USER>/Medxoom/medxoom-prod-37b489489944.json"  # Update this path
    PROJECT_ID = "medxoom-prod"
    DATASET_ID = "Warehouse"
    VIEW_ID = "stg_mixpanel_events_data"
    
    # Extract view definition
    view_info = extract_view_definition(
        SERVICE_ACCOUNT_PATH, 
        PROJECT_ID, 
        DATASET_ID, 
        VIEW_ID
    )
    
    if view_info:
        # Print the SQL query
        print("=" * 80)
        print(f"VIEW: {PROJECT_ID}.{DATASET_ID}.{VIEW_ID}")
        print("=" * 80)
        print(view_info['sql_query'])
        print("=" * 80)
        
        # Save to files
        # Save full metadata as JSON
        # with open(f"{VIEW_ID}_metadata.json", "w") as f:
        #     json.dump(view_info, f, indent=2)
            
        # # Save just the SQL
        # with open(f"{VIEW_ID}.sql", "w") as f:
        #     f.write(view_info['sql_query'])
            
        # print(f"\nFiles saved:")
        # print(f"- {VIEW_ID}_metadata.json (full metadata)")
        # print(f"- {VIEW_ID}.sql (SQL query only)")

if __name__ == "__main__":
    main()