with base as (
  select
  distinct
  gp.Id as GroupId
  ,elig.GroupNumber
  ,gp.Name as GroupName
  ,elig.MemberNumber as MemberUID
  ,to_json_string(Json_extract(customattributes,'$.EnrSeqNumber')) as MemberEnrolleeSequenceIDNumber
  ,REPLACE(to_json_string(Json_extract(customattributes,'$.MemDepCode')), '"','') as MemDepCode
  ,REPLACE(to_json_string(JSON_EXTRACT(CustomAttributes, '$.DirectoryId')), '"','')  as DirectoryId
  , CONCAT(to_json_string(Json_extract(customattributes,'$.EnrSeqNumber')),
      REPLACE(to_json_string(Json_extract(customattributes,'$.MemDepCode')), '"',''),
      REPLACE(to_json_string(JSON_EXTRACT(CustomAttributes, '$.DirectoryId')), '"','') ) as MemberUID2
  ,elig.SSN as MemberSSN
  ,elig.FirstName as Member<PERSON><PERSON>t
  ,elig.LastName as MemberLast
  ,REPLACE(to_json_string(Json_extract(customattributes,'$.BenefitElections')), '"','') as BenefitElections
  ,case 
    when REPLACE(to_json_string(JSON_EXTRACT(customattributes, '$.FlexFlag')),'"','') = 'true' THEN 'View your flexible spending account summary, balances, and recent transactions.'
    else 'NA'
  
  end as FlexWidgetDescription

  from `medxoom-prod.ODS.mx_fileprocessing_Eligibility` elig

  join `medxoom-prod.ODS.mx_insurance_Policy` t2
        on elig.Id = t2.EligibilityId 
  join `medxoom-prod.ODS.mx_insurance_Plan` t3
          on t2.PlanId = t3.Id
  join `medxoom-prod.ODS.mx_insurance_Group` gp
          on t3.GroupId = gp.Id

  where (elig.TerminationDate > cast(current_date() as string) or elig.TerminationDate is null or elig.TerminationDate='')
  and elig.Active = 1
  and t2.Active = 1
  and t3.Active = 1
  and gp.Active = 1
  and elig.PayerName = 'Allied'
)

,ins_type_2_base as (
  select distinct
    CONCAT(to_json_string(Json_extract(customattributes,'$.EnrSeqNumber')),
      REPLACE(to_json_string(Json_extract(customattributes,'$.MemDepCode')), '"',''),
      REPLACE(to_json_string(JSON_EXTRACT(CustomAttributes, '$.DirectoryId')), '"','') ) as MemberUID2
   ,case when InsuranceType = 'med' then "Medical"
    when InsuranceType = 'den' then "Dental"
    when InsuranceType = 'vis' then "Vision"
    end as InsuranceType


  from `medxoom-prod.ODS.mx_fileprocessing_Eligibility` elig

  where (elig.TerminationDate > cast(current_date() as string) or elig.TerminationDate is null or elig.TerminationDate='')
  and elig.Active = 1
  and PayerName = "Allied"

  union all

  select distinct
     CONCAT(to_json_string(Json_extract(customattributes,'$.EnrSeqNumber')),
      REPLACE(to_json_string(Json_extract(customattributes,'$.MemDepCode')), '"',''),
      REPLACE(to_json_string(JSON_EXTRACT(CustomAttributes, '$.DirectoryId')), '"','') ) as MemberUID2
    ,'Flex' as InsuranceType


  from `medxoom-prod.ODS.mx_fileprocessing_Eligibility` elig

  where (elig.TerminationDate > cast(current_date() as string) or elig.TerminationDate is null or elig.TerminationDate='')
  and elig.Active = 1
  and PayerName = "Allied"
  and REPLACE(to_json_string(JSON_EXTRACT(customattributes, '$.FlexFlag')),'"','') = 'true'  

  order by MemberUID2,InsuranceType
)

,ins_type_2_ordered as (
  SELECT MemberUID2, InsuranceType, 

  FROM ins_type_2_base
  ORDER BY
      MemberUID2,
      CASE
          WHEN InsuranceType = 'Medical' THEN 1
          WHEN InsuranceType = 'Dental' THEN 2
          WHEN InsuranceType = 'Vision' THEN 3
          WHEN InsuranceType = 'Flex' THEN 4
      END
)

,ins_type_2 as (
  select distinct
    MemberUID2
    ,REPLACE(REPLACE(REPLACE(REPLACE(to_json_string(array_agg(InsuranceType)),'"',''),"]",""),"[",""),",",", ") InsuranceTypes

  from ins_type_2_ordered

  group by MemberUID2

)

,mem_serv_ph_ as (
  select 
    
    t1.GroupId
    ,JSON_EXTRACT(Settings, '$.Uri')  as MemberServiceWidgetCTAPhoneNumber
    ,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 7
  and JSON_EXTRACT(Settings, '$.MenuName') = '"Member Services"'
  and t1.Active = 1
)

,mem_serv_ph as (
  select distinct
        GroupId
        ,MemberServiceWidgetCTAPhoneNumber
        ,BenefitElectionCode
    from mem_serv_ph_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)
, teladoc_desc_ as (
  select 
    
    t1.GroupId
    ,"Get answers to your health questions with our fast, convenient, high-quality care - anytime, anywhere." as TelehealthVendorWidgetDiscriptionTeladoc
    ,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode

  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 36
  and t1.Active = 1
)
, teladoc_desc as (
  select 
    distinct
    GroupId
    ,TelehealthVendorWidgetDiscriptionTeladoc
    ,BenefitElectionCode

  from teladoc_desc_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)

,eden_ as (
  select
  
  t1.GroupId
  ,JSON_EXTRACT(Settings, '$.ExternalLinkDescription') as VirtualDirectPrimaryCareDescriptionEdenHealth
  ,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 7
  and lower(t1.Settings) like '%eden%'
  and t1.Active = 1
)

, eden as (
  select 
    distinct
    GroupId
    ,VirtualDirectPrimaryCareDescriptionEdenHealth
    ,BenefitElectionCode

  from eden_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)

,pbm_uri_ as (
  select
  
  t1.GroupId
  ,JSON_EXTRACT(Settings, '$.ExternalLinkLogoUri') as PBMVendorLogo
  ,JSON_EXTRACT(Settings, '$.Uri') as PBMVendorUrl
  ,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 7
  and JSON_EXTRACT(Settings, '$.MenuName') = '"Pharmacy"'
  and t1.Active = 1
)

, pbm_uri as (
  select 
    distinct
    GroupId
    ,PBMVendorLogo
    ,PBMVendorUrl
    ,BenefitElectionCode

  from pbm_uri_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)
,hsa_desc_ as (
    select
  
  t1.GroupId
  ,JSON_EXTRACT(Settings, '$.ExternalLinkDescription') as HSAVendorDescriptionBenefitWallet
  ,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 7
  and JSON_EXTRACT(Settings, '$.MenuName') = '"HSA"'
  and t1.Active = 1
)

,hsa_desc as (
  select
    distinct
    GroupId
    ,HSAVendorDescriptionBenefitWallet
    ,BenefitElectionCode

  from hsa_desc_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)
,careplus_home_ as (
    select 
    
    t1.GroupId
    ,"Let Allied Care's expert team and resources help you navigate complex health care situations." as CarePlusHomePageDescription
,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 41
  and t1.Active = 1
)
,careplus_home as (
  select
    distinct
    GroupId
    ,CarePlusHomePageDescription
    ,BenefitElectionCode

  from careplus_home_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)
,careplus_precert_ as (
    select 
    
    t1.GroupId
    ,JSON_EXTRACT(Settings, '$.PrecertPhone') as CarePlusPrecertificationandClinicalCareSupportCTAPhoneNumber
    ,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 41
  and JSON_EXTRACT(Settings, '$.HasCaseMgmt') = 'true'
  and t1.Active = 1
)
,careplus_precert as (
  select
    distinct
    GroupId
    ,CarePlusPrecertificationandClinicalCareSupportCTAPhoneNumber
    ,BenefitElectionCode

  from careplus_precert_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)
,careplus_oncology_ as (
    select 
    
    t1.GroupId
    ,"Allied's expert team of trained oncology specialists are here to support you and your family in the event of a cancer diagnosis. Through Allied Care, you will have a dedicated specialist to guide you every step of the way by providing informative and compassionate counseling throughout your cancer care journey. To get started, call Allied Care's Oncology support team." as CarePlusOncologyDescription
  ,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode

  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 41
  and JSON_EXTRACT(Settings, '$.HasOncology') = 'true'
  and t1.Active = 1
)
,careplus_oncology as (
  select
    distinct
    GroupId
    ,CarePlusOncologyDescription
    ,BenefitElectionCode

  from careplus_oncology_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)
,careplus_behav_ as (
    select 
    
    t1.GroupId
    ,"At some point in our lives, we are faced with challenges that take a toll on our mental health. Whether you are coping with stress, grief, anxiety, relationship issues, depression, or substance abuse, Allied Care's dedicated team of highly-trained and compassionate mental health professionals can help you get the support and guidance you need. Contact Allied Care's Behavioral Health team to get started." as CarePlusBehavioralHealthDescription
,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 41
  and JSON_EXTRACT(Settings, '$.HasBehavioralHealth') = 'true'
  and t1.Active = 1
)
,careplus_behav as (
  select
    distinct
    GroupId
    ,CarePlusBehavioralHealthDescription
    ,BenefitElectionCode

  from careplus_behav_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)
,careplus_wellness_ as (
    select 
    
    t1.GroupId
    ,"Improve your physical health, reduce stress, and enhance your overall wellbeing with Allied Care's Wellness Programs. Get connected with an expert health coach who will help you develop meal plans, increase your physical activity, and introduce you to simple ways to stay healthy. To get started, contact Allied Care's Wellness support team." as CarePlusWellnessAndLifestyleDescription
,split(REPLACE(JSON_EXTRACT(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode

  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 41
  and JSON_EXTRACT(Settings, '$.HasWellnessSupport') = 'true'
  and t1.Active = 1
)
,careplus_wellness as (
  select
    distinct
    GroupId
    ,CarePlusWellnessAndLifestyleDescription
    ,BenefitElectionCode

  from careplus_wellness_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)

,acs_wellness_ as (
    select
  
  t1.GroupId
  ,JSON_EXTRACT(Settings, '$.Uri') as ACSWellnessCTAURL
,split(replace(json_extract(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 7
  and JSON_EXTRACT(Settings, '$.MenuName') = '"Wellness Program"'
  and t1.Active = 1
)

,acs_wellness as (
  select
    distinct
    GroupId
    ,ACSWellnessCTAURL
    ,BenefitElectionCode

  from acs_wellness_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)

,acs_behav_health_ as (
    select
  
  t1.GroupId
  ,JSON_EXTRACT(Settings, '$.Uri') as ACSBehavioralHealthCTAURL
,split(replace(json_extract(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 7
  and JSON_EXTRACT(Settings, '$.MenuName') = '"EAP/Behavioral Health Support"'
  and t1.Active = 1
)

,acs_behav_health as (
  select
    distinct
    GroupId
    ,ACSBehavioralHealthCTAURL
    ,BenefitElectionCode

  from acs_behav_health_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)


,precert_casemgmt_ as (
    select
  
  t1.GroupId
  ,JSON_EXTRACT(Settings, '$.Uri') as PreCertVendorCTAPhoneNumber
,split(replace(json_extract(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 7
  and JSON_EXTRACT(Settings, '$.MenuName') = '"Precertification and Case Management"'
  and t1.Active = 1
)

,precert_casemgmt as (
  select
    distinct
    GroupId
    ,PreCertVendorCTAPhoneNumber
    ,BenefitElectionCode

  from precert_casemgmt_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)

,hcbb_ as (
  select
    
    t1.GroupId
    ,JSON_EXTRACT(Settings, '$.Uri') as HealthCareBlueBookCTAURL
,split(replace(json_extract(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
    from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
    join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
    on t1.GroupId = pg.GroupId

    join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
    on pg.PartnerId = p.Id

    where p.Name = "Allied Benefit Systems, LLC"
    and t1.Type = 7
    and JSON_EXTRACT(Settings, '$.MenuName') = '"Price Transparency Tool"'
    and t1.Active = 1
)

,hcbb as (
  select
    distinct
    GroupId
    ,HealthCareBlueBookCTAURL
    ,BenefitElectionCode

  from hcbb_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)

,imaging_vendor_ as (
    select
  
  t1.GroupId
  ,JSON_EXTRACT(Settings, '$.ExternalLinkDescription') as ImagingVendorDescription
  ,split(replace(json_extract(Settings, '$.BenefitElectionCode'), '"',''),",") as BenefitElectionCode
  from `medxoom-prod.ODS.mx_configuration_ModuleConfig` t1
  join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
  on t1.GroupId = pg.GroupId

  join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
  on pg.PartnerId = p.Id

  where p.Name = "Allied Benefit Systems, LLC"
  and t1.Type = 7
  and lower(t1.Settings) like '%imaging%'
  and t1.Active = 1
)

,imaging_vendor as (
  select
    distinct
    GroupId
    ,ImagingVendorDescription
    ,BenefitElectionCode

  from imaging_vendor_ cross join unnest(BenefitElectionCode) as BenefitElectionCode
)

select
  distinct
  t1.GroupNumber
  ,t1.GroupName
  ,t1.MemberUID
  ,t1.MemberEnrolleeSequenceIDNumber
  ,t1.MemberSSN
  ,t1.MemberFirst
  ,t1.MemberLast
  ,t1.MemDepCode
  ,ins.InsuranceTypes
  ,t1.FlexWidgetDescription
  ,t2.MemberServiceWidgetCTAPhoneNumber
  ,t3.TelehealthVendorWidgetDiscriptionTeladoc
  ,t4.VirtualDirectPrimaryCareDescriptionEdenHealth
  ,t5.PBMVendorLogo
  ,t5.PBMVendorUrl
  ,t6.HSAVendorDescriptionBenefitWallet
  ,t7.CarePlusHomePageDescription
  ,t8.CarePlusPrecertificationandClinicalCareSupportCTAPhoneNumber
  ,t9.CarePlusOncologyDescription
  ,t10.CarePlusBehavioralHealthDescription
  ,t11.CarePlusWellnessAndLifestyleDescription
  ,t12.ACSWellnessCTAURL
  ,t15.ACSBehavioralHealthCTAURL
  ,t16.PreCertVendorCTAPhoneNumber
  ,t13.HealthCareBlueBookCTAURL
  ,t14.ImagingVendorDescription
  ,null as IncludedHealthSimplifyDescription
  ,t1.DirectoryId
  ,t1.MemberUID2
  
  
from base t1


left join mem_serv_ph t2
on t1.GroupId = t2.GroupId and t2.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join ins_type_2 ins
on t1.MemberUID2 = ins.MemberUID2

left join teladoc_desc t3
on t1.GroupId = t3.GroupId and t3.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections))

left join eden t4
on t1.GroupId = t4.GroupId and t4.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join pbm_uri t5
on t1.GroupId = t5.GroupId and t5.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join hsa_desc t6
on t1.GroupId = t6.GroupId  and t6.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join careplus_home t7
on t1.GroupId = t7.GroupId and t7.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections))

left join careplus_precert t8
on t1.GroupId = t8.GroupId and t8.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections))

left join careplus_oncology t9
on t1.GroupId = t9.GroupId and t9.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join careplus_behav t10
on t1.GroupId = t10.GroupId and t10.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join careplus_wellness t11
on t1.GroupId = t11.GroupId and t11.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join acs_wellness t12
on t1.GroupId = t12.GroupId  and t12.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join hcbb t13
on t1.GroupId = t13.GroupId  and t13.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join imaging_vendor t14
on t1.GroupId = t14.GroupId and t14.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join acs_behav_health t15
on t1.GroupId = t15.GroupId and t15.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections)) 

left join precert_casemgmt t16
on t1.GroupId = t16.GroupId  and t16.BenefitElectionCode in UNNEST(SPLIT(t1.BenefitElections))