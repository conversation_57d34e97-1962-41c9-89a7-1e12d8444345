select 
      distinct
       g.ExternalId as GroupExternalId
      ,g.Name as GroupName
      ,pl.ExternalId as PlanExternalId
      ,ben.BenefitInformationCode
      ,ben.CoverageLevel
      ,ben.ServiceTypeCode
      ,ben.InsuranceTypeCode
      ,ben.TimePeriodQualifier
      ,ben.<PERSON>
      ,ben.<PERSON><PERSON>
      ,ben.QuantityQualifier
      ,ben.Quantity
      ,ben.AuthorizationIndicator
      ,ben.NetworkTier Benefits_NetworkTier
      ,ben.DeductibleApplies
      ,bent.Tier as BenefitTier
      ,bent.TierName as BenefitTierName
      ,pnw.Tier as PlanTierNetwork_Tier
      ,nw.ExternalNetworkId
      ,nw.Name as NetworkName
      ,'PlanTierNetwork' as PlanTierNetworkOrPlanNetwork
      

from `medxoom-prod.ODS.mx_insurance_Group` g
join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
on g.Id = pg.GroupId

join `medxoom-prod.ODS.mx_insurance_Plan` pl
on pl.GroupId = g.Id

join `medxoom-prod.ODS.mx_insurance_Benefit` ben
on pl.Id = ben.PlanId

join `medxoom-prod.ODS.mx_insurance_BenefitTier` bent
on pl.Id = bent.PlanId

join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
on p.Id = pg.PartnerId

left join `medxoom-prod.ODS.mx_providersearch_PlanTierNetwork` pnw
on pl.Id = pnw.PlanId
and pnw.Active = 1

left join `medxoom-prod.ODS.mx_providersearch_Network` nw
on nw.Id = pnw.NetworkId
and nw.Active = 1

where p.name = 'Allied Benefit Systems, LLC'

and g.Active = 1
and pl.Active = 1
and p.Active = 1
and ben.Active = 1
and bent.Active = 1
and pg.Active = 1
and p.Active = 1

union distinct

select 
      distinct
       g.ExternalId as GroupExternalId
      ,g.Name as GroupName
      ,pl.ExternalId as PlanExternalId
      ,ben.BenefitInformationCode
      ,ben.CoverageLevel
      ,ben.ServiceTypeCode
      ,ben.InsuranceTypeCode
      ,ben.TimePeriodQualifier
      ,ben.MonetaryValue
      ,ben.Percent
      ,ben.QuantityQualifier
      ,ben.Quantity
      ,ben.AuthorizationIndicator
      ,ben.NetworkTier
      ,ben.DeductibleApplies
      ,bent.Tier as BenefitTier
      ,bent.TierName as BenefitTierName
      ,null PlanTierNetwork_Tier
      ,nw.ExternalNetworkId
      ,nw.Name as NetworkName
      ,'PlanNetwork' as PlanTierNetworkOrPlanNetwork

      

from `medxoom-prod.ODS.mx_insurance_Group` g
join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
on g.Id = pg.GroupId

join `medxoom-prod.ODS.mx_insurance_Plan` pl
on pl.GroupId = g.Id

join `medxoom-prod.ODS.mx_insurance_Benefit` ben
on pl.Id = ben.PlanId

join `medxoom-prod.ODS.mx_insurance_BenefitTier` bent
on pl.Id = bent.PlanId

join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
on p.Id = pg.PartnerId

left join `medxoom-prod.ODS.mx_providersearch_PlanNetwork` pnw
on pl.Id = pnw.PlanId
and pnw.Active = 1

left join `medxoom-prod.ODS.mx_providersearch_Network` nw
on nw.Id = pnw.NetworkId
and nw.Active = 1

where p.name = 'Allied Benefit Systems, LLC'

and g.Active = 1
and pl.Active = 1
and p.Active = 1
and ben.Active = 1
and bent.Active = 1
and pg.Active = 1
and p.Active = 1