select 
    distinct
    g.ExternalId as GroupExternalId
    ,g.name as GroupName
    ,mc.ExternalId as ModuleExternalId
    ,mc.Type as ModuleType
    ,mc.Active as ModuleConfiguration_Active

from `medxoom-prod.ODS.mx_insurance_Group` g
join `medxoom-prod.ODS.mx_partnerorganization_PartnerGroup` pg
on g.Id = pg.GroupId

join `medxoom-prod.ODS.mx_partnerorganization_Partner` p
on p.Id = pg.PartnerId

join `medxoom-prod.ODS.mx_configuration_ModuleConfig` mc
on mc.GroupId = g.Id

where p.name = 'Allied Benefit Systems, LLC'

and g.Active = 1
and pg.Active = 1
and p.Active = 1