FROM python:3.10-slim
WORKDIR /usr/src/app
RUN mkdir -p /usr/src/app/analytics
COPY ./src/dbt_documentation/requirements.txt /usr/src/app/requirements.txt
RUN pip install -r requirements.txt
COPY ./src/config/profiles.yml /root/.dbt/profiles.yml
COPY ./src/keys/* /root/.dbt/
COPY /Analytics/ /usr/src/app/analytics

RUN dbt deps --project-dir /usr/src/app/analytics

COPY ./src/dbt_documentation/ddtrace_requirements.txt /usr/src/app/ddtrace_requirements.txt
COPY --from=datadog/serverless-init:1 /datadog-init /app/datadog-init
RUN pip install -r ddtrace_requirements.txt --target /dd_tracer/python/

ENV DD_SERVICE=dbt_documentation

# RUN dbt docs generate --project-dir ./analytics/

EXPOSE 8080

#ENTRYPOINT ["/app/datadog-init"]
#CMD ["/dd_tracer/python/bin/ddtrace-run", "dbt", "docs", "serve", "--project-dir","./analytics/", "--port", "8080"]

CMD dbt docs generate --project-dir ./analytics/ --target $ENVIRONMENT_NAME && dbt docs serve --project-dir ./analytics/ --target $ENVIRONMENT_NAME --host 0.0.0.0 --port 8080
