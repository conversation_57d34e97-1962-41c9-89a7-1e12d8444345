from flask import request, jsonify
from services.bigquery_service import BigQueryService
from datetime import datetime, timezone
from apis import app
import os

@app.route("/", methods=["GET"])
def get_heart_beat():
    return "Batch Control API is up and running"

@app.route('/get_env_vars', methods=["GET"])
def get_env_vars():
    return jsonify({
        "project_id": os.environ["PROJECT_ID"],
        "environment_name": os.environ["ENVIRONMENT_NAME"]
    })

@app.route("/get_latest_batch_execution_date/<string:batch_control_id>/batchtype/<string:batch_type>"
           , methods=["GET"])
def get_latest_batch_execution_date(batch_control_id:str, batch_type:str):
    
    bigquery_service = BigQueryService()
     
    next_execution_date = \
        bigquery_service.get_next_execution_date(batch_control_id, batch_type)
    
    return jsonify({"is_first_run": next_execution_date[0], 
                    "execution_date": next_execution_date[1].strftime("%Y-%m-%d")}) 
                  
@app.route("/does_batch_need_to_run/<string:batch_control_id>/batchtype/<string:batch_type>",
           methods=["GET"])
def does_batch_need_to_run(batch_control_id:str, batch_type:str):
    bigquery_service = BigQueryService()
    
    next_execution_date = \
        bigquery_service.get_next_execution_date(batch_control_id, batch_type)
    
    current_date_utc = datetime.now(tz=timezone.utc).date()
    
    return jsonify({"run_job": next_execution_date[1] <= current_date_utc,
                    "next_execution_date": next_execution_date[1].strftime("%Y-%m-%d"),
                   "current_date": current_date_utc.strftime("%Y-%m-%d"),
                   "date_diff": (next_execution_date[1] - current_date_utc).days
                   })


@app.route("/increment_batch_execution_date", methods=["POST"])
def update_batch_execution_date():
    
    bigquery_service = BigQueryService()
    
    request_data = request.get_json()
    
    batch_control_id = request_data["batch_control_id"]
    batch_type = request_data["batch_type"]
    
    bigquery_service.increment_execution_date(batch_control_id, batch_type)
    
    return jsonify({"message": "Batch Control Date Updated"})

