from google.oauth2 import service_account
from google.cloud import bigquery
from datetime import datetime, timedelta
from typing import Tuple
import pandas_gbq
import os

class BigQueryService:
    def __init__(self):
        self.environment = os.environ["ENVIRONMENT_NAME"]
        self.project = os.environ["PROJECT_ID"]
        self.namespace = os.environ["NAMESPACE"]
        self.credentials = service_account.Credentials.from_service_account_file(
                        f"/root/.dbt/{self.namespace}-{self.environment}-dw-key.json",)
        self.bq_client = bigquery.Client.from_service_account_json(f"/root/.dbt/{self.namespace}-{self.environment}-dw-key.json")
        
    
    def get_next_execution_date(self,
        batch_id: str, 
        batch_type: str) -> Tuple[bool,datetime.date]:
        
        sql_statement = f"""
        SELECT Batch_Control_Dt as Batch_Control_Dt FROM {self.project}.Warehouse.T_Batch_Control
        WHERE Batch_Id = '{batch_id}' and Batch_Type = '{batch_type}'
        """
        df = pandas_gbq.read_gbq(sql_statement, 
                                 project_id=self.project, 
                                 credentials=self.credentials)
        
        if (df['Batch_Control_Dt'].count() > 0):
            return False, df['Batch_Control_Dt'].iloc[0]
        else: 
            return True, datetime.strptime("2024-11-01", "%Y-%m-%d").date() 
    
    def increment_execution_date(self,
                                batch_control_id: str, 
                                batch_type: str):
        
        next_execution_date = self.get_next_execution_date(batch_control_id, batch_type)
    
        batch_control_date = (next_execution_date[1] + timedelta(days=1)).strftime('%Y-%m-%d')
        
        query = ""
        if(next_execution_date[0]):
            # First run, create a new record
            query = f"""
                INSERT INTO {self.project}.Warehouse.T_Batch_Control (Batch_Id, Batch_Type, Batch_Control_Dt)
                VALUES ('{batch_control_id}', '{batch_type}', '{batch_control_date}')
                """
        else:
            # Not the first run, update needed
            query = f"""
                UPDATE {self.project}.Warehouse.T_Batch_Control
                SET Batch_Control_Dt = '{batch_control_date}'
                WHERE Batch_Id = '{batch_control_id}' AND Batch_Type = '{batch_type}'
                """
            
        job = self.bq_client.query(query)
        
        job.result()  
    