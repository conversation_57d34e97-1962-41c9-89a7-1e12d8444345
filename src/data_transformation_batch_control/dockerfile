# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy the profile and required files into the container
COPY ./src/data_transformation_batch_control/requirements.txt /usr/src/app/requirements.txt
# Install any needed dependencies
RUN pip install -r requirements.txt

COPY ./src/keys/* /root/.dbt/
COPY ./src/data_transformation_batch_control/run.py /usr/src/app/run.py
COPY ./src/data_transformation_batch_control/apis/* /usr/src/app/apis/
COPY ./src/data_transformation_batch_control/services/* /usr/src/app/services/

COPY ./src/data_transformation_batch_control/ddtrace_requirements.txt /usr/src/app/ddtrace_requirements.txt
COPY --from=datadog/serverless-init:1 /datadog-init /app/datadog-init
RUN pip install -r ddtrace_requirements.txt --target /dd_tracer/python/

ENV DD_SERVICE=data_transformation_batch_control

EXPOSE 8080

ENTRYPOINT ["/app/datadog-init"]
CMD ["/dd_tracer/python/bin/ddtrace-run", "python3", "run.py"]
