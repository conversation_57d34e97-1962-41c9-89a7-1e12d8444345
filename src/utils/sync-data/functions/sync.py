from dbt.cli.main import dbtRunner, dbtRunnerResult
import calendar


# Define the function to run dbt for each day in the given month and year
def run_dbt_for_month(year, month, from_day):
    # Get the number of days in the given month and year
    num_days = calendar.monthrange(year, month)[1]

     # initialize
    dbt = dbtRunner()

    # Loop through all days in the month
    for day in range(from_day, num_days + 1): # CHANGE THIS ACCORDINGLY
        
        # Format the date as YYYY-MM-DD
        run_date = f"{year:04d}-{month:02d}-{day:02d}"

        # # Create CLI args as a list of strings
        cli_args = [
            "build",
            "--project-dir",
            "analytics",
            "--select",
            "billable_counts_per_partner_daily",  # CHANGE TO THE MODEL REQUIRED
            "--target",
            "fb5",  # CHANGE THIS TO PROD
            "--vars",
            f"as_of_run_date: '{run_date}'",
        ]

        # Print the command for logging purposes
        print(f"Running dbt with as_of_run_date: {run_date}")

        # Run the command
        try:
            # run the command
            res: dbtRunnerResult = dbt.invoke(cli_args)

            if res is None:
                raise ValueError("No result returned from DBT invoke.")
        except Exception as e:
            print(f"Error running dbt for date {run_date}: {e}")