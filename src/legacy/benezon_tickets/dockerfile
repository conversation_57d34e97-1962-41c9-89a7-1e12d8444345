# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /usr/src/app

# Install any needed dependencies
RUN pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client google-cloud-storage requests pandas openpyxl

RUN mkdir -p /usr/src/app/config

# Copy the profile and required files into the container
COPY ./src/config/gmail-authentication-token.json /usr/src/app/config/gmail-authentication-token.json
COPY ./src/config/gmail-api-credentials.json /usr/src/app/config/gmail-api-credentials.json
COPY ./src/benezon_tickets/main.py /usr/src/app/main.py

CMD [ "python3", "main.py" ]
