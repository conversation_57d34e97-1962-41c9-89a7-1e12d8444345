import os
import base64
import pandas as pd
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google.cloud import storage
from datetime import datetime, timedelta

SCOPES = ["https://www.googleapis.com/auth/gmail.readonly"]

# Authenticate and return Gmail API service
def authenticate_gmail():
    creds = None
    if os.path.exists("./config/gmail-authentication-token.json"):
        creds = Credentials.from_authorized_user_file(
            "./config/gmail-authentication-token.json", SCOPES
        )
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                "./config/gmail-api-credentials.json",
                SCOPES,
            )
            creds = flow.run_local_server(port=8080)
        with open("./config/gmail-authentication-token.json", "w") as token:
            token.write(creds.to_json())
    return creds


# Search for messages with attachments
def search_messages(service, user_id, search_string):
    try:
        search_id = (
            service.users().messages().list(userId=user_id, q=search_string).execute()
        )
        number_results = search_id["resultSizeEstimate"]

        final_list = []
        if number_results > 0:
            message_ids = search_id["messages"]
            for ids in message_ids:
                final_list.append(ids["id"])
            return final_list
        else:
            return []

    except HttpError as error:
        print(f"An error occurred: {error}")
        return []


# Download attachments and return file paths
def get_attachments(service, user_id, msg_id, store_dir):
    file_paths = []
    try:
        message = service.users().messages().get(userId=user_id, id=msg_id).execute()
        for part in message["payload"]["parts"]:
            if part["filename"]:
                if "data" in part["body"]:
                    data = part["body"]["data"]
                else:
                    att_id = part["body"]["attachmentId"]
                    att = (
                        service.users()
                        .messages()
                        .attachments()
                        .get(userId=user_id, messageId=msg_id, id=att_id)
                        .execute()
                    )
                    data = att["data"]
                file_data = base64.urlsafe_b64decode(data.encode("UTF-8"))
                path = os.path.join(store_dir, part["filename"])

                with open(path, "wb") as f:
                    f.write(file_data)
                print(f"Attachment {part['filename']} saved to {store_dir}")
                file_paths.append(path)

    except HttpError as error:
        print(f"An error occurred: {error}")
    return file_paths


# Upload file to GCP bucket
def upload_to_gcp_bucket(
    bucket_name, source_file_name, destination_blob_name, processing_date
):
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(f"ProcessingDate={processing_date}/{destination_blob_name}")
    blob.upload_from_filename(source_file_name)
    print(
        f"File {source_file_name} uploaded to {destination_blob_name} in bucket {bucket_name}."
    )


def convert_from_excel_to_csv(src_file, output_file):
    excel_reader = pd.read_excel(src_file)
    excel_reader.to_csv(output_file, index=False)

    print(f"Successfully converted from XLSX to CSV. File: {output_file}")

def remove_column_spaces(file_path):
    df = pd.read_excel(file_path)

    new_cols = {}
    for c in df.columns:
        new_cols[c] = c.replace(" ", "").replace("-", "")


def main():
    creds = authenticate_gmail()
    try:
        service = build("gmail", "v1", credentials=creds)

        user_id = os.environ["USER_ID"]
        email_sender = os.environ["EMAIL_SENDER"]

        today = datetime.now().strftime("%Y/%m/%d")
        tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y/%m/%d")

        search_string = f"FROM {email_sender} has:attachment filename:*.xlsx after:{today} before:{tomorrow}"

        msg_ids = search_messages(service, user_id, search_string)

        store_dir = "attachments/"

        if not os.path.exists(store_dir):
            os.makedirs(store_dir)

        for msg_id in msg_ids:
            file_paths = get_attachments(service, user_id, msg_id, store_dir)
            for file_path in file_paths:
                current_date = datetime.now().strftime("%Y%m%d")
                new_file_name = f"benezon_ticket_data_{current_date}.csv"
                new_file_path = os.path.join(store_dir, new_file_name)

                remove_column_spaces(file_path)
                convert_from_excel_to_csv(file_path, new_file_path)

                bucket_name = os.environ["BUCKET_NAME"]
                destination_blob_name = os.path.basename(new_file_path)

                upload_to_gcp_bucket(
                    bucket_name,
                    new_file_path,
                    destination_blob_name,
                    datetime.now().strftime("%Y-%m-%d"),
                )

                # Optionally, delete the file after upload
                os.remove(new_file_path)
                os.remove(file_path)

                print(f"Temporary file {new_file_path} deleted.")
                print(f"Temporary file {file_path} deleted.")

    except HttpError as error:
        print(f"An error occurred: {error}")


if __name__ == "__main__":
    main()
