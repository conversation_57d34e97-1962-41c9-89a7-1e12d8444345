import os
import sys
from functions.run_dbt import (
    run_dbt,
)
command = os.environ["DBT_COMMAND"]
environment = os.environ["ENVIRONMENT_NAME"]
namespace = os.environ["NAMESPACE"]
slack_url = os.environ["SLACK_URL"]
project = os.environ["PROJECT_ID"]
batch_id = os.environ["BATCH_ID"]
batch_type = os.environ["BATCH_TYPE"]
container_name = os.environ["CONTAINER_NAME"]
container_region = os.environ["CONTAINER_REGION"]
exclude_tag = "" if "EXCLUDE_TAG" not in os.environ else os.environ["EXCLUDE_TAG"]
          
exit_code = run_dbt(command, environment, slack_url, project, batch_id, batch_type,
                    container_name, container_region, namespace, exclude_tag)

sys.exit(exit_code)
