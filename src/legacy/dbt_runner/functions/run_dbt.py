from dbt.cli.main import dbtRunner, dbtRunner<PERSON><PERSON>ult,RunExecutionResult
from google.oauth2 import service_account
from google.cloud import bigquery
from slack_sdk.webhook import WebhookClient
from datetime import datetime, timezone, timedelta
from dateutil.relativedelta import relativedelta
from typing import Tuple
import pandas_gbq
import pandas

def run_dbt(command: str, environment: str, slack_url: str, project: str, batch_id: str, batch_type: str,
            container_name: str, container_region: str, namespace: str, tag_to_exclude:str = ''):
    
    credentials = service_account.Credentials.from_service_account_file(
        f"/root/.dbt/{namespace}-{environment}-dw-key.json",)
    
    current_date_utc = datetime.now(tz=timezone.utc).date()
    
    row_exists, next_execution_date = get_next_execution_date(project, credentials, batch_id, batch_type)
        
    print(f"DEBUG: Bigquery returned: {next_execution_date.strftime('%Y-%m-%d')}")
    
    if(next_execution_date > current_date_utc):
        
        print(f"Job already ran for {current_date_utc.strftime('%Y-%m-%d')}")
        
        return 0
    
    cli_args = []

    as_of_run_date = current_date_utc - relativedelta(months=1) if batch_type == "monthly" else next_execution_date
    
    as_of_run_date_str = as_of_run_date.strftime('%Y-%m-%d')
    
    cli_args = [
            command,
            "--project-dir",
            "analytics",
            "--select",
            f"tag:{batch_id}",
            "--target",
            environment,
            "--vars",
            f'{{"as_of_run_date": "{as_of_run_date_str}"}}',
        ]
    
    if(tag_to_exclude):
        cli_args.append("--exclude")
        cli_args.append(f"tag:{tag_to_exclude}")
    
    try:
        
        dbt = dbtRunner()
        
        res: dbtRunnerResult = dbt.invoke(cli_args)

        if res is None:
            raise ValueError("No result returned from DBT invoke.")

        if environment == "prod":
            if (not (res.success) ):
                if isinstance(res.result, RunExecutionResult):
                    errors = [x.message for x in res.result if x.status == 'error']
                    all_errors = ", ".join(er for er in errors)
                    raise Exception(all_errors)
                else:
                    raise Exception(res.exception)
            
            model_execution_result = determine_model_results(res)
            test_execution_result = determine_test_results(res)

            if model_execution_result[1] > 0 or test_execution_result[1] > 0:
                alert_blocks = generate_execution_summary(
                    model_execution_result[0],
                    model_execution_result[1],
                    test_execution_result[0],
                    test_execution_result[1],
                    environment,
                    project,
                    container_name,
                    container_region
                )

                send_slack_notification(slack_url, alert_blocks)
        
        # Update T_Batch_Control
        next_run_date = (next_execution_date + timedelta(days=1)).strftime('%Y-%m-%d')
        query = ""
        if row_exists:
            query = f"""
            UPDATE Warehouse.T_Batch_Control
            SET Batch_Control_Dt = '{next_run_date}'
            WHERE Batch_Id = '{batch_id}' AND Batch_Type = '{batch_type}'
            """
        else:
            query = f"""
            INSERT INTO Warehouse.T_Batch_Control (Batch_Id, Batch_Type, Batch_Control_Dt)
            VALUES ('{batch_id}', '{batch_type}', '{next_run_date}')
            """
        
        client = bigquery.Client.from_service_account_json(f"/root/.dbt/{namespace}-{environment}-dw-key.json")
        
        job = client.query(query)
        
        job.result()

        return 0
    
    except Exception as Argument:
        error_message = f"Exception during DBT invocation - {Argument}"

        print(error_message)
    
        alert_blocks = generate_dbt_run_exception_alert(
            error_message, environment, project, container_name, container_region
        )
        send_slack_notification(slack_url, alert_blocks)

        return 1
    
def send_slack_notification(slack_url: str, blocks: object):
    webhook = WebhookClient(slack_url)

    response = webhook.send(text="fallback", blocks=blocks)

    assert response.status_code == 200
    assert response.body == "ok"


def generate_execution_summary(
    ms_count: int,
    mf_count: int,
    ts_count: int,
    tf_count: int,
    environment: str,
    project: str,
    container_name: str,
    container_region: str
):
    return [
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": f"DBT models execution results in {environment}",
            },
        },
        {
            "type": "section",
            "fields": [
                {"type": "mrkdwn", "text": f"*Successful models:* {ms_count}"},
                {"type": "mrkdwn", "text": f"*Failed models:* {mf_count}"},
            ],
        },
        {"type": "divider"},
        {
            "type": "section",
            "fields": [
                {"type": "mrkdwn", "text": f"*Passed data_tests:* {ts_count}"},
                {"type": "mrkdwn", "text": f"*Failed data_tests:* {tf_count}"},
            ],
        },
        {"type": "divider"},
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "You can click the button to check the detailed view of the execution",
            },
            "accessory": {
                "type": "button",
                "text": {"type": "plain_text", "text": "Click to check logs"},
                "value": "click_me_123",
                "url": f"https://console.cloud.google.com/run/jobs/details/{container_region}/{container_name}/logs?project={project}",
                "action_id": "button-action",
            },
        },
    ]


def generate_dbt_run_exception_alert(
    error_message: str, environment: str, project: str, 
    container_name: str,
    container_region: str
):
    return [
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": f" :alert: Error while executing DBT models in {environment}",
            },
        },
        {
            "type": "section",
            "fields": [
                {"type": "mrkdwn", "text": f"*Error message*\n {error_message}"}
            ],
        },
        {"type": "divider"},
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "You can click the button to check the detailed view of the execution",
            },
            "accessory": {
                "type": "button",
                "text": {"type": "plain_text", "text": "Click to check logs"},
                "value": "click_me_123",
                "url": f"https://console.cloud.google.com/run/jobs/details/{container_region}/{container_name}/logs?project={project}",
                "action_id": "button-action",
            },
        },
    ]


def determine_model_results(dbt_result: dbtRunnerResult):
    ms_count = 0
    mf_count = 0
    
    try:
        iter(dbt_result.result)
        print("dbt_result.result is iterable! ")
    except:
        print("dbt_result.result is not iterable! ")
    
            
    for r in dbt_result.result:
        if r.node.resource_type == "model":
            if r.status == "success":
                ms_count = ms_count + 1
            else:
                mf_count = mf_count + 1

    return ms_count, mf_count


def determine_test_results(dbt_result: dbtRunnerResult):
    ts_count = 0
    tf_count = 0

    for r in dbt_result.result:
        if r.node.resource_type == "test":
            if r.status == "pass":
                ts_count = ts_count + 1
            else:
                tf_count = tf_count + 1

    return ts_count, tf_count

def get_next_execution_date(project: str, credentials: str, batch_id: str, batch_type: str) -> Tuple[bool,datetime.date]:
    sql_statement = f"""
    SELECT Batch_Control_Dt as Batch_Control_Dt FROM Warehouse.T_Batch_Control
    WHERE Batch_Id = '{batch_id}' and Batch_Type = '{batch_type}'
    """
    df = pandas_gbq.read_gbq(sql_statement, project_id=project, credentials=credentials)
    
    if (df['Batch_Control_Dt'].count() > 0):
        return True, df['Batch_Control_Dt'].iloc[0]
    else: 
        return False, datetime.strptime("2024-11-01", "%Y-%m-%d").date()
