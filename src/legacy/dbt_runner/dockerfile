# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /usr/src/app



RUN mkdir -p /usr/src/app/analytics

# Copy the profile and required files into the container
COPY ./src/dbt_runner/requirements.txt /usr/src/app/requirements.txt
# Install any needed dependencies
RUN pip install -r requirements.txt
COPY ./src/config/profiles.yml /root/.dbt/profiles.yml
COPY ./src/keys/* /root/.dbt/
COPY /Analytics/ /usr/src/app/analytics
COPY ./src/dbt_runner/functions/run_dbt.py /usr/src/app/functions/run_dbt.py
COPY ./src/dbt_runner/main.py /usr/src/app/main.py

RUN dbt deps --project-dir /usr/src/app/analytics

COPY ./src/dbt_runner/ddtrace_requirements.txt /usr/src/app/ddtrace_requirements.txt
COPY --from=datadog/serverless-init:1 /datadog-init /app/datadog-init
RUN pip install -r ddtrace_requirements.txt --target /dd_tracer/python/
ENV DD_SERVICE=dbt_runner
ENTRYPOINT ["/app/datadog-init"]
CMD ["/dd_tracer/python/bin/ddtrace-run", "python3", "main.py"]
