import pandas as pd
import os
from datetime import datetime
from google.cloud import storage

def export_allied_tables_to_GCS():

    current_date = datetime.now()
    current_date_string = current_date.strftime("%Y%m%d")

    gcs_bucket = os.environ['GCS_BUCKET_ALLIED']
    bq_dataset = os.environ['BQ_DATASET_ID']
    bq_elig_table = os.environ['BQ_ELIG_TABLE_ID']
    bq_claims_table = os.environ['BQ_CLAIMS_TABLE_ID']
    bq_accums_table = os.environ['BQ_ACCUMS_TABLE_ID']
    bq_gpf_grps_to_nw_table = os.environ['BQ_GPF_GROUPSTONETWORKS_TABLE_ID']
    bq_gpf_grps_to_modules = os.environ['BQ_GPF_GROUPSTOMODULES_TABLE_ID']
    bq_gpf_grp_and_plan_documents = os.environ['BQ_GPF_GROUPSANDPLAN_DOCUMENTS_TABLE_ID']
    project_id = os.environ['PROJECT_ID']

    storage_client = storage.Client()
    bucket = storage_client.bucket(gcs_bucket)

    #Export elig data
    df = pd.read_gbq("""select * from `"""+project_id+"""."""+bq_dataset+"""."""+bq_elig_table+"""`"""
                     ,project_id = project_id)
    
    df.to_csv("./"+current_date_string+"_allied_member_benefits.csv" , index = False, sep='|')

    blob = bucket.blob(current_date_string+"_allied_member_benefits.csv")
    blob.upload_from_filename("./"+current_date_string+"_allied_member_benefits.csv")

    #Export claims data
    df = pd.read_gbq("""select * from `"""+project_id+"""."""+bq_dataset+"""."""+bq_claims_table+"""`"""
                     ,project_id = project_id)
    
    df.to_csv(
        "./" + current_date_string + "_allied_orphaned_claims.csv", index=False, sep="|"
    )
    blob = bucket.blob(current_date_string+"_allied_orphaned_claims.csv")
    blob.upload_from_filename("./"+current_date_string+"_allied_orphaned_claims.csv")

    #Export accums data
    df = pd.read_gbq("""select * from `"""+project_id+"""."""+bq_dataset+"""."""+bq_accums_table+"""`"""
                     ,project_id = project_id)
    
    df.to_csv(
        "./" + current_date_string + "_allied_orphaned_accums.csv", index=False, sep="|"
    )

    blob = bucket.blob(current_date_string+"_allied_orphaned_accums.csv")
    blob.upload_from_filename(current_date_string+"_allied_orphaned_accums.csv")

    #Export groupstobenefits_and_networks data
    df = pd.read_gbq("""select * from `"""+project_id+"""."""+bq_dataset+"""."""+bq_gpf_grps_to_nw_table+"""`"""
                     ,project_id = project_id)
    
    df.to_csv(
        "./" + current_date_string + "_allied_groups_to_networks.csv",
        index=False,
        sep="|",
    )

    blob = bucket.blob(current_date_string+"_allied_groups_to_networks.csv")
    blob.upload_from_filename(current_date_string+"_allied_groups_to_networks.csv")

    #Export groupstomodules data
    df = pd.read_gbq("""select * from `"""+project_id+"""."""+bq_dataset+"""."""+bq_gpf_grps_to_modules+"""`"""
                     ,project_id = project_id)
    
    df.to_csv(
        "./" + current_date_string + "_allied_groups_to_modules.csv",
        index=False,
        sep="|",
    )

    blob = bucket.blob(current_date_string+"_allied_groups_to_modules.csv")
    blob.upload_from_filename(current_date_string+"_allied_groups_to_modules.csv")

    #Export groups and plan docs data
    df = pd.read_gbq("""select * from `"""+project_id+"""."""+bq_dataset+"""."""+bq_gpf_grp_and_plan_documents+"""`"""
                     ,project_id = project_id)
    
    df.to_csv(
        "./" + current_date_string + "_allied_groups_and_plan_documents.csv",
        index=False,
        sep="|",
    )

    blob = bucket.blob(current_date_string+"_allied_groups_and_plan_documents.csv")
    blob.upload_from_filename(current_date_string+"_allied_groups_and_plan_documents.csv")
