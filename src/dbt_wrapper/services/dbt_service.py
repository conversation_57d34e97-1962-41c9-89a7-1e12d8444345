from dbt.cli.main import dbt<PERSON><PERSON><PERSON>, dbtRunner<PERSON><PERSON>ult,RunExecutionResult
from dbt_common.events.base_types import EventMsg
from dbt_common.events.functions import msg_to_dict
from dbt_common.utils.encoding import ForgivingJSONEncoder
import json
import os

class DbtService:
    def __init__(self, batch_control_id:str=None, batch_control_dt:str=None):
        self.Environment = os.environ["ENVIRONMENT_NAME"]
        self.batch_control_id = batch_control_id
        self.batch_control_dt = batch_control_dt
        
    def Build(self, select_tag:str, exclude_tag:str, **variables) -> bool:
        
        cli_args = self.__build_dbt_command(False, select_tag, exclude_tag, **variables)
         
        try:
            self.__execute_dbt(cli_args)
            return True
        except Exception as e:
            error_message = f"Exception during DBT invocation - {e}"
            print(error_message)
            return False
            
    def Full_Refresh_Build(self, select_tag:str, exclude_tag:str, **variables) -> bool:
        cli_args = self.__build_dbt_command(True, select_tag, exclude_tag, **variables)
         
        try:
            self.__execute_dbt(cli_args)
            return True
        except Exception as e:
            error_message = f"Exception during DBT invocation - {e}"
            print(error_message)
            return False
    
    def __build_dbt_command(self, isFullRefresh:bool, select_tag:str, exclude_tag:str, **variables) -> list: 
        cli_args = [
                "--log-format",
                "json",
                "build",
                "--project-dir",
                "analytics",
                "--target",
                self.Environment,
        ]
        if(isFullRefresh):
            cli_args.append("--full-refresh")
            
        if(select_tag):
            cli_args.append("--select")
            cli_args.append(select_tag)
        
        if(exclude_tag):
            cli_args.append("--exclude")
            cli_args.append(' '.join(['tag:' + x for x in exclude_tag.split(',')]))
        
        if variables:
            cli_args.append("--vars")
            for key, value in variables.items():
                cli_args.append(f'{{"{key}": "{value}"}}')
        
        return cli_args
    
    def __execute_dbt(self, cli_args):
        
        dbt = dbtRunner(callbacks=[self.__custom_log])
        
        res: dbtRunnerResult = dbt.invoke(cli_args)
        
        if res is None:
            raise ValueError("No result returned from DBT invoke.")
        
        if (not (res.success) ):
            if isinstance(res.result, RunExecutionResult):
                errors = [x.message for x in res.result if x.status == 'error']
                all_errors = ", ".join(er for er in errors)
                raise Exception(all_errors)
            else:
                raise Exception(res.exception)
    
    def __custom_log(self, event: EventMsg):
        if (event.info.name in ['MainReportVersion','AdapterRegistered','UnableToPartialParse',
                                'UnusedResourceConfigPath','Formatting','FoundStats','LogStartLine',
                                'ConcurrencyLine','LogSnapshotResult','EndOfRunSummary',
                                'FinishedRunningStats','StatsLine','LogModelResult','LogSeedResult',
                                'LogTestResult']):
            
            
            msg_dict = msg_to_dict(event)
            if(
                'node_info' in msg_dict["data"] and
                'node_started_at' in msg_dict["data"]["node_info"] and
                (msg_dict["data"]["node_info"]["node_finished_at"] != '')
                ):
                data_dict = {}
                data_dict["consolidated"] = "YES"
                data_dict["invocation_id"]=msg_dict["info"]["invocation_id"]
                data_dict["layer_name"] = "Data Warehouse" if "DataWarehouse" in self.batch_control_id else "Data Mart"
                data_dict["dbt_model_path"] = msg_dict["data"]["node_info"]["node_path"]
                data_dict["table_or_view_name"] = msg_dict["data"]["node_info"]["node_name"]
                data_dict["start_time_ts"] = msg_dict["data"]["node_info"]["node_started_at"]
                data_dict["end_time_ts"] = msg_dict["data"]["node_info"]["node_finished_at"]
                data_dict["num_rows"] = msg_dict["info"]["msg"]
                data_dict["process_run_status"] = msg_dict["data"]["node_info"]["node_status"]
                data_dict["batch_control_id"] = self.batch_control_id
                data_dict["batch_control_dt"] = self.batch_control_dt
                print(json.dumps(data_dict, sort_keys=True, cls=ForgivingJSONEncoder))    
                
            
            