from flask import request, jsonify
from apis import app
from services.dbt_service import DbtService
import os

@app.route("/", methods=["GET"])
def get_heart_beat():
    return "dbt wrapper API is up and running"


@app.route('/get_env_vars', methods=["GET"])
def get_env_vars():
    return jsonify({
        "project_id": os.environ["PROJECT_ID"],
        "environment_name": os.environ["ENVIRONMENT_NAME"]
    })

@app.route("/dbt_build", methods=["POST"])
def dbt_build():
    
    request_data = request.get_json()
    select_tag = request_data["select_tag"] if "select_tag" in request_data else None
    exclude_tag = request_data["exclude_tag"] if "exclude_tag" in request_data else None
    batch_control_id = request_data["batch_control_id"] if "batch_control_id" in request_data else None
    batch_control_dt = request_data["batch_control_dt"] if "batch_control_dt" in request_data else None
    
    dbt_service = DbtService(batch_control_id, batch_control_dt)
    
    
    result = None
    if("as_of_run_date" in request_data):
        as_of_run_date = request_data["as_of_run_date"]
        result = dbt_service.Build(select_tag,exclude_tag, as_of_run_date=as_of_run_date)
    else:
        result = dbt_service.Build(select_tag,exclude_tag)
    
    if(result):
        return "Success", 200
    else:
        return "Failure", 500
    
    
    
    