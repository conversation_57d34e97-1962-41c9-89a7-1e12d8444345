FROM python:3.10-slim
WORKDIR /usr/src/app
RUN mkdir -p /usr/src/app/analytics
COPY ./src/dbt_wrapper/requirements.txt /usr/src/app/requirements.txt
RUN pip install -r requirements.txt
COPY ./src/config/profiles.yml /root/.dbt/profiles.yml
COPY ./src/keys/* /root/.dbt/
COPY /Analytics/ /usr/src/app/analytics
COPY ./src/dbt_wrapper/run.py /usr/src/app/run.py
COPY ./src/dbt_wrapper/apis/* /usr/src/app/apis/
COPY ./src/dbt_wrapper/services/* /usr/src/app/services/

RUN dbt deps --project-dir /usr/src/app/analytics

COPY ./src/dbt_wrapper/ddtrace_requirements.txt /usr/src/app/ddtrace_requirements.txt
COPY --from=datadog/serverless-init:1 /datadog-init /app/datadog-init
RUN pip install -r ddtrace_requirements.txt --target /dd_tracer/python/

ENV DD_SERVICE=dbt_wrapper

EXPOSE 8080

ENTRYPOINT ["/app/datadog-init"]
CMD ["/dd_tracer/python/bin/ddtrace-run", "python3", "run.py"]
