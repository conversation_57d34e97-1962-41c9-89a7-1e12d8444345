parameters:
  - name: secureFileName
    type: string

  - name: stepName
    type: string

steps:
  - task: DownloadSecureFile@1
    name: ${{parameters.stepName}}
    displayName: "Download AlliedDigital BigQuery connection key"
    inputs:
      secureFile: ${{parameters.secureFileName}}

  - task: Bash@3
    displayName: "Moving BigQuery KEY file"
    inputs:
      targetType: "inline"
      script: |
        ls -l
        mkdir -p src/keys
        mv $(${{parameters.stepName}}.secureFilePath) src/keys/${{parameters.secureFileName}}
