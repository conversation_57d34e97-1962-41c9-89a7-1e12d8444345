version: 2

macros:
  - name: generate_schema_name
    description: |
      Purpose:
      This macro overrides dbt's default schema generation behavior to implement environment-specific
      schema naming conventions. It ensures consistent schema naming across different deployment environments.
      
      Usage:
      This macro is called automatically by dbt when resolving schema names. It doesn't need to be
      explicitly invoked in models.
      
      Behavior:
      - In the 'local' environment: Appends the custom schema name to the target schema with an underscore
      - In other environments: Uses the custom schema name as provided
      - When no custom schema is specified: Uses the default target schema
    
    arguments:
      - name: custom_schema_name
        type: string
        description: "The custom schema name specified in a model's 'schema' config"
      
      - name: node
        type: object
        description: "The dbt node object representing the model"
    
    returns: |
      String containing the resolved schema name based on environment and configuration.
    
    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      layer: "all"
      category: "dbt_core"
