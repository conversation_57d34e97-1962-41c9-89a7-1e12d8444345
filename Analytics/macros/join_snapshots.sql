{% macro join_snapshots(
    cte_join, 
    cte_join_on, 
    cte_join_valid_to,
    cte_join_valid_from, 
    cte_join_on_valid_to, 
    cte_join_on_valid_from,
    cte_join_id, 
    cte_join_on_id,
    cte_join_on_values_to_get) 
%}
        {{ cte_join}}.*,
        {%- for other_columns in cte_join_on_values_to_get %}
            {%- for key,value in other_columns.items() %}

                {{ cte_join_on}}.{{key}} as {{value}},
        
            {% endfor %}
        {% endfor %}
        greatest({{cte_join}}.{{cte_join_valid_from}},
                    coalesce( 
                        {{cte_join_on}}.{{cte_join_on_valid_from}}, 
                        {{cte_join}}.{{cte_join_valid_from}}
                        )
                )
                AS add_{{cte_join_on}}_valid_from,
        least({{cte_join}}.{{cte_join_valid_to}},
            coalesce(
                {{cte_join_on}}.{{cte_join_on_valid_to}}, 
                {{cte_join}}.{{cte_join_valid_to}}
                    )
            ) AS add_{{cte_join_on}}_valid_to
  
   FROM {{cte_join}}
   LEFT JOIN {{cte_join_on}} ON {{cte_join}}.{{cte_join_id}} = {{cte_join_on}}.{{cte_join_on_id}}
      AND ({{cte_join_on}}.{{cte_join_on_valid_from}} <= {{cte_join}}.{{cte_join_valid_to}}
      AND {{cte_join_on}}.{{cte_join_on_valid_to}} >= {{cte_join}}.{{cte_join_valid_from}})
  
{% endmacro %}