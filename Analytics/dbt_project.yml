name: 'Analytics'
version: '1.0.0'
config-version: 2

vars:
  as_of_run_date: "{{ var('today_date', modules.datetime.date.today().strftime('%Y-%m-%d')) }}"
  future_proof_date: '9999-12-31'
  past_init_date: '2000-12-31'

require-dbt-version: ">=1.8.7"

profile: 'Analytics'

model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

clean-targets:
  - "target"
  - "dbt_packages"


models:
  Analytics:
    ods:
      +schema: ODS
    warehouse:
      +schema: Warehouse
    silver:
      +schema: Silver
    gold:
      +schema: Gold
    legacy:
      +schema: Legacy
    intermediate:
      +schema: Warehouse

snapshots:
  Analytics:
    +target_schema: Warehouse

seeds:
  Analytics:
    +schema: Warehouse