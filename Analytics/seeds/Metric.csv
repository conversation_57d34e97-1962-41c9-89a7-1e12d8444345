Metric_Id,Subject_Area,Metric_Type,Metric_Sub_Type,Metric_Name,Metric_Business_Rules,DBT_Model_Name,Metric_Version
1,Eligibility,Subscriber,Registered Count,Total_Registered_Subscriber_Cnt,,,1
2,Eligibility,Subscriber,Total Count,Total_Subscriber_Cnt,,,
3,Eligibility,Dependent,Registered Count,Total_Registered_Dependent_Cnt,,,
4,Eligibility,Dependent,Total Count,Total_Dependent_Cnt,,,
5,Engagement,Subscriber,Registered Logins on Member Portal,Number of Subscriber Logins,,,
6,Engagement,Dependent,Registered Logins on Member Portal,Number of Dependent Logins,,,
7,Eligibility,Subscriber+Dependent,Registered Count,Total_Registered_Cnt,,,
8,Billing,Subscriber,Billable Count,Total_Billable_Cnt,,,
9,Elig_Reg_Sub_Dep,Subscriber,Total Count,Total_Elig_Subscriber_Cnt,,,
10,Elig_Reg_Sub_Dep,Subscriber,Registered Count,Total_Elig_Registered_Subscriber_Cnt,,,
11,Elig_Reg_Sub_Dep,Dependent,Total Count,Total_Elig_Dependent_Cnt,,,
12,Elig_Reg_Sub_Dep,Dependent,Registered Count,Total_Elig_Registered_Dependent_Cnt,,,
13,Re-Engagements,Subscriber,Total Count Daily,Total_Subscriber_ReEngagements_Daily,,,
14,Re-Engagements,Dependent,Total Count Daily,Total_Dependent_ReEngagements_Daily,,,
15,Re-Engagements,Subscriber,Total Unique Count Daily,Total_Subscriber_ReEngagements_Daily_Unique,,,
16,Re-Engagements,Dependent,Total Unique Count Daily,Total_Dependent_ReEngagements_Daily_Unique,,,
17,Re-Engagements,Subscriber,Total Count MTD,Total_Subscriber_ReEngagements_MTD,,,
18,Re-Engagements,Dependent,Total Count MTD,Total_Dependent_ReEngagements_MTD,,,
19,Re-Engagements,Subscriber,Total Unique Count MTD,Total_Subscriber_ReEngagements_MTD_Unique,,,
20,Re-Engagements,Dependent,Total Unique Count MTD,Total_Dependent_ReEngagements_MTD_Unique,,,
21,Re-Engagements,Subscriber,Total Unique Count LTD,Total_Subscriber_ReEngagements_LTD_Unique,,,
22,Re-Engagements,Dependent,Total Unique Count LTD,Total_Dependent_ReEngagements_LTD_Unique,,,
23,Re-Engagements,Subscriber,Total Unique Count YTD,Total_Subscriber_ReEngagements_YTD_Unique,,,
24,Re-Engagements,Dependent,Total Unique Count YTD,Total_Dependent_ReEngagements_YTD_Unique,,,
