version: 2

seeds:
  - name: age_groups
    description: >
      This seed file contains the mapping of individual ages to their corresponding
      age group classifications. It provides two different grouping strategies:
      Age_Group_1 for detailed age bands and Age_Group_2 for broader age categories.

    config:
      materialized: seed
      tags: ["MxDataMartDailyBuild"]

    columns:
      - name: Age
        description: "Individual age value (0-151)"

      - name: Age_Group_1
        description: "Primary age grouping with detailed bands"

      - name: Age_Group_2
        description: "Secondary age grouping with broader bands"

      - name: Created_dt_ts
        description: "Timestamp when the record was created"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"

      - name: Created_By
        description: "User or process that created the record"

      - name: Updated_By
        description: "User or process that last updated the record"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2024-01-24"
      table_type: "seed"
      refresh_frequency: "as_needed"
      business_rules:
        - "Each age must have exactly one mapping to Age_Group_1 and Age_Group_2"
        - "Age values must be between 0 and 151"
        - "Age_Group_1 provides detailed age bands (e.g., 5-year increments)"
        - "Age_Group_2 provides broader age categories"
        - "Age groups must be mutually exclusive and collectively exhaustive"
        - "Age group ranges must be continuous with no gaps"
        - "Age_Group_1 bands: 0-1, 1-13, 13-18, 18-24, 25-34, 35-44, 45-54, 55-64, 65-74, 75-120, 120+"
        - "Age_Group_2 bands: 0-1, 1-17, 18-60, 61-100, 101-149, 149+"

