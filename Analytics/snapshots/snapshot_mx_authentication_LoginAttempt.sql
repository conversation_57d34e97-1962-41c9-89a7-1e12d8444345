{% snapshot mx_authentication_LoginAttempt %}
    {{
        config(
        strategy='check',
        unique_key='Id',
        check_cols=[
            'UserId',
            'Username',
            'Context',
            'Success',
            'Active',
            'Reason'
        ],
        tags = ["MxDataWarehouseDailyBuild","decouple_from_datamart"],
        invalidate_hard_deletes=True,
        partition_by={
            "field":"dbt_valid_to",
            "data_type": "timestamp",
            "granularity": "day"
            }
        )
    }}
    SELECT 
        *
    FROM 
        {{ source(
            'ODS',
            'mx_authentication_LoginAttempt'
        ) }}
{% endsnapshot %}