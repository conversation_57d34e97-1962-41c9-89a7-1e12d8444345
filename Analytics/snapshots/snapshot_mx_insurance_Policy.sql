{% snapshot mx_insurance_Policy %}
    {{
        config(
        strategy='check',
        unique_key='Id',
        check_cols=[
            'Active',
            'UserId',
            'EligibilityId',
            'PlanId',
            'FirstName',
            'LastName',
            'MemberNumber',
            'PersonNumber',
            'DateOfBirth',
            'Gender',
            'EffectiveFrom',
            'EffectiveTo',
            'Coverage',
            'PersonId',
            'ParentPolicyId',
            'Relationship',
            'ParentPersonId',
            'AdditionalMonthsOfAccessAfterTermination'
        ],
        invalidate_hard_deletes=True,
        tags = ["MxDataWarehouseDailyBuild","decouple_from_datamart"],
        partition_by={
            "field":"dbt_valid_to",
            "data_type": "timestamp",
            "granularity": "day"
            }
        )
    }}
    SELECT 
        *
    FROM 
        {{ source(
            'ODS',
            'mx_insurance_Policy'
        ) }}
{% endsnapshot %}