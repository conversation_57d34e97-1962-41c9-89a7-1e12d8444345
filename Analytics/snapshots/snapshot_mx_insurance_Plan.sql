{% snapshot mx_insurance_Plan %}
    {{
        config(
        strategy='check',
        unique_key='Id',
        check_cols=[
            'Active',
            'PayerId',
            'Name',
            'ExternalId',
            'Type',
            'PlanImageUri',
            'RxPcn',
            'RxBin',
            'RxGroup',
            'ClaimSubmittalAddressLine1',
            'ClaimSubmittalAddressLine2',
            'ClaimSubmittalCity',
            'ClaimSubmittalState',
            'ClaimSubmittalPostalCode',
            'EdiPayerId',
            'MemberSupportPhoneNumber',
            'ProviderSupportPhoneNumber',
            'GroupId',
            'GroupNumber',
            'SbcUri',
            'FamilyAccumulatorsMustBeMetForFamilies',
            'SpdUri',
            'PrefixNote',
            'SuffixNote',
            'HidePolicyIdentifiers',
            'MaskMemberNumbers',
            'AdditionalMonthsOfAccessAfterTermination'
        ],
        invalidate_hard_deletes=True,
        tags = ["MxDataWarehouseDailyBuild","decouple_from_datamart"],
        partition_by={
            "field":"dbt_valid_to",
            "data_type": "timestamp",
            "granularity": "day"
            }
        )
    }}
    SELECT 
        *
    FROM 
        {{ source(
            'ODS',
            'mx_insurance_Plan'
        ) }}
{% endsnapshot %}