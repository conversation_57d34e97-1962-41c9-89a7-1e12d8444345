{% snapshot mx_fileprocessing_Eligibility %}
    {{
        config(
        strategy='check',
        unique_key='Id',
        check_cols=[
            'MemberNumber',
            'PersonNumber',
            'GroupNumber',
            'PlanId',
            'DateOfBirth',
            'EffectiveDate',
            'TerminationDate',
            'Relationship',
            'Active',
        	'BenefitClassCode',
            'BenefitElections',
            'Benefits',
            'DirectoryId',
            'DocumentIds',
            'EnrSeqNumber',
            'FlexFlag',
            'IDCardRecordId',
            'MemDepCode',
            'PaperlessEobElectionFlag',
            'SapphireAccount',
            'SapphireReportingPlanId',
            'SapphireTwoFact'
            ],
        invalidate_hard_deletes=True,
        tags = ["MxDataWarehouseDailyBuild","decouple_from_datamart"],
        partition_by={
            "field":"dbt_valid_to",
            "data_type": "timestamp",
            "granularity": "day"
            }
        )
    }}

    SELECT 
        *
    FROM 
        {{ 
            ref('stg_mx_fileprocessing_Eligibility')
         }}
{% endsnapshot %}