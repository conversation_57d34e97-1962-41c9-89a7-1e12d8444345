{% snapshot mx_insurance_Benefit %}
    {{
        config(
        strategy='check',
        unique_key='Id',
        check_cols=[
            'Active',
            'PlanId',
            'BenefitInformationCode',
            'CoverageLevel',
            'ServiceTypeCode',
            'InsuranceTypeCode',
            'TimePeriodQualifier',
            'MonetaryValue',
            'Percent',
            'QuantityQualifier',
            'Quantity',
            'AuthorizationIndicator',
            'NetworkTier',
            'DeductibleApplies'
        ],
        invalidate_hard_deletes=True,
        tags = ["MxDataWarehouseDailyBuild","decouple_from_datamart"],
        partition_by={
            "field":"dbt_valid_to",
            "data_type": "timestamp",
            "granularity": "day"
            }
        )
    }}
    SELECT 
        *
    FROM 
        {{ source(
            'ODS',
            'mx_insurance_Benefit'
        ) }}
{% endsnapshot %}