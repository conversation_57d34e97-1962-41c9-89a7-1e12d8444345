{% snapshot mx_providersearch_PlanTierRibbonInsurance %}
    {{
        config(
        strategy='check',
        unique_key='Id',
        check_cols=[
            'PlanId',
            'Tier',
            'RibbonInsuranceId',
            'Active'
        ],
        invalidate_hard_deletes=True,
        tags = ["MxDataWarehouseDailyBuild","decouple_from_datamart"],
        partition_by={
            "field":"dbt_valid_to",
            "data_type": "timestamp",
            "granularity": "day"
            }
        )
    }}
    SELECT 
        *
    FROM 
        {{ source(
            'ODS',
            'mx_providersearch_PlanTierRibbonInsurance'
        ) }}
{% endsnapshot %}