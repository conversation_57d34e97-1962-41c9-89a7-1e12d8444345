{% snapshot mx_insurance_Group %}
    {{
        config(
        strategy='check',
        unique_key='Id',
        check_cols=[
            'Active',
            'Name',
            'Phone',
            'AddressLine1',
            'AddressLine2',
            'City',
            'State',
            'PostalCode',
            'CountryCode',
            'BillingCycleDay',
            'StartDate',
            'TerminationDate',
            'ExternalId',
            'BrandKey',
            'Frozen'
        ],
        invalidate_hard_deletes=True,
        tags = ["MxDataWarehouseDailyBuild","decouple_from_datamart"],
        partition_by={
            "field":"dbt_valid_to",
            "data_type": "timestamp",
            "granularity": "day"
            }
        )
    }}
    SELECT 
        *
    FROM 
        {{ source(
            'ODS',
            'mx_insurance_Group'
        ) }}
{% endsnapshot %}