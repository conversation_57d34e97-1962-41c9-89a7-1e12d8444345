# Data Warehouse Architecture & Standards Guide

## 1. Environment Overview

### Technology Stack
- **BigQuery**
  - Primary data platform chosen for its scalability and cost-effective storage of large datasets
  - Supports both real-time analytics and batch processing
  - Native integration with Google Cloud Platform (GCP) services

- **DBT (Data Build Tool)**
  - SQL-first transformation framework that enables version control of data transformations
  - Provides testing, documentation, and dependency management
  - Allows modular development with reusable code blocks
  - Implements software engineering best practices in data transformation

- **Python**
  - Used for orchestration and automation
  - Handles complex transformations that are difficult in SQL
  - Provides flexibility for custom data processing needs

### Data Layers

1. **ODS (Operational Data Store)**
   - Purpose: Serves as an intermediate landing zone for raw data
   - Refresh Frequency: Every 3 hours from MySQL
   - Why?: 
     - Minimizes impact on source systems
     - Provides a stable foundation for downstream processing
     - Maintains data in its original form for auditing
   - Best Practices:
     - No transformations at this layer
     - Maintain source system column names
     - Include audit columns for tracking data lineage

2. **Warehouse Layer (DW)**
   - Purpose: Standardized, historical view of enterprise data
   - Key Features:
     - Implements Slowly Changing Dimensions (SCD) Type 2
     - Maintains historical snapshots
     - Standardizes naming and data types
   - Why SCD Type 2?:
     - Tracks historical changes in dimension attributes
     - Enables point-in-time analysis
     - Supports compliance and audit requirements
   - Implementation:
     ```sql
     -- Example of SCD Type 2 implementation
     SELECT 
         *,
         dbt_valid_from,
         COALESCE(
             LEAD(dbt_valid_from) OVER (
                 PARTITION BY business_key 
                 ORDER BY dbt_valid_from
             ) - INTERVAL '1 day',
             NULL
         ) as dbt_valid_to
     FROM source_table
     ```

3. **Gold Layer (Data Mart)**
   - Purpose: Business-ready datasets optimized for reporting
   - Characteristics:
     - Denormalized where appropriate
     - Includes business logic and calculations
     - Optimized for query performance
   - Why Separate from DW?:
     - Isolates business logic from core data model
     - Enables different refresh patterns
     - Allows optimization for specific use cases

## 2. Key Design Principles

### Time Zone Standards
- **UTC Storage Requirement**
  - All timestamps must be stored in UTC
  - Rationale:
    - Eliminates daylight saving time complexity
    - Supports global operations
    - Ensures consistent time-based calculations
  - Implementation:
    ```sql
    -- Example of proper timestamp handling
    DATETIME(TIMESTAMP_TRUNC(
        CURRENT_TIMESTAMP(), 
        DAY, 
        'UTC'
    )) as standardized_timestamp
    ```

### Batch Control
The `T_BATCH_CONTROL` table is central to our data processing strategy:

```sql
CREATE TABLE `Warehouse.T_BATCH_CONTROL` (
    Batch_Id STRING,        -- e.g., 'MxDataMartDailyBuild'
    Batch_Type STRING,      -- e.g., 'FULL', 'INCREMENTAL'
    Batch_Control_Dt DATE,  -- Controls data selection
    Created_Dt TIMESTAMP,
    Updated_Dt TIMESTAMP
);
```

Why Batch Control?:
- Ensures data consistency across related tables
- Enables point-in-time reporting
- Facilitates incremental processing
- Supports data recovery and replay

Usage Example:
```sql
WITH batch_date AS (
    SELECT CAST(batch_control_dt AS DATE) AS batch_date
    FROM `Warehouse.T_BATCH_CONTROL`
    WHERE batch_id = 'MxDataMartDailyBuild'
)
SELECT 
    *
FROM source_table
WHERE date_field <= (SELECT batch_date FROM batch_date)
```

### Base Views Pattern
Base views serve as the foundation for all downstream processing:

Key Requirements:
1. **Batch Control Integration**
   ```sql
   -- Always reference batch control date
   WHERE processing_date <= (
       SELECT batch_control_dt 
       FROM T_BATCH_CONTROL 
       WHERE batch_id = 'MxDataMartDailyBuild'
   )
   ```

2. **SCD Type 2 Implementation**
   ```sql
   -- Handle historical tracking
   WHERE dbt_valid_to IS NULL  -- Current records only
   ```

3. **Active Record Filtering**
   ```sql
   -- Standard active record pattern
   WHERE is_deleted = FALSE
   AND status = 'ACTIVE'
   ```

## 3. Naming Conventions and Design Philosophy

### Project Structure Rationale
```
Analytics/
├── models/              # All DBT models
│   ├── ods/            # Raw data models
│   ├── warehouse/      # Core business entities
│   │   └── views/      # Reusable view layer
│   ├── silver/         # Intermediate transformations
│   │   ├── common/     # Common dimension and reference tables
│   │   └── member/     # Member-specific metrics and attributes
│   ├── intermediate/   # Processing views
│   │   └── views/      # Reusable intermediate views
│   └── gold/           # Business-ready datasets
├── seeds/              # Reference data
├── macros/            # Reusable code blocks
└── tests/             # Data quality checks
```

Why This Structure?:
- Separates concerns by data processing stage
- Enables clear dependency management
- Supports incremental development
- Facilitates code reuse
- Organizes models by business domain

### Layer-Based Naming Conventions

#### Bronze/ODS Layer (Source Data)
- **Tables**: No specific prefix, typically source system names
- **Views**: Prefixed with `V_` (e.g., `V_Base_User_DW`, `V_Eligibility_DW`)
- **External Tables**: Prefixed with `ext_` (e.g., `ext_GCS_EligibilitiesByDateRaw`)

#### Silver Layer (Transformed Data)
- **Dimension Tables**: Prefixed with `D_` (e.g., `D_State`, `D_Group`, `D_Partner`)
- **Fact Tables**: Prefixed with `F_` (e.g., `F_Member_Metrics`)
- **Views**: Prefixed with `V_` (e.g., `V_Billing_Partner`, `V_Engage_Partner`)

#### Gold Layer (Business-Ready Data)
- **Tables**: No specific prefix, business domain names
- **Views**: Prefixed with `V_` for views

#### Intermediate Layer (Processing Views)
- **Views**: Prefixed with `V_` and often include level indicators (e.g., `V_F_Member_Metrics_L1`, `V_F_Member_Metrics_L2`)

### Hierarchical Naming

Models often follow a hierarchical naming pattern to indicate relationships:
- **Level Indicators**: Suffixes like `_L1`, `_L2`, `_L3` indicate processing levels (e.g., `V_F_Member_Metrics_L1`, `V_F_Member_Metrics_L2`)
- **Domain Prefixes**: Business domain indicators in names (e.g., `V_Billing_Partner`, `V_Engage_Partner`)
- **Purpose Indicators**: Suffixes like `_DW` indicate data warehouse views (e.g., `V_Base_User_DW`, `V_Policy_DW`)

### Table and View Type Conventions

#### Table Types

- **Dimension Tables (D_)**: Store descriptive attributes about business entities
  - Example: `D_State`, `D_Group`, `D_Partner`, `D_TPA`
  - Typically implement SCD (Slowly Changing Dimension) patterns

- **Fact Tables (F_)**: Store business metrics and measurements
  - Example: `F_Member_Metrics`
  - Often include foreign keys to dimension tables

- **Control Tables (T_)**: System control objects
  - Example: `T_BATCH_CONTROL`
  - Manages processing metadata

#### View Types

- **Base Views (V_Base_)**: Direct transformations of source data
  - Example: `V_Base_User_DW`, `V_Base_Policy_DW`

- **Business Domain Views (V_[Domain]_)**: Domain-specific views
  - Example: `V_Billing_Partner`, `V_Engage_Partner`

- **Metric Views (V_F_)**: Views that prepare or transform metrics
  - Example: `V_F_Member_Metrics_L1`, `V_F_Member_ReEngagements`

- **Processing Level Views (_L#)**: Indicate transformation stages
  - Example: `V_F_Member_Metrics_L1`, `V_F_Member_Metrics_L2`, `V_F_Member_Metrics_L3`

### Column Naming Conventions

#### Primary Keys

- **Surrogate Keys**: Suffixed with `_SK` (e.g., `State_SK`, `Group_SK`, `Partner_SK`)
- **Natural Keys/Business Keys**: Suffixed with `_Id` (e.g., `Group_Id`, `Partner_Id`, `User_Id`)

#### Attribute Columns

- **Standard Attributes**: Use Pascal_Case with underscores (e.g., `First_Name`, `Last_Name`, `Email`)
- **Code Values**: Suffixed with `_Cd` or `_Code` (e.g., `State_Cd`, `Relationship_Cd`, `Lob_Cd`)
- **Type Indicators**: Suffixed with `_Type` (e.g., `Partner_Type`, `Region_Type`)
- **Flags/Indicators**: Prefixed with verb or suffixed with flag-like terms (e.g., `REGISTERED_ELIG`, `Current_Flag`)
- **Counts**: Suffixed with `_CNT` or `_Count` (e.g., `SUBSCRIBER_CNT`, `DEPENDENT_CNT`, `MEMBER_CNT`)

#### Temporal Columns

- **Date Columns**: Suffixed with `_Dt` (e.g., `Effective_From_Dt`, `Effective_To_Dt`, `As_Of_Date`)
- **Timestamp Columns**: Suffixed with `_dt_ts` or `_ts` (e.g., `Created_dt_ts`, `Updated_dt_ts`, `Latest_Logged_In_ts`)

#### Audit Columns

Standard audit columns across tables:
- `Created_dt_ts` / `CreatedAt`: Creation timestamp
- `Updated_dt_ts` / `UpdatedAt`: Last update timestamp
- `Created_By` / `CreatedByUserId`: User who created the record
- `Updated_By` / `UpdatedByUserId`: User who last updated the record

#### SCD-Related Columns

For SCD Type 2 tables:
- `Effective_From_Dt`: Start of validity period
- `Effective_To_Dt`: End of validity period
- `Current_Flag` / `CURRENT`: Flag indicating current record

### Non-Conforming Cases and Recommendations

#### Inconsistent Naming

1. **Mixed Case Conventions**:
   - Some columns use UPPERCASE (`SUBSCRIBER_CNT`, `REGISTERED_ELIG`)
   - Others use Pascal_Case (`First_Name`, `Last_Name`)
   - Some use lowercase with underscores in SQL but Pascal_Case in YAML
   - **Recommendation**: Adopt Pascal_Case consistently for all column names

2. **Inconsistent Suffixes**:
   - Date columns sometimes use `_Dt` and other times `_Date` or just `Date`
   - Timestamp columns sometimes use `_dt_ts`, other times `_ts` or `_At`
   - **Recommendation**: Standardize on `_Dt` suffix for date columns and `_dt_ts` for timestamps

3. **Inconsistent Prefixes**:
   - Some views use domain prefixes (`V_Billing_Partner`) while similar views don't
   - **Recommendation**: Apply domain prefixes consistently for related view sets

#### Structural Inconsistencies

1. **Temporal Handling Variations**:
   - Some models use `dbt_valid_from`/`dbt_valid_to` (dbt snapshots)
   - Others use `Effective_From_Dt`/`Effective_To_Dt` (custom implementation)
   - Some use `Current_Flag`, others use `CURRENT`
   - **Recommendation**: Standardize temporal column naming across models

2. **Audit Column Variations**:
   - Bronze/Silver layers use `Created_dt_ts`/`Updated_dt_ts`
   - ODS layer uses `CreatedAt`/`UpdatedAt`
   - **Recommendation**: Adopt a single naming convention for audit columns


## 4. DBT Implementation

### Project Configuration Deep Dive
```yaml
name: 'Analytics'
version: '1.0.0'
config-version: 2

vars:
  # Global date handling
  as_of_run_date: "{{ var('today_date', modules.datetime.date.today().strftime('%Y-%m-%d')) }}"
  future_proof_date: '9999-12-31'  # For SCD Type 2 current records
  past_init_date: '2000-12-31'     # For historical initialization

models:
  Analytics:
    ods:
      +schema: ODS
      +materialized: view  # Keep as views for flexibility
    warehouse:
      +schema: Warehouse
      +materialized: incremental  # Efficient historical tracking
    gold:
      +schema: Gold
      +materialized: table  # Optimized for query performance
```

Configuration Rationale:
- **Schema Separation**: 
  - Isolates different data processing stages
  - Enables granular security controls
  - Simplifies cost tracking and optimization

- **Materialization Choices**:
  - ODS as views: Minimizes storage costs, ensures latest data
  - Warehouse as incremental: Efficiently handles historical changes
  - Gold as tables: Optimizes query performance for reporting

### Model Documentation Standards

#### Required Documentation Elements

All dbt models should include YAML documentation with the following structure:

```yaml
version: 2

models:
  - name: model_name
    description: |
      Purpose:
      [Clear description of what this model does and why it exists]
      
      Data Grain:
      [Description of what each row represents]
      
      Direct Sources:
      - [Source1](/#!/model/model.Analytics.Source1) (description)
      - [Source2](/#!/model/model.Analytics.Source2) (description)
      
      Indirect Sources:
      - [IndirectSource1](/#!/model/model.Analytics.IndirectSource1) (description)
      - [IndirectSource2](/#!/model/model.Analytics.IndirectSource2) (description)
    
    config:
      materialized: [table/view/incremental]
      tags: ['layer', 'domain', 'type']
    
    transformations:
      - name: transformation_name
        description: |
          [Detailed description of transformation]
        joins:
          - join: table_name
            type: [left/inner/etc]
            relationship: [one_to_one/many_to_one/etc]
            sql: "join condition"
    
    columns:
      # Group columns by logical sections with comments
      - name: column_name
        description: "Detailed description"
        type: data_type
        source_column: "original_column_name"
        source_table: "source_table_name"
        tests:
          - unique
          - not_null
    
    meta:
      owner: "Team Name"
      updated_at: "YYYY-MM-DD"
      depends_on:
        - "dependency1" # [dependency1](/#!/model/model.Analytics.dependency1)
        - "dependency2" # [dependency2](/#!/model/model.Analytics.dependency2)
      table_type: "table/view"
      temporal_type: "scd_type_1/scd_type_2/current/snapshot/static"
      refresh_frequency: "daily/weekly/monthly/incremental/static"
```

#### Documentation Guidelines

1. **Description Format**
   - Use pipe character (`|`) for descriptions to preserve formatting
   - Include Purpose, Data Grain, Direct Sources, and Indirect Sources sections
   - Add hyperlinks to sources and dependencies for easy navigation

2. **Column Documentation**
   - Group columns by logical sections with comments
   - Include source information (source_column, source_table)
   - Document data type and any transformations applied

3. **Transformations Section**
   - Document each major transformation step
   - Include join information with relationship types
   - Explain business logic in plain language

4. **Meta Section**
   - Include owner, update date, and dependencies
   - Specify correct table_type based on naming conventions
   - Set appropriate temporal_type based on layer
   - Document refresh frequency

#### Testing Requirements
   - Every primary key must have unique and not_null tests
   - Business rules must have custom tests
   - Reference data must have referential integrity tests

Example Test:
```sql
-- tests/assert_valid_user_dates.sql
SELECT 
    User_Id,
    dbt_valid_from,
    dbt_valid_to
FROM {{ ref('V_User_DW') }}
WHERE dbt_valid_from >= dbt_valid_to
   OR dbt_valid_from IS NULL
```

## 5. Seed File Management

### Purpose and Usage
Seed files serve multiple purposes:
1. Reference data management
2. Configuration values
3. Mapping tables
4. Test data

### Storage Standards
```plaintext
Analytics/seeds/
├── reference/
│   ├── state_codes.csv
│   └── country_codes.csv
├── mappings/
│   ├── product_categories.csv
│   └── department_mappings.csv
└── configuration/
    └── batch_configuration_variables.csv
```

### Seed File Requirements
1. **Format Standards**:
   - UTF-8 encoding
   - CSV format with headers
   - No special characters in column names
   - Consistent date format (YYYY-MM-DD)

2. **Version Control**:
   - Include in repository
   - Document changes in commit messages
   - Review changes in PR process

3. **Configuration Example**:
```yaml
seeds:
  Analytics:
    +schema: Warehouse
    reference:
      state_codes:
        +column_types:
          state_code: string
          state_name: string
    configuration:
      batch_configuration_variables:
        +column_types:
          Name: string
          Value: string
          Description: string
```

### Processing Guidelines
1. **Initial Load**:
```bash
dbt seed --full-refresh --select reference.*
```

2. **Incremental Updates**:
```bash
dbt seed --select state_codes
```

3. **Validation**:
```sql
-- tests/assert_seed_referential_integrity.sql
SELECT s.state_code
FROM {{ ref('state_codes') }} s
LEFT JOIN {{ ref('user_addresses') }} ua
  ON s.state_code = ua.state_code
WHERE ua.state_code IS NULL
```

## 6. Development Workflow

### Feature Development Process

1. **Branch Creation**
```bash
git checkout -b MX-1234-user-reengagement-metrics
```

2. **Model Development Steps**:
   a. Create base view in warehouse layer
   b. Implement business logic
   c. Add tests
   d. Generate documentation
   e. Create PR

### Testing Framework

1. **Unit Tests**
```sql
-- tests/unit/test_user_reengagement.sql
WITH test_data AS (
    SELECT '2023-01-01' as login_date, 'user1' as user_id
    UNION ALL
    SELECT '2023-01-02', 'user1'
)
SELECT 
    user_id,
    COUNT(*) as login_count
FROM {{ ref('V_ReEngagements_Daily_DW') }}
WHERE login_date BETWEEN '2023-01-01' AND '2023-01-02'
GROUP BY user_id
HAVING login_count != 2
```

2. **Integration Tests**
```sql
-- tests/integration/test_batch_control_flow.sql
SELECT 
    batch_control_dt,
    COUNT(*) as record_count
FROM {{ ref('V_ReEngagements_Daily_DW') }} r
JOIN {{ ref('T_BATCH_CONTROL') }} b
  ON DATE(r.login_date) <= b.batch_control_dt
WHERE b.batch_id = 'MxDataMartDailyBuild'
GROUP BY batch_control_dt
HAVING record_count = 0
```

### Deployment Pipeline
1. **Pre-deployment Checks**:
   ```bash
   dbt compile  # Verify SQL validity
   dbt test    # Run all tests
   dbt docs generate  # Update documentation
   ```

2. **Production Deployment**:
   - Merge to main branch triggers deployment
   - Automated tests run in staging
   - Production deployment requires approval

## 7. Processing Schedule

### ODS Layer Processing
- **Frequency**: Every 3 hours
- **Implementation**:
```python
def process_ods():
    """
    Updates ODS layer from source systems.
    Implements CDC (Change Data Capture) pattern.
    """
    current_timestamp = datetime.now(timezone.utc)
    last_successful_run = get_last_successful_run()
    
    return process_changes(last_successful_run, current_timestamp)
```

### Warehouse Layer Processing
- **Frequency**: Every 3 hours, following ODS
- **Implementation**:
```sql
-- Example incremental model
{{ config(
    materialized='incremental',
    unique_key='User_Id',
    incremental_strategy='timestamp'
) }}

SELECT 
    User_Id,
    Username,
    Email,
    UpdatedAt,
    CURRENT_TIMESTAMP() as dbt_valid_from,
    NULL as dbt_valid_to
FROM {{ ref('ods_users') }}
WHERE UpdatedAt > (
    SELECT MAX(dbt_valid_from) 
    FROM {{ this }}
)
```

### Gold Layer Processing
- **Frequency**: Daily
- **Batch Control**:
```python
def update_batch_control():
    """
    Updates batch control date after successful processing
    """
    with BigQueryClient() as bq:
        query = """
        UPDATE `Warehouse.T_BATCH_CONTROL`
        SET batch_control_dt = DATE_ADD(batch_control_dt, INTERVAL 1 DAY)
        WHERE batch_id = 'MxDataMartDailyBuild'
        """
        bq.query(query)
```

## 8. Best Practices and Reusable Patterns

### Reusable Patterns and Macros

Extensive use of macros for standardized functionality:

1. **Standard Column Macros**
   - `bronze_basetable_columns` and `ods_basetable_columns` for consistent audit columns
   ```sql
   SELECT
     primary_key_columns,
     business_columns,
     {{ bronze_basetable_columns() }}
   FROM source_table
   ```

2. **Temporal Data Handling**
   - `get_as_future_proof_date` for standardized handling of open-ended dates
   ```sql
   SELECT
     id,
     valid_from,
     {{ get_as_future_proof_date('valid_to') }} as valid_to
   FROM source_table
   ```

   - `is_snapshot_current` for identifying current records
   ```sql
   SELECT
     id,
     valid_from,
     valid_to,
     {{ is_snapshot_current() }} as is_current
   FROM snapshot_table
   ```

3. **Table Joining Patterns**
   - `join_snapshots` for temporally-aware table joins
   ```sql
   SELECT
     {{ join_snapshots(
         cte_join='dim_customer',
         cte_join_on='dim_address',
         cte_join_valid_to='valid_to',
         cte_join_valid_from='valid_from',
         cte_join_on_valid_to='valid_to',
         cte_join_on_valid_from='valid_from',
         cte_join_id='address_id',
         cte_join_on_id='id',
         cte_join_on_values_to_get=[{'address_line1': 'customer_address_line1'}]
     ) }}
   ```

4. **Partner-Related Utilities**
   - `get_partner_type` for standardized partner classification
   ```sql
   SELECT
     partner_id,
     partner_name,
     {{ get_partner_type('partner_name') }} as partner_type
   FROM partners
   ```

   - `get_partner_name_ignore_prefix` for clean partner names
   ```sql
   SELECT
     partner_id,
     partner_name,
     {{ get_partner_name_ignore_prefix('partner_name') }} as clean_partner_name
   FROM partners
   ```

### Performance Optimization
1. **Partition Design**:
```sql
{{ config(
    partition_by={
      "field": "created_date",
      "data_type": "date",
      "granularity": "day"
    }
) }}
```

2. **Clustering Strategy**:
```sql
{{ config(
    cluster_by = ["user_id", "product_category"]
) }}
```

### Error Handling
1. **Data Quality Checks**:
```sql
-- Implement quality gates
WITH quality_check AS (
    SELECT COUNT(*) as invalid_records
    FROM {{ ref('source_table') }}
    WHERE amount < 0
        OR transaction_date > CURRENT_DATE()
)
SELECT * 
FROM quality_check
WHERE invalid_records > 0
```

2. **Logging and Monitoring**:
```python
def log_processing_metrics(model_name, start_time, end_time, record_count):
    """
    Logs processing metrics to monitoring system
    """
    metrics = {
        'model': model_name,
        'duration_seconds': (end_time - start_time).total_seconds(),
        'record_count': record_count,
        'timestamp': datetime.now(timezone.utc)
    }
    send_to_monitoring_system(metrics)
```

### Documentation Maintenance
1. **Model Documentation**:
   - Update docs with every schema change
   - Include business context
   - Document dependencies

2. **Change Management**:
   - Track breaking changes
   - Maintain upgrade guides
   - Document migration paths
