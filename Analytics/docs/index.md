# Medxoom Data Warehouse Documentation

Welcome to the Medxoom Data Warehouse documentation. This site contains comprehensive information about our data models, transformation logic, and standards.

## Key Documentation

- [Data Warehouse Standards](DataWarehouse_Standards_Detailed.md) - Detailed standards for naming conventions, design philosophy, and documentation requirements

## Data Layers

Our data warehouse is organized into several layers:

### ODS Layer
The Operational Data Store (ODS) serves as an intermediate landing zone for raw data, refreshed every 3 hours from source systems.

### Warehouse/Silver Layer
The Warehouse layer provides a standardized, historical view of enterprise data, implementing Slowly Changing Dimensions (SCD) Type 2 for historical tracking.

### Gold Layer
The Gold layer contains business-ready datasets optimized for reporting, with denormalized structures and business logic applied.

### Intermediate Layer
The Intermediate layer contains processing views that support the transformation pipeline between layers.

## Model Types

Our models follow specific naming conventions:

- **Dimension Tables (D_)**: Store descriptive attributes about business entities
- **Fact Tables (F_)**: Store business metrics and measurements
- **Views (V_)**: Provide transformed or filtered views of data
- **Control Tables (T_)**: Manage system metadata and processing control

## Getting Started

To explore our data models:

1. Browse the **Project** section to see all models organized by folder
2. Use the **Search** functionality to find specific models
3. Explore the **Lineage** graphs to understand data dependencies
4. Review the **Documentation** section for standards and guidelines
