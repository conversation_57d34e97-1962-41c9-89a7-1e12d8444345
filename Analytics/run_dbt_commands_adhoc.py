from google.cloud import bigquery
from datetime import datetime, timedelta
import subprocess
import os
from google.oauth2 import service_account
import traceback
import sys

def update_batch_control(client,project, date_str):
    query = f"""
    update `{project}.Warehouse.T_Batch_Control`
    set Batch_Control_Dt = date('{date_str}')
    where batch_id = 'MxDataMartDailyBuild'
    """
    
    job = client.query(query)
    job.result()  # Wait for the query to complete

def run_dbt_model(dbt_target = None):
    # Add a flag to track if it's the first run
    # if not hasattr(run_dbt_model, 'first_run'):
    #     run_dbt_model.first_run = True
    #     # First run - dimensions with full refresh
    #     dbt_command_dims = (
    #         f"dbt build "
    #         f"--select +D_Module_Config +D_Event +D_Access_Mode +D_Event_Region "
    #         f"--exclude tag:decouple_from_datamart "
    #         f"--target {dbt_target} "
    #         f"--full-refresh"
    #     )
    #     # Run facts and gold models
    #     dbt_command_facts = (
    #         f"dbt build "
    #         f"--select F_Event_Metrics Member_Event_History "
    #         f"--target {dbt_target}"
    #     )
    # else:
        # Subsequent runs - incremental for all
    # dbt_command_dims = (
    #     f"dbt build "
    #     f"--select +D_Event +D_Access_Mode +D_Event_Region "
    #     f"--exclude tag:decouple_from_datamart "
    #     f"--target {dbt_target}"
    # )
    dbt_command_facts = (
        f"dbt build "
        f"--select F_Event_Metrics Member_Event_History "
        f"--target {dbt_target}"
    )
    # dbt_command_facts = (
    #     f"dbt build "
    #     f"--select Member_Event_History "
    #     f"--target {dbt_target}"
    # )
    
    # Run dimension models
    # exit_code = os.system(dbt_command_dims)
    # if exit_code != 0:
    #     print("DBT build failed for dimension models")
    #     sys.exit(1)
        
    # Run fact and gold models
    exit_code = os.system(dbt_command_facts)
    if exit_code != 0:
        print("DBT build failed for fact and gold models")
        sys.exit(1)

def main():
    try:

        project = "allieddigital-prod"
        dbt_target = "allied_prod"
        proj_sa_json = 'allieddigital-prod-dw-key.json'

        # project = "medxoom-prod"
        # dbt_target = "prod"
        # proj_sa_json = 'medxoom-prod-37b489489944.json'

        # project = "medxoom-dataops"
        # dbt_target = "stg_dataops"
        # proj_sa_json = 'medxoom-dataops-312724fb6c43.json'

        # Initialize BigQuery client
        credentials = service_account.Credentials.from_service_account_file(
            f'/Users/<USER>/Medxoom/{proj_sa_json}'
        )
        
        client = bigquery.Client(
            credentials=credentials,
            project=project
        )

        # Define date range
        start_date = datetime(2025,5, 29)
        end_date = datetime(2025, 6, 10)
        current_date = start_date

        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            print(f"\nProcessing date: {date_str}")
            
            # Step 1: Update batch control date
            print("Updating batch control...")
            update_batch_control(client, project, date_str)
            
            # Step 2: Run DBT model
            print("Running DBT model...")
            run_dbt_model(dbt_target=dbt_target)
            
            print(f"Successfully processed {date_str}")
            current_date += timedelta(days=1)

    except Exception as e:
        print(f"Fatal error: {str(e)}")
        print("Traceback:")
        print(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
