DROP TABLE IF EXISTS `PROJECT_ID.Silver.D_Age`;
DROP TABLE IF EXISTS `PROJECT_ID.Silver.D_LOB`;
DROP TABLE IF EXISTS `PROJECT_ID.Silver.D_TPA`;
DROP TABLE IF EXISTS `PROJECT_ID.Silver.D_State`;
DROP TABLE IF EXISTS `PROJECT_ID.Silver.D_Group`;
DROP TABLE IF EXISTS `PROJECT_ID.Silver.D_Plan`;
DROP TABLE IF EXISTS `PROJECT_ID.Silver.D_Partner`;
DROP TABLE IF EXISTS `PROJECT_ID.Silver.D_Metric`;
DROP TABLE IF EXISTS `PROJECT_ID.Silver.F_Member_Metrics`;
DROP VIEW IF EXISTS `PROJECT_ID.Silver.V_Billing_Partner`;
DROP VIEW IF EXISTS `PROJECT_ID.Silver.V_Engage_Partner`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_Age_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_State_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_TPA_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_Metric_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_Plan_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_Group_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_Partner_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_PartnerGroup_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_Policy_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_Eligibility_DW`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_F_Member_Metrics_L1`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_F_Member_Metrics_L2`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_F_Member_Metrics_L3`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_F_Member_Metrics_L3_Billing`;
DROP VIEW IF EXISTS `PROJECT_ID.Warehouse.V_F_Member_Metrics_L4`;

DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_D_Age`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_D_Group`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_D_LOB`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_D_Metric`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_D_Partner`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_D_Plan`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_D_State`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_D_TPA`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_F_Member_Metrics`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_F_Member_Metrics_Billing`;
DROP PROCEDURE IF EXISTS `PROJECT_ID.Silver.SP_F_Member_Metrics_Eligibility`;


##phase initialization
dbt build --select "models/warehouse/views" --exclude tag:decouple_from_datamart --target prod
dbt build --full-refresh --select "+models/silver/common" --exclude tag:decouple_from_datamart  --target prod
dbt build --select "+models/intermediate" --exclude tag:decouple_from_datamart tag:exclude_when_incremental --target prod
dbt build --select +V_F_Member_Metrics_L4 --exclude tag:exclude_when_incremental tag:decouple_from_datamart --target prod
dbt build --select "models/silver/member" --target prod
dbt build --select +V_F_Member_Metrics_L3_Billing --exclude tag:exclude_when_incremental tag:decouple_from_datamart --target prod

##phase incremental
dbt build --select "+models/silver/common" --exclude tag:decouple_from_datamart --target prod
dbt build --select "+models/intermediate" --exclude tag:exclude_when_incremental tag:decouple_from_datamart --target prod
dbt build --select "models/silver/member" --target prod

#command to run build in prod

gcloud auth application-default login
curl -d '{"select_tag": "models/intermediate","batch_control_id": "Custom_Run","batch_control_dt":"2025-04-30"}' -H "Content-Type: application/json" -H "Authorization: Bearer $(gcloud auth print-identity-token)" -X POST https://mx-dbt-wrapper-88322699222.us-west1.run.app/dbt_build

SELECT 
 jsonPayload.consolidated,
jsonPayload.invocation_id,
jsonPayload.layer_name,
jsonPayload.dbt_model_path,
jsonPayload.table_or_view_name,
jsonPayload.start_time_ts,
jsonPayload.end_time_ts,
jsonPayload.num_rows as num_rows_text,
jsonPayload.process_run_status,
jsonPayload.batch_control_id,
jsonPayload.batch_control_dt,
timestamp
FROM `medxoom-prod.mx_dbtlog_prod.run_googleapis_com_stdout`
WHERE TIMESTAMP_TRUNC(timestamp, DAY) = TIMESTAMP("2025-05-01")  
AND jsonPayload.layer_name = 'Data Mart'
AND jsonPayload.process_run_status <> 'success'
order by timestamp desc
