
CREATE OR REPLACE TEMP TABLE `G_Ids_to_delete` AS

SELECT 
  g.Id
FROM `mx.insurance.Group` g
JOIN `mx.partnerorganization.PartnerGroup` pg ON g.Id = pg.GroupId
JOIN `mx.partnerorganization.Partner` p ON pg.PartnerId = p.Id
WHERE 
    p.Name LIKE '[Billing]%'
    AND TRIM(REGEXP_REPLACE(p.Name, r'\[Billing\]', '')) <> 'Allied';


Select count(*) from `G_Ids_to_delete`;
--4882

--Warehouse Tables

-- mx_authentication_LoginAttempt
delete from `mx.authentication.LoginAttempt`
where Id in (
    SELECT 
        distinct la.Id
    FROM 
        `mx_authentication_LoginAttempt` la
    INNER JOIN `mx.insurance.Policy` p
        ON la.UserId = p.UserId
    INNER JOIN `mx.insurance.Plan` pl
        ON p.PlanId = pl.Id
    INNER JOIN `mx.partnerorganization.PartnerGroup` pg
        ON pl.GroupId = pg.GroupId
    INNER JOIN `mx.partnerorganization.Partner` bp
        ON pg.PartnerId = bp.Id
    WHERE bp.Name not like '%Allied%'
);
-- This statement removed 3,234,846 rows from mx_authentication_LoginAttempt.

-- mx_authentication_User
delete from `mx.authentication.User`
where Id in (
    SELECT 
        distinct u.Id
    FROM 
        `mx.authentication.User` u
    INNER JOIN `mx.insurance.Policy` p
        ON u.Id = p.UserId
    INNER JOIN `mx.insurance.Plan` pl
        ON p.PlanId = pl.Id
    INNER JOIN `mx.partnerorganization.PartnerGroup` pg
        ON pl.GroupId = pg.GroupId
    INNER JOIN `mx.partnerorganization.Partner` bp
        ON pg.PartnerId = bp.Id
    WHERE bp.Name not like '%Allied%'
);
--This statement removed 253,202 rows from mx_authentication_User.


--mx_insurance_Benefit
DELETE FROM `mx.insurance.Benefit` AS b
WHERE EXISTS (
  SELECT 1
  FROM `mx.insurance.Plan` AS p
  WHERE p.id = b.planid 
    AND p.groupid IN (SELECT id FROM G_Ids_to_delete)
);
--This statement removed 1,574,567 rows from mx_insurance_Benefit.


--mx_insurance_BenefitTier
DELETE FROM `mx.insurance.BenefitTier` AS b
WHERE EXISTS (
  SELECT 1
  FROM `mx.insurance.Plan` AS p
  WHERE p.id = b.planid 
    AND p.groupid IN (SELECT id FROM G_Ids_to_delete)
);
--This statement removed 38,081 rows from mx_insurance_BenefitTier.


-- mx_providersearch_PlanTierNetwork
DELETE FROM `mx.providersearch.PlanTierNetwork` AS n
WHERE EXISTS (
  SELECT 1
  FROM `mx.insurance.Plan` AS p
  WHERE p.id = n.planid 
    AND p.groupid IN (SELECT id FROM G_Ids_to_delete)
);
--This statement removed 4,897 rows from mx_providersearch_PlanTierNetwork.


-- mx_providersearch_PlanTierRibbonInsurance
DELETE FROM `mx.providersearch.PlanTierRibbonInsurance` AS r
WHERE EXISTS (
  SELECT 1
  FROM `mx.insurance.Plan` AS p
  WHERE p.id = r.planid 
    AND p.groupid IN (SELECT id FROM G_Ids_to_delete)
);
--This statement removed 15,044 rows from mx_providersearch_PlanTierRibbonInsurance.


--mx_insurance_Plan
delete from `mx.insurance.Plan`
where groupid in (select Id from G_Ids_to_delete);
--This statement removed 68,420 rows from mx_insurance_Plan.


--mx_insurance_Policy
delete from `mx.insurance.Policy`
where EligibilityId in (
    select Id from `mx.fileprocessing.Eligibility` where Payername <> 'Allied'
);
--This statement removed 1,985,950 rows from mx_insurance_Policy.


--mx_fileprocessing_Eligibility
delete from `mx.fileprocessing.Eligibility` where Payername <> 'Allied';
--this statement removed 2,247,115 rows from mx_fileprocessing_Eligibility


--mx_configuration_ModuleConfig
delete from `mx.configuration.ModuleConfig`
where GroupId in (select Id from G_Ids_to_delete);
--This statement removed 55,950 rows from mx_configuration_ModuleConfig.

--mx_configuration_TerminologyConfig
delete from `mx.configuration.TerminologyConfig`
where GroupId in (select Id from G_Ids_to_delete);
--This statement removed 200 rows from mx_configuration_TerminologyConfig.

--mx_partnerorganization_Partner
delete from `mx.partnerorganization.Partner`
WHERE Name not like '%Allied%';
--This statement removed 884 rows from mx_partnerorganization_Partner.

--mx_partnerorganization_PartnerGroup
delete from `mx.partnerorganization.PartnerGroup`
where groupid in (select Id from G_Ids_to_delete);
--This statement removed 29,804 rows from mx_partnerorganization_PartnerGroup.


--mx_insurance_Group
delete from `mx.insurance.Group`
where Id in (select Id from G_Ids_to_delete);
--This statement removed 9,918 rows from mx_insurance_Group.