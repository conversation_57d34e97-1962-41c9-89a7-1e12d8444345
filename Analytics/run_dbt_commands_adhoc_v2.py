"""
DBT Runner Script for Data Engineering Pipelines
Provides flexible execution of dbt commands with proper configuration management
"""

import os
import sys
import logging
import subprocess
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from google.cloud import bigquery
from google.oauth2 import service_account
import traceback


@dataclass
class DatabaseConfig:
    """Configuration for database connection"""
    project: str
    dbt_target: str
    service_account_file: str
    

@dataclass
class DbtCommand:
    """Configuration for a dbt command"""
    command: str = "build"
    select: Optional[str] = None
    exclude: Optional[str] = None
    tags: Optional[List[str]] = None
    full_refresh: bool = False
    

class DbtRunner:
    """Main class for running dbt commands with flexible configuration"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.logger = self._setup_logging()  # Move this BEFORE client initialization
        self.client = self._initialize_bigquery_client()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('dbt_runner.log')
            ]
        )
        return logging.getLogger(__name__)
    
    def _initialize_bigquery_client(self) -> bigquery.Client:
        """Initialize BigQuery client with service account credentials"""
        try:
            if not os.path.exists(self.config.service_account_file):
                raise FileNotFoundError(f"Service account file not found: {self.config.service_account_file}")
                
            credentials = service_account.Credentials.from_service_account_file(
                self.config.service_account_file
            )
            
            return bigquery.Client(
                credentials=credentials,
                project=self.config.project
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize BigQuery client: {str(e)}")
            raise

    def update_batch_control(self, date_str: str, batch_id: str = 'MxDataMartDailyBuild') -> None:
        """Update batch control date in warehouse"""
        query = f"""
        UPDATE `{self.config.project}.Warehouse.T_Batch_Control`
        SET Batch_Control_Dt = DATE('{date_str}')
        WHERE batch_id = '{batch_id}'
        """
        
        try:
            self.logger.info(f"Updating batch control for date: {date_str}")
            job = self.client.query(query)
            job.result()  # Wait for completion
            self.logger.info("Batch control updated successfully")
        except Exception as e:
            self.logger.error(f"Failed to update batch control: {str(e)}")
            raise
    
    def run_dbt_command(self, dbt_cmd: DbtCommand) -> bool:
        """
        Run a dbt command with specified parameters
        
        Args:
            dbt_cmd: DbtCommand object with command configuration
            
        Returns:
            bool: True if command succeeded, False otherwise
        """
        # Build the command string
        command_parts = [f"dbt {dbt_cmd.command}"]
        
        if dbt_cmd.select:
            command_parts.append(f"--select {dbt_cmd.select}")
            
        if dbt_cmd.exclude:
            command_parts.append(f"--exclude {dbt_cmd.exclude}")
            
        if dbt_cmd.tags:
            for tag in dbt_cmd.tags:
                command_parts.append(f"--tag {tag}")
        
        command_parts.append(f"--target {self.config.dbt_target}")
        
        if dbt_cmd.full_refresh:
            command_parts.append("--full-refresh")
            
        command = " ".join(command_parts)
        
        try:
            self.logger.info(f"Executing dbt command: {command}")
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info("DBT command completed successfully")
                if result.stdout:
                    self.logger.info(f"DBT output: {result.stdout}")
                return True
            else:
                self.logger.error(f"DBT command failed with return code: {result.returncode}")
                if result.stderr:
                    self.logger.error(f"DBT error: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to execute dbt command: {str(e)}")
            return False
    
    def run_pipeline(self, commands: List[DbtCommand]) -> bool:
        """
        Run a sequence of dbt commands
        
        Args:
            commands: List of DbtCommand objects to execute
            
        Returns:
            bool: True if all commands succeeded, False otherwise
        """
        for i, cmd in enumerate(commands, 1):
            self.logger.info(f"Running command {i}/{len(commands)}")
            if not self.run_dbt_command(cmd):
                self.logger.error(f"Command {i} failed, stopping pipeline")
                return False
        
        self.logger.info("All commands completed successfully")
        return True


def load_config_from_env() -> DatabaseConfig:
    """Load configuration from environment variables"""
    project = os.getenv('DBT_PROJECT', 'allieddigital-prod')
    dbt_target = os.getenv('DBT_TARGET', 'allied_prod')
    sa_file = os.getenv('GOOGLE_SERVICE_ACCOUNT_FILE', '/Users/<USER>/Medxoom/allieddigital-prod-dw-key.json')
    
    return DatabaseConfig(
        project=project,
        dbt_target=dbt_target,
        service_account_file=sa_file
    )


def create_sample_pipeline() -> List[DbtCommand]:
    """Create a sample pipeline configuration"""
    return [
        DbtCommand(
            command="build",
            select="+D_Module_Config +D_Event +D_Access_Mode +D_Event_Region",
            exclude="tag:decouple_from_datamart",
            full_refresh=True
        ),
        DbtCommand(
            command="build",
            select="F_Event_Metrics Member_Event_History"
        )
    ]


def run_date_range_pipeline(
    runner: DbtRunner,
    commands: List[DbtCommand],
    start_date: datetime,
    end_date: datetime,
    batch_id: str = 'MxDataMartDailyBuild'
) -> None:
    """Run pipeline for a range of dates"""
    current_date = start_date
    
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        runner.logger.info(f"Processing date: {date_str}")
        
        try:
            # Update batch control
            runner.update_batch_control(date_str, batch_id)
            
            # Run dbt pipeline
            if not runner.run_pipeline(commands):
                runner.logger.error(f"Pipeline failed for date: {date_str}")
                sys.exit(1)
                
            runner.logger.info(f"Successfully processed {date_str}")
            current_date += timedelta(days=1)
            
        except Exception as e:
            runner.logger.error(f"Fatal error processing {date_str}: {str(e)}")
            runner.logger.error("Traceback:")
            runner.logger.error(traceback.format_exc())
            sys.exit(1)


def main():
    """Main execution function"""
    try:
        # Load configuration
        config = load_config_from_env()
        
        # Initialize runner
        runner = DbtRunner(config)
        
        # Define pipeline commands
        # commands = create_sample_pipeline()
        
        # You can also create custom commands:
        commands = [
            # DbtCommand(
            #     command="build",
            #     select="+models/silver/common",
            #     exclude="tag:decouple_from_datamart"
            # )
        # ,   DbtCommand(
        #         command="build",
        #         select="+models/intermediate/",
        #         exclude="tag:exclude_when_incremental tag:decouple_from_datamart"
        #     )

        DbtCommand(
                command="build",
                select="D_Event D_Event_Region D_Access_Mode",
                exclude="tag:decouple_from_datamart"
        )

        ,DbtCommand(
                command="build",
                select="F_Event_Metrics"            
        )
        ,DbtCommand(
                command="build",
                select="Member_Event_History"            
        )       
        ]
        
        # Define date range
        start_date = datetime(2025, 7, 2)
        end_date = datetime(2025, 7, 6)
        
        # Run pipeline for date range
        run_date_range_pipeline(runner, commands, start_date, end_date)
        
        runner.logger.info("All processing completed successfully")
        
    except Exception as e:
        logging.error(f"Fatal error in main: {str(e)}")
        logging.error("Traceback:")
        logging.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()