version: 2

models:
  - name: V_F_Member_Metrics_L3
    description: |
      Purpose:
      This table enriches member metrics with dimension keys by joining the L2 member metrics
      with various dimension tables, providing a complete view of member-related metrics with
      all necessary dimensional attributes.

      Data Grain:
      One record per member per as_of_date, per plan, per group, per partner (both billing and engage), per state, per age, per TPA, and per line of business.

      Direct Sources:
      - [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2) (base member metrics)
      - [D_Plan](/#!/model/model.Analytics.D_Plan) (plan dimension)
      - [D_Group](/#!/model/model.Analytics.D_Group) (group dimension)
      - [D_Partner](/#!/model/model.Analytics.D_Partner) (partner dimension)
      - [D_State](/#!/model/model.Analytics.D_State) (state dimension)
      - [D_Age](/#!/model/model.Analytics.D_Age) (age dimension)
      - [D_TPA](/#!/model/model.Analytics.D_TPA) (TPA dimension)
      - [D_LOB](/#!/model/model.Analytics.D_LOB) (line of business dimension)

      Indirect Sources:
      - [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW) (via D_Plan)
      - [V_Group_DW](/#!/model/model.Analytics.V_Group_DW) (via D_Group)
      - [V_Partner_DW](/#!/model/model.Analytics.V_Partner_DW) (via D_Partner)
      - [V_LOB_DW](/#!/model/model.Analytics.V_LOB_DW) (via D_LOB)
      - mx_fileprocessing_Eligibility (via V_F_Member_Metrics_L2)
      - mx_insurance_Policy (via V_F_Member_Metrics_L2)

      Key Business Rules:
      - Handles SCD Type 2 joins using effective dates for Plan, Group, and Partner dimensions
      - Differentiates between Billing and Engage partner types
      - Defaults to "-1" (Unknown) for missing dimension keys
      - Calculates registered subscriber and dependent counts based on registration status

    config:
      materialized: table
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: dimension_key_resolution
        description: |
          Resolves dimension keys through SCD Type 2 joins:
          - Matches effective dates with as_of_date for temporal dimensions
          - Applies partner type filtering for partner dimension
          - Defaults missing keys to "-1"

      - name: registration_metrics
        description: |
          Calculates registration-related metrics:
          - REGISTERED_SUBSCRIBER_CNT when SUBSCRIBER_CNT = 1 and REGISTERED_CNT = 1
          - REGISTERED_DEPENDENT_CNT when DEPENDENT_CNT = 1 and REGISTERED_CNT = 1

    columns:
      # Key Columns
      - name: Plan_SK
        description: "Surrogate key for the insurance plan"
        type: string
        source_column: "Plan_SK"
        source_table: "D_Plan"
        transformation_logic: "CASE WHEN dp.Plan_SK IS NULL THEN '-1' ELSE dp.Plan_SK END"

      - name: Group_SK
        description: "Surrogate key for the group"
        type: string
        source_column: "Group_SK"
        source_table: "D_Group"
        transformation_logic: "CASE WHEN dg.Group_SK IS NULL THEN '-1' ELSE dg.Group_SK END"

      - name: Billing_Partner_SK
        description: "Surrogate key for the billing partner"
        type: string
        source_column: "Partner_SK"
        source_table: "D_Partner"
        transformation_logic: "CASE WHEN dpb.Partner_SK IS NULL THEN '-1' ELSE dpb.Partner_SK END"

      - name: Engage_Partner_SK
        description: "Surrogate key for the engage partner"
        type: string
        source_column: "Partner_SK"
        source_table: "D_Partner"
        transformation_logic: "CASE WHEN dpe.Partner_SK IS NULL THEN '-1' ELSE dpe.Partner_SK END"

      - name: State_SK
        description: "Surrogate key for the state"
        type: string
        source_column: "State_SK"
        source_table: "D_State"
        transformation_logic: "CASE WHEN State_SK IS NULL THEN '-1' ELSE State_SK END"

      - name: Age_SK
        description: "Surrogate key for the age"
        type: string
        source_column: "Age_SK"
        source_table: "D_Age"
        transformation_logic: "CASE WHEN Age_SK IS NULL THEN '-1' ELSE Age_SK END"

      - name: TPA_SK
        description: "Surrogate key for the TPA"
        type: string
        source_column: "TPA_SK"
        source_table: "D_TPA"
        transformation_logic: "CASE WHEN TPA_SK IS NULL THEN '-1' ELSE TPA_SK END"

      - name: LOB_SK
        description: "Surrogate key for the line of business"
        type: string
        source_column: "LOB_SK"
        source_table: "D_LOB"
        transformation_logic: "CASE WHEN LOB_SK IS NULL THEN '-1' ELSE LOB_SK END"

      # Registration Metrics
      - name: REGISTERED_SUBSCRIBER_CNT
        description: "Count of registered subscribers"
        type: integer
        transformation_logic: "CASE WHEN SUBSCRIBER_CNT = 1 AND REGISTERED_CNT = 1 THEN 1 ELSE 0 END"

      - name: REGISTERED_DEPENDENT_CNT
        description: "Count of registered dependents"
        type: integer
        transformation_logic: "CASE WHEN DEPENDENT_CNT = 1 AND REGISTERED_CNT = 1 THEN 1 ELSE 0 END"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_F_Member_Metrics_L2" # [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2)
        - "D_Plan" # [D_Plan](/#!/model/model.Analytics.D_Plan)
        - "D_Group" # [D_Group](/#!/model/model.Analytics.D_Group)
        - "D_Partner" # [D_Partner](/#!/model/model.Analytics.D_Partner)
        - "D_State" # [D_State](/#!/model/model.Analytics.D_State)
        - "D_Age" # [D_Age](/#!/model/model.Analytics.D_Age)
        - "D_TPA" # [D_TPA](/#!/model/model.Analytics.D_TPA)
        - "D_LOB" # [D_LOB](/#!/model/model.Analytics.D_LOB)
      table_type: "table"
      temporal_type: "current"
      refresh_frequency: "daily"
