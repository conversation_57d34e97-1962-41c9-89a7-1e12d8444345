{{ config(
    materialized='table'
) }}

SELECT  
    l2.*
    ,CASE WHEN dp.Plan_SK IS NULL THEN "-1"
    ELSE dp.Plan_SK
    END AS Plan_SK
    ,CASE WHEN dg.Group_SK IS NULL THEN "-1"
    ELSE dg.Group_SK
    END AS Group_SK
    ,CASE WHEN dpb.Partner_SK IS NULL THEN "-1"
    ELSE dpb.Partner_SK
    END AS Billing_Partner_SK
    ,CASE WHEN dpe.Partner_SK IS NULL THEN "-1"
    ELSE dpe.Partner_SK
    END AS Engage_Partner_SK
    ,CASE WHEN SUBSCRIBER_CNT = 1 AND REGISTERED_ELIG = 1
     THEN 1 
     ELSE 0
    END AS REGISTERED_SUBSCRIBER_CNT
    ,CASE WHEN DEPENDENT_CNT = 1 AND REGISTERED_ELIG = 1
     THEN 1 
     ELSE 0
    END AS REGISTERED_DEPENDENT_CNT
    ,CASE WHEN State_SK IS NULL THEN "-1"
    ELSE State_SK
    END AS State_SK
    ,CASE WHEN Age_SK IS NULL THEN "-1"
    ELSE Age_SK
    END AS Age_SK
    ,CASE WHEN TPA_SK IS NULL THEN "-1"
    ELSE TPA_SK
    END AS TPA_SK
    ,CASE WHEN LOB_SK IS NULL THEN "-1"
    ELSE LOB_SK
    END AS LOB_SK

    

FROM {{ ref('V_F_Member_Metrics_L2') }} l2
LEFT JOIN {{ ref('D_Plan') }} dp
ON l2.Plan_Id = dp.Plan_Id
AND dp.Effective_From_Dt <= l2.As_Of_Date
AND dp.Effective_To_Dt >= l2.As_Of_Date

LEFT JOIN {{ ref('D_Group') }} dg
ON l2.Group_Id = dg.Group_Id
AND dg.Effective_From_Dt <= l2.As_Of_Date
AND dg.Effective_To_Dt >= l2.As_Of_Date

LEFT JOIN {{ ref('D_Partner') }} dpb
ON l2.Billing_Partner_Id = dpb.Partner_Id
AND dpb.Effective_From_Dt <= l2.As_Of_Date
AND dpb.Effective_To_Dt >= l2.As_Of_Date
AND dpb.Partner_Type = 'Billing'

LEFT JOIN {{ ref('D_Partner') }} dpe
ON l2.Engage_Partner_Id = dpe.Partner_Id
AND dpe.Effective_From_Dt <= l2.As_Of_Date
AND dpe.Effective_To_Dt >= l2.As_Of_Date
AND dpe.Partner_Type = 'Engage'

LEFT JOIN {{ ref('D_State') }} dst
ON l2.State_Cd = dst.State_Cd

LEFT JOIN {{ ref('D_Age') }} dage
ON l2.Age = dage.Age

LEFT JOIN {{ ref('D_TPA') }} dtpa
ON l2.TPA_Name = dtpa.TPA_Name

LEFT JOIN {{ ref('D_LOB') }} dlob
ON l2.LOB_Cd = dlob.LOB_Cd