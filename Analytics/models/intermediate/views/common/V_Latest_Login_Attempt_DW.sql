{{ config(
    materialized='view'
    ) 
}}


WITH base AS (
    SELECT 
        
        UserId
        ,Username
        ,Success
        ,Context
        ,CreatedAt

    FROM {{ ref('mx_authentication_LoginAttempt') }}

    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY UserId
        ORDER BY CreatedAt DESC
    ) = 1

)

SELECT

UserId as User_Id
,Username AS User_Name
,Success AS Success_Flag
,CreatedAt as Created_At

FROM 

base

WHERE Context = 'mx.portal.member'
