version: 2

models:
  - name: V_Latest_Login_Attempt_DW
    description: |
      Purpose:
      This view provides the most recent login attempt for each user in the member portal,
      filtering specifically for member portal context and including success/failure status.

      Data Grain:
      One record per user, representing their latest login attempt.

      Direct Sources:
      - [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt) (login attempt history)

      Indirect Sources:
      - [mx_authentication_User](/#!/model/model.Analytics.mx_authentication_User) (referenced through UserId in LoginAttempt)
      - [mx_authentication_Context](/#!/model/model.Analytics.mx_authentication_Context) (referenced through Context in LoginAttempt)

      Key Business Rules:
      - Filters for 'mx.portal.member' context only
      - Takes only the most recent attempt per user based on CreatedAt timestamp
      - Includes both successful and failed attempts

    config:
      materialized: view
      tags: ["intermediate", "common", "authentication", "member_portal"]

    transformations:
      - name: latest_attempt_selection
        description: |
          Selects the most recent login attempt:
          - Partitions data by UserId
          - Orders by CreatedAt descending
          - Uses ROW_NUMBER() to select latest record

      - name: context_filtering
        description: |
          Filters records:
          - Includes only 'mx.portal.member' context
          - Removes any other portal or application contexts

    columns:
      # Key Columns
      - name: User_Id
        description: "Unique identifier for the user"
        type: string
        source_column: "UserId"
        source_table: "mx_authentication_LoginAttempt"

      # Attribute Columns
      - name: User_Name
        description: "Username used for the login attempt"
        type: string
        source_column: "Username"
        source_table: "mx_authentication_LoginAttempt"

      - name: Success_Flag
        description: "Indicates if the login attempt was successful"
        type: boolean
        source_column: "Success"
        source_table: "mx_authentication_LoginAttempt"

      # Timestamp Columns
      - name: Created_At
        description: "Timestamp when the login attempt occurred"
        type: timestamp
        source_column: "CreatedAt"
        source_table: "mx_authentication_LoginAttempt"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_authentication_LoginAttempt" # [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"

