version: 2

models:
  - name: V_Policy_Max_EligId_DW
    description: |
      Purpose:
      This view identifies the most recent eligibility ID for each user's policy,
      ensuring that we capture the latest eligibility association for policy holders.

      Data Grain:
      One record per User_Id, containing their most recent (highest) Eligibility ID.

      Direct Sources:
      - [mx_insurance_Policy](/#!/model/model.Analytics.mx_insurance_Policy) (policy data with user and eligibility associations)
      - T_Batch_Control (for batch processing date)

      Indirect Sources:
      - None

      Key Business Rules:
      - Filters for records valid on the batch control date
      - Includes only records where UserId is not null
      - Takes the highest EligibilityId per UserId
      - Handles SCD Type 2 versioning using dbt_valid_from/to dates

    config:
      materialized: view
      tags: ["intermediate", "common", "MxDataMartDailyBuild"]

    transformations:
      - name: temporal_filtering
        description: |
          Applies temporal filtering:
          - Uses batch date from T_Batch_Control (MxDataMartDailyBuild)
          - Ensures records are valid on batch date using dbt_valid_from/to
          - Adjusts dbt_valid_to by subtracting one day when not 9999-12-31
        joins:
          - join: T_Batch_Control
            type: cross
            relationship: many_to_one
            sql: "batch_id = 'MxDataMartDailyBuild'"

      - name: latest_version_selection
        description: |
          Selects latest version of policy records:
          - Partitions by id
          - Orders by dbt_updated_at DESC
          - Takes first record per partition

      - name: max_eligibility_selection
        description: |
          Selects highest eligibility ID:
          - Partitions by UserId
          - Orders by EligibilityId DESC
          - Takes first record per partition

    columns:
      # Key Columns
      - name: User_Id
        description: "Unique identifier for the user from the policy record"
        type: string
        source_column: "UserId"
        source_table: "mx_insurance_Policy"

      - name: Elig_Id
        description: "Latest eligibility ID associated with the user"
        type: string
        source_column: "EligibilityId"
        source_table: "mx_insurance_Policy"
        transformation_logic: "Highest EligibilityId per UserId"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_insurance_Policy" # [mx_insurance_Policy](/#!/model/model.Analytics.mx_insurance_Policy)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"

