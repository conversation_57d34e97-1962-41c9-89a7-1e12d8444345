{{ config(
    materialized='view'
) }}

WITH base AS (
    SELECT

    l3.Elig_Id
    ,pu.UserId AS User_Id

    FROM 

    {{ ref("V_F_Member_Metrics_L3") }} l3
    JOIN {{ ref("V_Policy_User_DW") }} pu
    ON l3.Elig_Id = pu.Elig_Id
)

,daily AS (
    SELECT 

    Elig_Id,
    COUNT(*) AS Daily_Total_ReEngagements,
    COUNT(DISTINCT reeng.User_Id) AS Daily_Unique_ReEngagements

    FROM base
    JOIN {{ ref("V_ReEngagements_Daily_DW") }} reeng
    ON base.User_Id = reeng.User_Id

    GROUP BY 

    Elig_Id
)

,mtd AS (
    SELECT 

    Elig_Id,
    COUNT(*) AS MTD_Total_ReEngagements,
    COUNT(DISTINCT reeng.User_Id) AS MTD_Unique_ReEngagements

    FROM base
    JOIN {{ ref("V_ReEngagements_MTD_DW") }} reeng
    ON base.User_Id = reeng.User_Id

    GROUP BY 

    Elig_Id
)

,ltd AS (
    SELECT 

    Elig_Id,
    COUNT(DISTINCT reeng.User_Id) AS LTD_Unique_ReEngagements

    FROM base
    JOIN {{ ref("V_ReEngagements_LTD_Unique_DW") }} reeng
    ON base.User_Id = reeng.User_Id

    GROUP BY 

    Elig_Id
)

,ytd AS (
    SELECT 

    Elig_Id,
    COUNT(DISTINCT reeng.User_Id) AS YTD_Unique_ReEngagements

    FROM base
    JOIN {{ ref("V_ReEngagements_YTD_Unique_DW") }} reeng
    ON base.User_Id = reeng.User_Id

    GROUP BY 

    Elig_Id
)

SELECT
    ltd.Elig_Id
    ,IFNULL(daily.Daily_Total_ReEngagements , 0 ) AS Daily_Total_ReEngagements
    ,IFNULL(daily.Daily_Unique_ReEngagements , 0 ) AS Daily_Unique_ReEngagements
    ,IFNULL(mtd.MTD_Total_ReEngagements ,0) AS MTD_Total_ReEngagements
    ,IFNULL(mtd.MTD_Unique_ReEngagements,0) AS MTD_Unique_ReEngagements
    ,IFNULL(ltd.LTD_Unique_ReEngagements,0) AS LTD_Unique_ReEngagements
    ,IFNULL(ytd.YTD_Unique_ReEngagements,0) AS YTD_Unique_ReEngagements

FROM ltd
LEFT JOIN daily
ON daily.Elig_Id = ltd.Elig_Id

LEFT JOIN mtd
ON mtd.Elig_Id = ltd.Elig_Id

LEFT JOIN ytd
ON ytd.Elig_Id = ltd.Elig_Id