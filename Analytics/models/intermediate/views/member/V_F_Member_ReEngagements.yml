version: 2

models:
  - name: V_F_Member_ReEngagements
    description: |
      Purpose:
      This view calculates member re-engagement metrics by combining member metrics with daily
      and month-to-date re-engagement statistics. It provides a comprehensive view of how
      members are returning to and interacting with the platform over time.

      Data Grain:
      One record per eligibility ID per day, containing both daily and month-to-date
      re-engagement metrics.

      Direct Sources:
      - [V_F_Member_Metrics_L3](/#!/model/model.Analytics.V_F_Member_Metrics_L3) (base member metrics)
      - [V_Policy_User_DW](/#!/model/model.Analytics.V_Policy_User_DW) (eligibility-user mapping)
      - [V_ReEngagements_Daily_DW](/#!/model/model.Analytics.V_ReEngagements_Daily_DW) (daily re-engagement counts)
      - [V_ReEngagements_MTD_DW](/#!/model/model.Analytics.V_ReEngagements_MTD_DW) (month-to-date re-engagement counts)
      - [V_ReEngagements_LTD_Unique_DW](/#!/model/model.Analytics.V_ReEngagements_LTD_Unique_DW) (lifetime-to-date unique re-engagement counts)
      - [V_ReEngagements_YTD_Unique_DW](/#!/model/model.Analytics.V_ReEngagements_YTD_Unique_DW) (year-to-date unique re-engagement counts)

      Indirect Sources:
      - [mx_fileprocessing_Eligibility](/#!/model/model.Analytics.mx_fileprocessing_Eligibility) (source for V_F_Member_Metrics_L3)
      - [mx_insurance_Policy](/#!/model/model.Analytics.mx_insurance_Policy) (source for V_Policy_User_DW)
      - [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt) (source for re-engagement views)
      - [V_Eligibility_DW](/#!/model/model.Analytics.V_Eligibility_DW) (via V_F_Member_Metrics_L3)
      - [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW) (via V_F_Member_Metrics_L3)
      - [V_Group_DW](/#!/model/model.Analytics.V_Group_DW) (via V_F_Member_Metrics_L3)
      - [V_PartnerGroup_DW](/#!/model/model.Analytics.V_PartnerGroup_DW) (via V_F_Member_Metrics_L3)

      Key Business Rules:
      - Re-engagements are counted only for authenticated sessions
      - MTD metrics reset at the beginning of each month
      - Unique re-engagements count distinct users per time period
      - Total re-engagements include all interactions per time period

    config:
      materialized: view
      tags: ["intermediate", "member", "engagement", "metrics"]

    transformations:
      - name: base_join_structure
        description: |
          Creates the foundational dataset by joining member metrics with user mapping:
          - Starts with L3 member metrics
          - Links to policy-user mapping for user identification
          - Establishes the base for re-engagement calculations
        joins:
          - join: V_Policy_User_DW
            type: inner
            relationship: many_to_one
            sql: "l3.Elig_Id = pu.Elig_Id"

      - name: daily_metrics_calculation
        description: |
          Calculates daily re-engagement statistics:
          - Joins with daily re-engagement data
          - Aggregates total re-engagements per eligibility
          - Counts unique users with re-engagements
          - Filters for current day's activity
        joins:
          - join: V_ReEngagements_Daily_DW
            type: inner
            relationship: many_to_many
            sql: "base.User_Id = reeng.User_Id"

      - name: mtd_metrics_calculation
        description: |
          Calculates month-to-date re-engagement statistics:
          - Joins with MTD re-engagement data
          - Aggregates total MTD re-engagements
          - Counts unique users with MTD re-engagements
          - Maintains running totals for the current month
        joins:
          - join: V_ReEngagements_MTD_DW
            type: inner
            relationship: many_to_many
            sql: "base.User_Id = reeng.User_Id"

      - name: ltd_metrics_calculation
        description: |
          Calculates lifetime-to-date unique re-engagement statistics:
          - Joins with LTD unique re-engagement data
          - Counts unique users with LTD re-engagements
          - Maintains historical totals across all time
        joins:
          - join: V_ReEngagements_LTD_Unique_DW
            type: inner
            relationship: many_to_many
            sql: "base.User_Id = reeng.User_Id"

      - name: ytd_metrics_calculation
        description: |
          Calculates year-to-date unique re-engagement statistics:
          - Joins with YTD unique re-engagement data
          - Counts unique users with YTD re-engagements
          - Maintains running totals for the current year
        joins:
          - join: V_ReEngagements_YTD_Unique_DW
            type: inner
            relationship: many_to_many
            sql: "base.User_Id = reeng.User_Id"

    columns:
      # Key Identifiers
      - name: Elig_Id
        description: "Unique identifier for eligibility"
        type: string
        source_column: "Elig_Id"
        source_table: "V_F_Member_Metrics_L3"

      - name: User_Id
        description: "Identifier from the authentication system"
        type: string
        source_column: "User_Id"
        source_table: "V_Policy_User_DW"

      # Daily Metrics
      - name: Daily_Total_ReEngagements
        description: "Total number of re-engagements for the current day"
        type: integer
        source_column: "ReEngagement_Count"
        source_table: "V_ReEngagements_Daily_DW"
        transformation_logic: "COUNT(*)"

      - name: Daily_Unique_ReEngagements
        description: "Number of unique users with re-engagements for the current day"
        type: integer
        source_column: "User_Id"
        source_table: "V_ReEngagements_Daily_DW"
        transformation_logic: "COUNT(DISTINCT User_Id)"

      # Month-to-Date Metrics
      - name: MTD_Total_ReEngagements
        description: "Total number of re-engagements for the current month to date"
        type: integer
        source_column: "ReEngagement_Count"
        source_table: "V_ReEngagements_MTD_DW"
        transformation_logic: "COUNT(*)"

      - name: MTD_Unique_ReEngagements
        description: "Number of unique users with re-engagements for the current month to date"
        type: integer
        source_column: "User_Id"
        source_table: "V_ReEngagements_MTD_DW"
        transformation_logic: "COUNT(DISTINCT User_Id)"

      # Lifetime-to-Date Metrics
      - name: LTD_Unique_ReEngagements
        description: "Number of unique users with re-engagements across all time"
        type: integer
        source_column: "User_Id"
        source_table: "V_ReEngagements_LTD_Unique_DW"
        transformation_logic: "COUNT(DISTINCT User_Id)"

      # Year-to-Date Metrics
      - name: YTD_Unique_ReEngagements
        description: "Number of unique users with re-engagements for the current year to date"
        type: integer
        source_column: "User_Id"
        source_table: "V_ReEngagements_YTD_Unique_DW"
        transformation_logic: "COUNT(DISTINCT User_Id)"

      # Audit Columns
      - name: As_Of_Date
        description: "Reference date for the metrics"
        type: date
        source_column: "As_Of_Date"
        source_table: "V_F_Member_Metrics_L3"

      - name: Processed_Dt_Ts
        description: "Timestamp when the record was processed"
        type: timestamp
        transformation_logic: "CURRENT_TIMESTAMP()"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"
      depends_on:
        - "V_F_Member_Metrics_L3" # [V_F_Member_Metrics_L3](/#!/model/model.Analytics.V_F_Member_Metrics_L3)
        - "V_Policy_User_DW" # [V_Policy_User_DW](/#!/model/model.Analytics.V_Policy_User_DW)
        - "V_ReEngagements_Daily_DW" # [V_ReEngagements_Daily_DW](/#!/model/model.Analytics.V_ReEngagements_Daily_DW)
        - "V_ReEngagements_MTD_DW" # [V_ReEngagements_MTD_DW](/#!/model/model.Analytics.V_ReEngagements_MTD_DW)
        - "V_ReEngagements_LTD_Unique_DW" # [V_ReEngagements_LTD_Unique_DW](/#!/model/model.Analytics.V_ReEngagements_LTD_Unique_DW)
        - "V_ReEngagements_YTD_Unique_DW" # [V_ReEngagements_YTD_Unique_DW](/#!/model/model.Analytics.V_ReEngagements_YTD_Unique_DW)



