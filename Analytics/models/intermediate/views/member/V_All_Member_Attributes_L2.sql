{{ config(
    materialized='view'
    ) 
}}

SELECT

l2.*
,po.UserId AS User_Id
,u.Username AS User_Name
,u.CreatedAt AS Registration_Date
,lu.Created_At AS Latest_Logged_In_ts

FROM {{ ref('V_F_Member_Metrics_L2') }} l2
LEFT JOIN {{ ref('V_Policy_User_DW') }} po
ON l2.Elig_Id = po.Elig_Id

LEFT JOIN {{ ref('V_User_DW') }} u
ON po.UserId = u.Id

LEFT JOIN {{ ref('V_Latest_LoggedIn_By_User_DW') }} lu
ON lu.User_Id = u.Id