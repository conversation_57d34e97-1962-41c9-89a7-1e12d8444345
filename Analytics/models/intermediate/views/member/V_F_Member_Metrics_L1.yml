version: 2

models:
  - name: V_F_Member_Metrics_L1
    description: |
      Purpose:
      This view provides base level (L1) member metrics by combining eligibility data with various
      dimensional attributes and reference data.

      Data Grain:
      One record per unique combination of eligibility ID, member number, and person number,
      representing the current state of member attributes.

      Direct Sources:
      - [V_Eligibility_DW](/#!/model/model.Analytics.V_Eligibility_DW) (base eligibility data)
      - [Conf_Codes](/#!/model/model.Analytics.Conf_Codes) (insurance type and state code mappings)
      - [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW) (plan information)
      - [V_Group_DW](/#!/model/model.Analytics.V_Group_DW) (group data)
      - [V_PartnerGroup_DW](/#!/model/model.Analytics.V_PartnerGroup_DW) (partner group relationships)
      - T_Batch_Control (batch date reference)

      Indirect Sources:
      - [ext_GCS_EligibilitiesByDateRaw](/#!/model/model.Analytics.ext_GCS_EligibilitiesByDateRaw) (source for V_Eligibility_DW)
      - [mx_configuration_ModuleConfig](/#!/model/model.Analytics.mx_configuration_ModuleConfig) (source for V_Group_DW)
      - [mx_insurance_Plan](/#!/model/model.Analytics.mx_insurance_Plan) (source for V_Plan_DW)
      - [mx_insurance_Group](/#!/model/model.Analytics.mx_insurance_Group) (source for V_Group_DW)
      - [mx_insurance_Partner](/#!/model/model.Analytics.mx_insurance_Partner) (source for V_PartnerGroup_DW)

      Key Business Rules:
      - Excludes demo groups in production environment
      - Uses current batch date for age calculations
      - Applies insurance type and state code conformance
      - Maintains relationships between eligibility, plans, and groups
      - Preserves all eligibility records through appropriate LEFT JOINs

    config:
      materialized: view
      tags: ["intermediate", "member", "member_metrics", "daily_build"]

    transformations:
      - name: batch_date_cte
        description: |
          Retrieves the current batch date from T_Batch_Control table:
          - Filters for MxDataMartDailyBuild batch_id
          - Uses batch date for age calculations
          - Serves as the as-of date for the view

      - name: base_join_structure
        description: |
          Joins eligibility data with various dimensional and reference tables:
          - Starts with eligibility data as the base
          - Applies insurance type code conformance
          - Applies state code conformance
          - Links to plan information
          - Connects to group data
          - Adds partner group relationships
        joins:
          - join: Conf_Codes (Insurance Type)
            type: inner
            relationship: many_to_one
            sql: >
              e.Insurance_Type = cc.Incoming_Cd AND
              cc.Code_Type = 'Insurance_Type'

          - join: Conf_Codes (State)
            type: left
            relationship: many_to_one
            sql: >
              UPPER(e.State) = UPPER(cc_st.Incoming_Cd) AND
              cc_st.Code_Type = 'State'

          - join: V_Plan_DW
            type: left
            relationship: many_to_one
            sql: >
              e.Plan_External_Id = pl.Plan_External_Id AND
              e.Elig_Group_Number = pl.Plan_Group_Number AND
              CAST(cc.Conformed_Cd AS STRING) = CAST(pl.Plan_Type AS STRING)

          - join: V_Group_DW
            type: left
            relationship: many_to_one
            sql: "pl.Group_Id = gp.Group_Id"

          - join: V_PartnerGroup_DW
            type: left
            relationship: many_to_one
            sql: "pgp.Group_Id = gp.Group_Id"

      - name: production_filtering
        description: |
          Applies production-specific data filtering:
          - Excludes demo groups using stg_demo_groups reference
          - Only applied in production environment
          - Maintains data integrity for reporting

    columns:
      # Key Identifiers
      - name: Elig_Id
        description: "Unique identifier for eligibility"
        type: string
        source_column: "EligibilityId"
        source_table: "V_Eligibility_DW"

      - name: Member_Number
        description: "Unique identifier for the member"
        type: string
        source_column: "MemberNumber"
        source_table: "V_Eligibility_DW"

      # Member Demographics
      - name: First_Name
        description: "Member's first name"
        type: string
        source_column: "FirstName"
        source_table: "V_Eligibility_DW"

      - name: Last_Name
        description: "Member's last name"
        type: string
        source_column: "LastName"
        source_table: "V_Eligibility_DW"

      - name: Email
        description: "Member's email address"
        type: string
        source_column: "Email"
        source_table: "V_Eligibility_DW"

      - name: Age
        description: "Calculated age based on Date_Of_Birth and current batch date"
        type: integer
        source_column: "Date_Of_Birth"
        source_table: "V_Eligibility_DW"
        transformation_logic: "DATE_DIFF(batch_date, Date_Of_Birth, YEAR)"

      # Member Identifiers
      - name: Member_SSN
        description: "Member's Social Security Number"
        type: string
        source_column: "SSN"
        source_table: "V_Eligibility_DW"

      - name: Subscriber_SSN
        description: "Subscriber's Social Security Number"
        type: string
        source_column: "SubscriberSSN"
        source_table: "V_Eligibility_DW"

      # Geographic and Administrative
      - name: State_Cd
        description: "Conformed state code, falls back to original state if no mapping exists"
        type: string
        source_column: "State"
        source_table: "V_Eligibility_DW"
        transformation_logic: "COALESCE(cc_st.Conformed_Cd, e.State)"

      # Partner and Group Information
      - name: Group_Id
        description: "Identifier for the group"
        type: string
        source_column: "Group_Id"
        source_table: "V_Group_DW"

      - name: Plan_Id
        description: "Identifier for the plan"
        type: string
        source_column: "Plan_Id"
        source_table: "V_Plan_DW"

      # Partner IDs
      - name: Billing_Partner_Id
        description: "Identifier for the billing partner"
        type: string
        source_column: "Partner_Id"
        source_table: "V_PartnerGroup_DW"

      # Audit Columns
      - name: As_Of_Date
        description: "Current batch date, used as reference date for the data"
        type: date
        source_column: "batch_date"
        source_table: "T_Batch_Control"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Eligibility_DW" # [V_Eligibility_DW](/#!/model/model.Analytics.V_Eligibility_DW)
        - "Conf_Codes" # [Conf_Codes](/#!/model/model.Analytics.Conf_Codes)
        - "V_Plan_DW" # [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW)
        - "V_Group_DW" # [V_Group_DW](/#!/model/model.Analytics.V_Group_DW)
        - "V_PartnerGroup_DW" # [V_PartnerGroup_DW](/#!/model/model.Analytics.V_PartnerGroup_DW)
        - "T_Batch_Control" # Database table, not a dbt model




