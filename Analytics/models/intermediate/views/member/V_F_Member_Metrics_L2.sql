{{ config(
    materialized='view'
    ) 
}}


WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

SELECT
    l1.*,
    CASE 
        WHEN l1.Relationship_Cd = 1 THEN 1 
        ELSE 0 
    END AS SUBSCRIBER_CNT,
    CASE 
        WHEN l1.Relationship_Cd <> 1 THEN 1 
        ELSE 0 
    END AS DEPENDENT_CNT,
    1 AS MEMBER_CNT
    ,po.REGISTERED_ELIG
    
FROM {{ ref('V_F_Member_Metrics_L1') }} l1
LEFT JOIN {{ ref('V_Policy_DW') }} po
ON l1.Elig_Id = po.Elig_Id
AND l1.Person_Number = po.Person_Number