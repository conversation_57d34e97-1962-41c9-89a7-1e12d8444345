{{ config(
    materialized='view'
    ) 
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

SELECT
 e.Elig_Id
,e.Member_Number
,e.Elig_Group_Number
,e.Person_Number
,e.First_Name
,e.Last_Name
,e.Email
,DATE_DIFF((SELECT batch_date.batch_date FROM batch_date), e.Date_Of_Birth, YEAR) AS Age
,e.Member_SSN
,e.Subscriber_SSN
,e.Relationship_Cd
,e.LOB_Cd
,CASE 
    WHEN cc_st.Conformed_Cd IS NOT NULL THEN cc_st.Conformed_Cd
    WHEN cc_st.Conformed_Cd IS NULL THEN e.State
END AS State_Cd
,cc.Conformed_Cd AS Insurance_Type
,Enr_Seq_Number
,Mem_Dep_Code
,e.TPA_Name
,gp.Group_Id
,pl.Plan_Id
,pgp.Billing_Partner_Id
,pgp.Engage_Partner_Id
,pgp.Auth_Partner_Id
,(SELECT batch_date.batch_date FROM batch_date) as As_Of_Date
    
FROM {{ ref('V_Eligibility_DW') }} e
JOIN {{ ref('Conf_Codes') }} cc
ON e.Insurance_Type = cc.Incoming_Cd
AND cc.Code_Type = 'Insurance_Type'

LEFT JOIN  {{ ref('Conf_Codes') }} cc_st
ON UPPER(e.State) = UPPER(cc_st.Incoming_Cd)
AND cc_st.Code_Type = 'State'

LEFT JOIN {{ ref('V_Plan_DW') }} pl
ON e.Plan_External_Id = pl.Plan_External_Id
AND e.Elig_Group_Number = pl.Plan_Group_Number
AND CAST(cc.Conformed_Cd AS STRING) = CAST(pl.Plan_Type AS STRING)

LEFT JOIN {{ ref('V_Group_DW') }} gp
ON pl.Group_Id  = gp.Group_Id

LEFT JOIN {{ ref('V_PartnerGroup_DW') }} pgp
ON pgp.Group_Id = gp.Group_Id

{% if target.name == 'prod' %}
WHERE NOT EXISTS (SELECT 1 FROM {{ ref('stg_demo_groups') }} stg WHERE stg.Id = gp.Group_Id)
{% endif %}
