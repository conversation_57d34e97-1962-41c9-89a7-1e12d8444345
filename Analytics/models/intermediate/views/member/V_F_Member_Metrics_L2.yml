version: 2

models:
  - name: V_F_Member_Metrics_L2
    description: |
      Purpose:
      This view extends L1 member metrics by adding registration status, user information,
      and member type counts. It serves as a comprehensive member profile view combining
      eligibility, policy, and user data.

      Data Grain:
      One record per unique combination of eligibility ID, member number, and person number,
      representing the current state of member attributes with registration status.

      Direct Sources:
      - [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1) (base member metrics)
      - [V_Policy_DW](/#!/model/model.Analytics.V_Policy_DW) (policy information)

      Indirect Sources:
      - [mx_fileprocessing_Eligibility](/#!/model/model.Analytics.mx_fileprocessing_Eligibility) (source for V_F_Member_Metrics_L1)
      - [mx_insurance_Policy](/#!/model/model.Analytics.mx_insurance_Policy) (source for V_Policy_DW)
      - [V_Eligibility_DW](/#!/model/model.Analytics.V_Eligibility_DW) (via V_F_Member_Metrics_L1)
      - [Conf_Codes](/#!/model/model.Analytics.Conf_Codes) (via V_F_Member_Metrics_L1)
      - [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW) (via V_F_Member_Metrics_L1)
      - [V_Group_DW](/#!/model/model.Analytics.V_Group_DW) (via V_F_Member_Metrics_L1)
      - [V_PartnerGroup_DW](/#!/model/model.Analytics.V_PartnerGroup_DW) (via V_F_Member_Metrics_L1)

      Key Business Rules:
      - Excludes test accounts (Username NOT LIKE '%+%')
      - Maintains relationship between eligibility and policy data
      - Preserves all member records through LEFT JOINs
      - Calculates member type counts based on relationship codes

    config:
      materialized: view
      tags: ["intermediate", "member", "member_metrics", "daily_build"]

    transformations:
      - name: base_join_structure
        description: |
          Joins member metrics with policy information:
          - Starts with L1 member metrics as the base
          - Links to policy data for registration status
        joins:
          - join: V_Policy_DW
            type: left
            relationship: many_to_one
            sql: "l1.Elig_Id = po.Elig_Id AND l1.Person_Number = po.Person_Number"

      - name: member_type_calculation
        description: |
          Calculates member type indicators based on relationship codes:
          - Identifies subscribers (relationship code = 1)
          - Identifies dependents (relationship code != 1)
          - Sets member count for each record
          - Determines registration status

      - name: batch_date_cte
        description: |
          Retrieves the current batch date from T_Batch_Control table:
          - Filters for MxDataMartDailyBuild batch_id
          - Used as reference date for the data

    columns:
      # Inherited Key Identifiers
      - name: Elig_Id
        description: "Unique identifier for eligibility"
        type: string
        source_column: "Elig_Id"
        source_table: "V_F_Member_Metrics_L1"

      - name: Member_Number
        description: "Unique identifier for the member"
        type: string
        source_column: "Member_Number"
        source_table: "V_F_Member_Metrics_L1"

      # Member Type Metrics
      - name: SUBSCRIBER_CNT
        description: "Flag indicating if the member is a subscriber"
        type: integer
        transformation_logic: "CASE WHEN l1.Relationship_Cd = 1 THEN 1 ELSE 0 END"

      - name: DEPENDENT_CNT
        description: "Flag indicating if the member is a dependent"
        type: integer
        transformation_logic: "CASE WHEN l1.Relationship_Cd <> 1 THEN 1 ELSE 0 END"

      - name: MEMBER_CNT
        description: "Count of members (always 1)"
        type: integer
        transformation_logic: "1"

      - name: REGISTERED_ELIG
        description: "Flag indicating if eligibility is registered"
        type: integer
        source_column: "REGISTERED_ELIG"
        source_table: "V_Policy_DW"

      # Inherited from L1
      - name: Person_Number
        description: "Person number from eligibility data"
        type: string
        source_column: "Person_Number"
        source_table: "V_F_Member_Metrics_L1"

      # Audit Columns
      - name: As_Of_Date
        description: "Reference date for the data"
        type: date
        source_column: "As_Of_Date"
        source_table: "V_F_Member_Metrics_L1"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_F_Member_Metrics_L1" # [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1)
        - "V_Policy_DW" # [V_Policy_DW](/#!/model/model.Analytics.V_Policy_DW)


