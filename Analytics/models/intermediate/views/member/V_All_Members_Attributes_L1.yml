version: 2

models:
  - name: V_All_Members_Attributes_L1
    description: |
      Purpose:
      This view combines L1 member metrics with user registration and login information,
      providing a comprehensive view of member attributes and their associated user details.

      Data Grain:
      One record per member eligibility ID (Elig_Id), containing member demographics,
      user registration status, and latest login information.

      Direct Sources:
      - [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1) (L1 member metrics base table)
      - [V_Base_Policy_DW](/#!/model/model.Analytics.V_Base_Policy_DW) (policy and eligibility mapping)
      - [V_Base_User_DW](/#!/model/model.Analytics.V_Base_User_DW) (user registration data)
      - [V_Latest_Login_Attempt_DW](/#!/model/model.Analytics.V_Latest_Login_Attempt_DW) (login history)

      Indirect Sources:
      - [mx_insurance_Policy](/#!/model/model.Analytics.mx_insurance_Policy) (source for V_Base_Policy_DW)
      - [mx_authentication_User](/#!/model/model.Analytics.mx_authentication_User) (source for V_Base_User_DW)
      - [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt) (source for V_Latest_Login_Attempt_DW)

      Key Business Rules:
      - Preserves all member records through LEFT JOINs
      - Links members to their most recent user registration
      - Captures latest login attempt per user
      - Maintains member-dependent relationships

    config:
      materialized: view
      tags: ["intermediate", "member", "member_attributes", "user_registration"]

    transformations:
      - name: base_join_structure
        description: |
          Joins member metrics with policy and user information:
          - Starts with L1 member metrics as the base
          - Links to base policy data for user identification
          - Connects to base user data for registration details
          - Adds latest login information
        joins:
          - join: V_Base_Policy_DW
            type: left
            relationship: many_to_one
            sql: "l1.Elig_Id = po.EligibilityId"

          - join: V_Base_User_DW
            type: left
            relationship: one_to_one
            sql: "po.UserId = bu.Id"

          - join: V_Latest_Login_Attempt_DW
            type: left
            relationship: one_to_one
            sql: "lg.User_Id = bu.Id"

      - name: member_attribute_selection
        description: |
          Selects core member attributes from L1 metrics:
          - Personal identifiers (First_Name, Last_Name)
          - Contact information (Email)
          - Enrollment details (Enr_Seq_Number)
          - Relationship codes (Mem_Dep_Code, Relationship_Cd)
          - SSN information (Subscriber_SSN, Member_SSN)
          - Member identification (Member_Number)

      - name: user_registration_processing
        description: |
          Processes user registration information:
          - Captures User_Id and Username from base user data
          - Records registration timestamp (CreatedAt)
          - Preserves all member records through LEFT JOIN
          - Null User_Id indicates unregistered members

      - name: login_tracking
        description: |
          Handles login attempt information:
          - Captures most recent login timestamp
          - Uses portal-specific login attempts only
          - Maintains login history through LEFT JOIN
          - Null login timestamp indicates no login history

    columns:
      # Key Identifiers
      - name: User_Id
        description: "Unique identifier for the user from V_Base_User_DW"
        type: string
        source_column: "Id"
        source_table: "V_Base_User_DW"

      - name: Elig_Id
        description: "Unique identifier for eligibility"
        type: string
        source_column: "EligibilityId"
        source_table: "V_Base_Policy_DW"

      # Member Demographics
      - name: First_Name
        description: "Member's first name"
        type: string
        source_column: "FirstName"
        source_table: "V_F_Member_Metrics_L1"

      - name: Last_Name
        description: "Member's last name"
        type: string
        source_column: "LastName"
        source_table: "V_F_Member_Metrics_L1"

      - name: Elig_Email
        description: "Member's email address from eligibility record"
        type: string
        source_column: "Email"
        source_table: "V_F_Member_Metrics_L1"

      # Member Identifiers
      - name: Enr_Seq_Number
        description: "Enrollment sequence number"
        type: string
        source_column: "EnrollmentSequenceNumber"
        source_table: "V_F_Member_Metrics_L1"

      - name: Mem_Dep_Code
        description: "Member dependent code"
        type: string
        source_column: "MemberDependentCode"
        source_table: "V_F_Member_Metrics_L1"

      - name: Relationship_Cd
        description: "Code indicating relationship to subscriber"
        type: string
        source_column: "RelationshipCode"
        source_table: "V_F_Member_Metrics_L1"

      - name: Subscriber_SSN
        description: "Subscriber's Social Security Number"
        type: string
        source_column: "SubscriberSSN"
        source_table: "V_F_Member_Metrics_L1"

      - name: Member_SSN
        description: "Member's Social Security Number"
        type: string
        source_column: "MemberSSN"
        source_table: "V_F_Member_Metrics_L1"

      - name: Member_Number
        description: "Unique identifier for the member"
        type: string
        source_column: "MemberNumber"
        source_table: "V_F_Member_Metrics_L1"

      # User Registration and Login
      - name: User_Name
        description: "Username from user record"
        type: string
        source_column: "Username"
        source_table: "V_Base_User_DW"

      - name: Registration_Date
        description: "Date when the user registered (from V_Base_User_DW CreatedAt)"
        type: timestamp
        source_column: "CreatedAt"
        source_table: "V_Base_User_DW"

      - name: Last_Logged_In
        description: "Timestamp of the user's most recent login attempt"
        type: timestamp
        source_column: "Created_At"
        source_table: "V_Latest_Login_Attempt_DW"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_F_Member_Metrics_L1" # [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1)
        - "V_Base_Policy_DW" # [V_Base_Policy_DW](/#!/model/model.Analytics.V_Base_Policy_DW)
        - "V_Base_User_DW" # [V_Base_User_DW](/#!/model/model.Analytics.V_Base_User_DW)
        - "V_Latest_Login_Attempt_DW" # [V_Latest_Login_Attempt_DW](/#!/model/model.Analytics.V_Latest_Login_Attempt_DW)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"



