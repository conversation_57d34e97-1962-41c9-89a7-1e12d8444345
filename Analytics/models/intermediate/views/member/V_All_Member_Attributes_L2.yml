version: 2

models:
  - name: V_All_Member_Attributes_L2
    description: |
      Purpose:
      This view combines member metrics with user registration and login information,
      providing a comprehensive view of member attributes and their engagement metrics.

      Data Grain:
      One record per member eligibility ID (Elig_Id), enriched with user registration
      and login information.

      Direct Sources:
      - [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2) (base member metrics)
      - [V_Policy_User_DW](/#!/model/model.Analytics.V_Policy_User_DW) (policy to user mapping)
      - [V_User_DW](/#!/model/model.Analytics.V_User_DW) (user registration data)
      - [V_Latest_LoggedIn_By_User_DW](/#!/model/model.Analytics.V_Latest_LoggedIn_By_User_DW) (latest login information)

      Indirect Sources:
      - [mx_authentication_User](/#!/model/model.Analytics.mx_authentication_User) (source for V_User_DW)
      - [mx_insurance_Policy](/#!/model/model.Analytics.mx_insurance_Policy) (source for V_Policy_User_DW)
      - [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt) (source for V_Latest_LoggedIn_By_User_DW)

      Key Business Rules:
      - Includes all records from V_F_Member_Metrics_L2
      - Left joins to preserve all member records even without user associations
      - Latest login timestamp is based on successful login attempts only

    config:
      materialized: view
      tags: ["intermediate", "member", "member_metrics", "user_engagement"]

    transformations:
      - name: user_association
        description: |
          Links member eligibility to user information:
          - Joins eligibility to policy-user mapping
          - Enriches with user registration details
          - Adds latest login timestamp
        joins:
          - join: V_Policy_User_DW
            type: left
            relationship: one_to_one
            sql: "l2.Elig_Id = po.Elig_Id"

          - join: V_User_DW
            type: left
            relationship: one_to_one
            sql: "po.UserId = u.Id"

          - join: V_Latest_LoggedIn_By_User_DW
            type: left
            relationship: one_to_one
            sql: "lu.User_Id = u.Id"

      - name: null_handling
        description: |
          Handles null scenarios:
          - Preserves all member records through LEFT JOINs
          - Null User_Id indicates unregistered members
          - Null Latest_Logged_In_ts indicates no login history

    columns:
      # Member Metrics (from L2)
      - name: Elig_Id
        description: "Unique identifier for member eligibility"
        type: string
        source_column: "Elig_Id"
        source_table: "V_F_Member_Metrics_L2"

      # User Information
      - name: User_Id
        description: "Unique identifier for the registered user"
        type: string
        source_column: "UserId"
        source_table: "V_Policy_User_DW"

      - name: User_Name
        description: "Username of the registered user"
        type: string
        source_column: "Username"
        source_table: "V_User_DW"

      # Registration and Login Timestamps
      - name: Registration_Date
        description: "Date when the user registered"
        type: timestamp
        source_column: "CreatedAt"
        source_table: "V_User_DW"

      - name: Latest_Logged_In_ts
        description: "Timestamp of the user's most recent successful login"
        type: timestamp
        source_column: "Created_At"
        source_table: "V_Latest_LoggedIn_By_User_DW"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_F_Member_Metrics_L2" # [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2)
        - "V_Policy_User_DW" # [V_Policy_User_DW](/#!/model/model.Analytics.V_Policy_User_DW)
        - "V_User_DW" # [V_User_DW](/#!/model/model.Analytics.V_User_DW)
        - "V_Latest_LoggedIn_By_User_DW" # [V_Latest_LoggedIn_By_User_DW](/#!/model/model.Analytics.V_Latest_LoggedIn_By_User_DW)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"


