version: 2

models:
  - name: V_Master_Event_DW
    description: |
      Intermediate view that processes and enriches Mixpanel event data with member information.
      
      Purpose: Standardizes event data by:
      - Sessionizing events (30-minute inactivity window)
      - Categorizing events into business domains
      - Enriching with member attributes
      - Standardizing event actions and categories
      
      Grain: One row per unique event (mp_insert_id) per timestamp
      
      Key Business Rules:
      - Filters events for the current batch date
      - Deduplicates events using ROW_NUMBER on mp_insert_id
      - Creates sessions based on 30-minute inactivity window
      - Categorizes events into domains (Care+, Flex, Claims, etc.)
      - Standardizes event actions (Clicked, Viewed, Submitted, etc.)

      CTEs:
        batch_date:
          description: "Gets the current batch date from T_Batch_Control for MxDataMartDailyBuild"
          
        base_events:
          description: |
            Extracts and transforms raw Mixpanel events:
            - Filters for current batch date
            - Deduplicates events using ROW_NUMBER
            - Extracts user identifiers and event properties
            - Standardizes timestamp formats
          grain: "One row per unique event (mp_insert_id)"
          
        user_sessions:
          description: |
            Creates user sessions based on event timestamps:
            - Groups events by user
            - Identifies session breaks (30-min inactivity)
            - Assigns session IDs and calculates duration
          grain: "One row per event with session attributes"
          
        event_categories:
          description: |
            Categorizes events into business domains and actions:
            - Maps events to domains (Care+, Flex, etc.)
            - Standardizes user actions (Clicked, Viewed, etc.)
            - Extracts action qualifiers
          grain: "One row per event with categorization"
          
        member_attributes:
          description: |
            Enriches events with member information:
            - Joins with member metrics for demographics
            - Adds eligibility and insurance details
            - Includes group and partner information
          grain: "One row per event with member attributes"
          
        final:
          description: |
            Combines all transformed data:
            - Merges session, category, and member data
            - Adds technical attributes (browser, OS, etc.)
            - Calculates final indicators and flags
          grain: "One row per event with all attributes"

    config:
      materialized: view
      
    columns:
      # Event Identifiers
      - name: Event_Id
        description: "Unique identifier for the event (mp_insert_id)"
        tests:
          - not_null
          - unique

      - name: sessionid
        description: "MD5 hash of userid-session_group-session_start_time"
        tests:
          - not_null

      - name: session_length
        description: "Duration of session in minutes"

      # User Identifiers
      - name: userid
        description: "User identifier from Mixpanel events"
        tests:
          - not_null

      - name: distinct_id
        description: "Alternative user identifier from Mixpanel"

      - name: mp_anon_id
        description: "Anonymous user identifier"

      # Event Attributes
      - name: Event_Time
        description: "Timestamp when the event occurred"
        tests:
          - not_null

      - name: Event_Date
        description: "Date portion of Event_Time"
        tests:
          - not_null

      - name: Time_Of_Day
        description: "Hour of day in HH:MM:00 format"

      - name: Event_Name
        description: "Original event name from Mixpanel (mp_event_name)"
        tests:
          - not_null

      - name: Event_Category_1
        description: |
          Business domain categorization:
          - Care+
          - Flex
          - Claims
          - Member Advocacy
          - ID Card
          - SSO
          - Unknown

      - name: Event_Action
        description: |
          Standardized user action:
          - Clicked
          - Failed
          - Viewed
          - Submitted
          - Uploaded
          - Downloaded
          - Opened
          - Requested
          - Filtered
          - Visited
          - Selected
          - Shared
          - Canceled
          - Continued
          - Not Applicable

      - name: Event_Action_Qualifier
        description: "Additional context for Event_Action (e.g., method channel type for ID Card sharing)"

      # Member Attributes
      - name: Elig_Id
        description: "Member eligibility identifier"

      - name: Group_Id
        description: "Insurance group identifier"

      - name: State_Cd
        description: "Member's state code"

      - name: Age
        description: "Member's age"

      - name: Billing_Partner_Id
        description: "Identifier for billing partner"

      - name: Lob_Cd
        description: "Line of business code"

      - name: TPA_Name
        description: "Third Party Administrator name"

      - name: Elig_Group_Number
        description: "Eligibility group number"

      - name: Is_Terminated
        description: "Flag indicating if member is terminated"

      - name: Elig_Termination_Date
        description: "Member's termination date if applicable"

      # Technical Attributes
      - name: mp_wifi
        description: "Boolean indicating if event occurred over WiFi"

      - name: mp_region
        description: "Geographic region of the event"

      - name: mp_os
        description: "Operating system used"

      - name: mp_os_version
        description: "Operating system version"

      - name: mp_browser
        description: "Browser used"

      - name: mp_browser_version
        description: "Browser version"

      - name: mp_city
        description: "City where event occurred"

      - name: mp_country_code
        description: "Country code where event occurred"

      - name: Fail_Event_Indicator
        description: "Boolean indicating if event failed"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2024-03-19"
      depends_on:
        - ref('V_F_Member_Metrics_L1_All')
        - ref('V_Policy_User_DW')
        - ref('V_User_DW')
        - source('mixpanel', 'mp_master_event')
      table_type: "view"
      refresh_frequency: "daily"
      tags:
        - "events"
        - "mixpanel"
        - "intermediate"
