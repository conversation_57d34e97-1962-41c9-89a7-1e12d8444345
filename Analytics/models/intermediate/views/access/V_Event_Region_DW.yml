version: 2

models:
  - name: V_Event_Region_DW
    description: |
      Purpose:
      This view provides a dimensional representation of geographic regions where user events occur.
      It captures unique combinations of cities and states from event data, creating a standardized
      dimension for geographic analysis of user interactions.
      
      Data Grain:
      One record per unique combination of city name and state name.
      
      Direct Sources:
      - [V_Master_Event_DW](/#!/model/model.Analytics.V_Master_Event_DW) (master event data with geographic information)
      
      Indirect Sources:
      - [V_Base_Event_DW](/#!/model/model.Analytics.V_Base_Event_DW) (via V_Master_Event_DW)
      - mx_mixpanel_events (via V_Base_Event_DW)
      
      Key Business Rules:
      - Handles null values by converting them to "Not Available" for consistency
      - Generates a surrogate key based on the combination of city and state
      - Ensures distinct combinations to avoid duplicates
      - Adds audit columns for tracking creation and update timestamps

    config:
      materialized: view
      tags: ["intermediate", "access", "dimension", "geography"]

    transformations:
      - name: null_handling
        description: |
          Handles null values in geographic attributes:
          - Converts null city names to "Not Available"
          - Converts null state names to "Not Available"
          - Ensures all string type consistency with CAST operations

      - name: distinct_combination_extraction
        description: |
          Extracts distinct combinations of geographic attributes:
          - Selects only unique combinations of city and state
          - Creates a base CTE with these distinct combinations
          - Prepares data for surrogate key generation

      - name: surrogate_key_generation
        description: |
          Generates a surrogate key for each unique geographic combination:
          - Combines city name and state name
          - Creates a deterministic hash-based surrogate key
          - Ensures referential integrity for the region dimension

      - name: audit_information
        description: |
          Adds audit information to each record:
          - Sets creation timestamp to current datetime
          - Sets update timestamp to current datetime
          - Leaves created by and updated by as NULL values

    columns:
      # Key Columns
      - name: Region_SK
        description: "Surrogate key for the geographic region dimension"
        type: string
        transformation_logic: "GENERATE_UUID()"

      # Geographic Information
      - name: Event_City_Name
        description: "Name of the city where the event occurred"
        type: string
        source_column: "mp_city"
        source_table: "V_Master_Event_DW"
        transformation_logic: "IFNULL(CAST(mp_city AS STRING), 'Not Available')"

      - name: Event_State_Name
        description: "Name of the state where the event occurred"
        type: string
        source_column: "mp_region"
        source_table: "V_Master_Event_DW"
        transformation_logic: "IFNULL(CAST(mp_region AS STRING), 'Not Available')"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Master_Event_DW" # [V_Master_Event_DW](/#!/model/model.Analytics.V_Master_Event_DW)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"
