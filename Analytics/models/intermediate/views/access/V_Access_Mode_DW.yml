version: 2

models:
  - name: V_Access_Mode_DW
    description: |
      Purpose:
      This view provides a dimensional representation of access modes used by users to interact with 
      the platform. It captures unique combinations of operating systems, browsers, and portal names,
      creating a standardized dimension for access mode analysis.
      
      Data Grain:
      One record per unique combination of OS name, OS version, browser name, browser version, and portal name.
      
      Direct Sources:
      - [V_Master_Event_DW](/#!/model/model.Analytics.V_Master_Event_DW) (master event data with user access information)
      
      Indirect Sources:
      - [V_Base_Event_DW](/#!/model/model.Analytics.V_Base_Event_DW) (via V_Master_Event_DW)
      - mx_mixpanel_events (via V_Base_Event_DW)
      
      Key Business Rules:
      - Handles null values by converting them to "Not Available" for consistency
      - Generates a surrogate key based on the combination of all access mode attributes
      - Ensures distinct combinations to avoid duplicates

    config:
      materialized: view
      tags: ["intermediate", "access", "dimension"]

    transformations:
      - name: null_handling
        description: |
          Handles null values in access mode attributes:
          - Converts null OS name to "Not Available"
          - Converts null OS version to "Not Available"
          - Converts null browser name to "Not Available"
          - Converts null browser version to "Not Available"
          - Ensures all string type consistency with CAST operations

      - name: distinct_combination_extraction
        description: |
          Extracts distinct combinations of access mode attributes:
          - Selects only unique combinations of OS, browser, and portal attributes
          - Creates a base CTE with these distinct combinations
          - Prepares data for surrogate key generation

      - name: surrogate_key_generation
        description: |
          Generates a surrogate key for each unique access mode combination:
          - Combines OS name, OS version, browser name, browser version, and portal name
          - Creates a deterministic hash-based surrogate key
          - Ensures referential integrity for the access mode dimension

    columns:
      # Key Columns
      - name: Access_Mode_SK
        description: "Surrogate key for the access mode dimension"
        type: string
        transformation_logic: "GENERATE_UUID()"

      # Operating System Information
      - name: OS_Name
        description: "Name of the operating system used for access"
        type: string
        source_column: "mp_os"
        source_table: "V_Master_Event_DW"
        transformation_logic: "IFNULL(CAST(mp_os AS STRING), 'Not Available')"

      - name: OS_Version
        description: "Version of the operating system used for access"
        type: string
        source_column: "mp_os_version"
        source_table: "V_Master_Event_DW"
        transformation_logic: "IFNULL(CAST(mp_os_version AS STRING), 'Not Available')"

      # Browser Information
      - name: Browser_Name
        description: "Name of the browser used for access"
        type: string
        source_column: "mp_browser"
        source_table: "V_Master_Event_DW"
        transformation_logic: "IFNULL(CAST(mp_browser AS STRING), 'Not Available')"

      - name: Browser_Version
        description: "Version of the browser used for access"
        type: string
        source_column: "mp_browser_version"
        source_table: "V_Master_Event_DW"
        transformation_logic: "IFNULL(CAST(mp_browser_version AS STRING), 'Not Available')"

      # Portal Information
      - name: Portal_Name
        description: "Name of the portal accessed by the user"
        type: string
        source_column: "Portal_Name"
        source_table: "V_Master_Event_DW"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Master_Event_DW" # [V_Master_Event_DW](/#!/model/model.Analytics.V_Master_Event_DW)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"
