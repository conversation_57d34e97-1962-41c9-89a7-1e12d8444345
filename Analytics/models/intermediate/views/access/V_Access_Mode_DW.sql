{{ config(
    materialized='view'
    ) 
}}

WITH base AS (
    SELECT
        DISTINCT
        mp_os  AS OS_Name
        ,mp_os_version AS OS_Version
        ,mp_browser  AS Browser_Name
        ,mp_browser_version  AS Browser_Version
        ,Portal_Name

    FROM {{ ref('V_Master_Event_DW') }}
)

SELECT
    {{ dbt_utils.generate_surrogate_key(
        ['OS_Name'
        ,'OS_Version'
        ,'Browser_Name'
        ,'Browser_Version'
        ,'Portal_Name'
        ]
    )}} AS Access_Mode_SK
    ,OS_Name
    ,OS_Version
    ,Browser_Name
    ,Browser_Version
    ,Portal_Name

FROM base