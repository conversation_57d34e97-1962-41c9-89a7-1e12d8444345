{{ config(
    materialized='view'
    ) 
}}


WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

,latest_member_info AS (
    SELECT 
        ep.Elig_Id,
        ep.Member_Number,
        ep.Elig_Group_Number,
        ep.Person_Number,
        ep.First_Name,
        ep.Last_Name,
        ep.Email,
        ep.Age,
        ep.Member_SSN,
        ep.Subscriber_SSN,
        ep.Relationship_Cd,
        ep.LOB_Cd,
        ep.Is_Terminated,
        ep.Elig_Termination_Date,
        ep.State_Cd,
        ep.Enr_Seq_Number,
        ep.Mem_Dep_Code,
        ep.TPA_Name,
        ep.Group_Id,
        ep.Plan_Id,
        ep.Billing_Partner_Id,
        ep.Engage_Partner_Id,
        ep.Auth_Partner_Id,
        ep.As_Of_Date,
        pu.UserId,
        vu.CreatedAt AS Registration_Date,

    FROM {{ ref('V_F_Member_Metrics_L1_All') }} ep
    LEFT JOIN {{ ref('V_Policy_User_DW') }} pu ON ep.Elig_Id = pu.Elig_Id
    LEFT JOIN {{ ref('V_User_DW') }} vu ON pu.UserId = vu.Id

        QUALIFY ROW_NUMBER() OVER (
            PARTITION BY pu.UserId
            ORDER BY ep.Elig_Termination_Date DESC
        ) = 1
)

,base_events AS (
    SELECT *  
    FROM {{ source(
            'mixpanel',
            'mp_master_event'
        ) }}
    WHERE (DATE(time) = (select batch_date.batch_date from batch_date)
       OR DATE(time) = DATE_SUB((select batch_date.batch_date from batch_date), INTERVAL 1 DAY))
    AND userid IS NOT NULL
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY mp_insert_id
        ORDER BY mp_mp_api_timestamp_ms
    ) = 1
)

,sessionized_events AS (

    WITH ordered_events AS (
        SELECT
            time,
            userid,
            mp_insert_id,
            LAG(time) OVER (PARTITION BY userid ORDER BY time) AS previous_time
            
        FROM base_events
    )
    ,flagged_events AS (
        SELECT
            time,
            userid,
            mp_insert_id,
            IF(previous_time IS NULL OR TIMESTAMP_DIFF(time, previous_time, MINUTE) > 30, 1, 0) AS new_session_flag
        FROM ordered_events
    )
    ,sessionized AS (
        SELECT
            time,
            userid,
            mp_insert_id,
            SUM(new_session_flag) OVER (PARTITION BY userid ORDER BY time) AS session_group
        FROM flagged_events
    )
    ,session_ids AS (
        SELECT
            userid,
            session_group,
            MIN(time) AS session_start_time,
            MAX(time) AS session_end_time,
            MD5(CONCAT(CAST(userid AS STRING), '-', CAST(session_group AS STRING), '-', CAST(MIN(time) AS STRING))) AS sessionid,
            TIMESTAMP_DIFF(MAX(time), MIN(time), MINUTE) AS session_length
        FROM sessionized
        GROUP BY userid, session_group
    )
    SELECT 
        s.time,
        s.userid,
        s.mp_insert_id,
        si.sessionid,
        si.session_length
    FROM sessionized s
    JOIN session_ids si
        ON s.userid = si.userid 
        AND s.session_group = si.session_group
    ORDER BY s.userid, s.time

)



,base AS (
    SELECT
        mp.userid,
        uri,
        title,
        mp.time AS Event_Time,
        DATE(mp.time) AS Event_Date,
        CONCAT(CAST(FORMAT_TIMESTAMP('%H:%M', DATETIME(mp.time)) AS STRING), ':00') AS Time_Of_Day,
        DATE_TRUNC(DATE(mp.TIME), MONTH) AS year_month,
        path,
        name,
        CASE 
            WHEN mp_wifi = True THEN 1 
            ELSE 0
        END AS mp_wifi,
        mp_region,
        IFNULL(CAST(mp_os_version AS STRING), "Not Available") AS mp_os_version,
        IFNULL(CAST(mp_os AS STRING), "Not Available") AS mp_os,
        CASE 
            WHEN mp_failure_reason IS NOT NULL THEN 1
            ELSE 0
        END AS Fail_Event_Indicator,
        mp_country_code,
        mp_city,
        mp_carrier,
        IFNULL(CAST(mp_browser_version AS STRING), "Not Available") AS mp_browser_version,
        IFNULL(CAST(mp_browser AS STRING), "Not Available") AS mp_browser,
        mp_brand,
        mp_anon_id,
        modal_name,
        mp.member_number,
        member,
        insurancetype,
        COALESCE(CAST(groupid AS INTEGER), ep.Group_Id) AS Group_Id,
        group_number,
        filters_applied,
        enddate,
        mp.email,
        documenturl,
        distinct_id,
        currentview,
        claimnumber,
        claimid,
        mp.mp_insert_id AS Event_Id,
        IFNULL(moduleconfigid_1, "Not Applicable") AS Module_Config_Id,
        v_mc.Module_Id,
        v_mc.Module_Name,
        ep.State_Cd,
        ep.Age,
        ep.Billing_Partner_Id,
        ep.Lob_Cd,
        ep.TPA_Name,
        ep.Elig_Group_Number,
        ep.Elig_Id,
        CASE 
            WHEN ep.Relationship_Cd = 1 THEN 'Subscriber'
            ELSE 'Dependent'
        END AS Member_Type,
        CASE 
            WHEN ep.Is_Terminated = 0 THEN 'No'
            ELSE 'Yes' 
        END AS Termination_Status,
        ep.Elig_Termination_Date,
        mp_event_name AS Event_Name,
        programname AS Program_Name,
        cta_type AS CTA_Type,
        methodchanneltype AS Method_Channel_Type,
        CASE 
            WHEN lower(mp_event_name) LIKE 'intake form%' AND (programname NOT LIKE 'specialist%') THEN 'Care+'
            WHEN lower(mp_event_name) LIKE 'intake form%' AND LOWER(programname) LIKE 'specialist%' THEN 'Virtual Primary Care/Specialist'
            WHEN mp_event_name LIKE 'Virtual Primary Care%' THEN 'Virtual Primary Care/Specialist'
            WHEN mp_event_name LIKE '%FSA%' THEN 'Flex'
            WHEN mp_event_name LIKE '%Medical Form%' or mp_event_name LIKE '%Dental Form%' or mp_event_name LIKE '%Vision Form%' or lower(mp_event_name) like 'new%claim%submitted' or lower(mp_event_name) like 'submit claim%' THEN 'Claims'
            WHEN mp_event_name = 'Your Member Advocacy Call Now Clicked' 
            AND DATE(mp.time) < DATE('2025-07-31')
            THEN 'Member Advocacy'
            WHEN v_mc.Module_Name = 'AlliedMemberAdvocacyProgram' 
                AND mp_event_name = 'External Link Phone Visited' 
                AND DATE(mp.time) >= DATE('2025-07-31')
            THEN 'Member Advocacy'
            WHEN mp_event_name LIKE '%ID Card%' THEN 'ID Card'
            WHEN mp_event_name LIKE 'Dashboard Widget SSO Visited%' THEN 'SSO'
            ELSE 'Unknown'
        END AS Event_Category_1,
        CASE 
            WHEN lower(mp_event_name) LIKE '%clicked%' THEN 'Clicked'
            WHEN lower(mp_event_name) LIKE '%failed%' THEN 'Failed'
            WHEN lower(mp_event_name) LIKE '%view%' THEN 'Viewed'
            WHEN lower(mp_event_name) LIKE '%submitted%' THEN 'Submitted'
            WHEN lower(mp_event_name) LIKE '%submitted%' and lower(mp_event_name) LIKE '%claim%' THEN 'Uploaded'
            WHEN lower(mp_event_name) LIKE '%downloaded%' THEN 'Downloaded'
            WHEN lower(mp_event_name) LIKE '%opened%' THEN 'Opened'
            WHEN lower(mp_event_name) LIKE '%requested%' THEN 'Requested'
            WHEN lower(mp_event_name) LIKE '%filtered%' THEN 'Filtered'
            WHEN lower(mp_event_name) LIKE '%visited%' THEN 'Visited'
            WHEN lower(mp_event_name) LIKE '%selected%' THEN 'Selected'
            WHEN lower(mp_event_name) LIKE '%shared%' THEN 'Shared'
            WHEN lower(mp_event_name) LIKE '%canceled%' THEN 'Canceled'
            WHEN lower(mp_event_name) LIKE '%continued%' THEN 'Continued'
            ELSE 'Not Applicable'
        END AS Event_Action,
        CASE 
            WHEN mp_event_name LIKE '%ID Card%' AND lower(mp_event_name) LIKE '%shared%' THEN methodchanneltype
            ELSE 'Not Applicable'
        END AS Event_Action_Qualifier,
        CASE 
            WHEN lower(mp_event_name) LIKE 'intake form%' THEN programname
            WHEN mp_event_name LIKE '%FSA%' and mp_event_name LIKE '%Dependent%' THEN 'Dependent Reimbursement Form'
            WHEN mp_event_name LIKE '%FSA%' and mp_event_name NOT LIKE '%Dependent%' THEN 'Subscriber Reimbursement Form'
            WHEN LOWER(mp_event_name) LIKE '%dental form%' THEN 'Dental Claims Form'
            WHEN LOWER(mp_event_name) LIKE '%vision form%' THEN 'Vision Claims Form'
            WHEN LOWER(mp_event_name) LIKE '%medical form%' THEN 'Medical Claims Form'
            WHEN LOWER(mp_event_name) LIKE '%internation%medical form%' THEN 'International Medical Claims Form'
            WHEN lower(mp_event_name) like 'new%claim%submitted' or lower(mp_event_name) like 'submit claim%' THEN 'Claim Submission'
                ELSE 'Not Applicable'
        END AS Event_Category_2,
        CASE 
            WHEN mp_os IN ('iOS', 'Android', 'iPadOS') AND mp_browser IS NOT NULL THEN 'Mobile Web'
            WHEN mp_os IN ('iOS', 'Android', 'iPadOS') AND mp_browser IS NULL THEN 'App'
            ELSE 'Web'
        END AS Portal_Access_Mode,
        'Member Portal' AS Portal_Name,
        mp_mp_api_timestamp_ms,
        ep.Registration_Date,
        DATE_DIFF(DATE(mp.time), DATE(ep.Registration_Date), DAY) AS Days_Since_Registered,
        CASE WHEN ep.Is_Terminated = 1 AND DATE(mp.time) > DATE(ep.Elig_Termination_Date) THEN DATE_DIFF(DATE(mp.time), DATE(ep.Elig_Termination_Date), DAY) ELSE null END AS Days_Since_Terminated,
        se.sessionid,
        se.session_length
    FROM base_events mp
    JOIN sessionized_events se
        ON mp.mp_insert_id = se.mp_insert_id
    LEFT JOIN latest_member_info ep
        ON mp.userid = CAST(ep.UserId AS STRING)
    LEFT JOIN {{ ref('V_Module_Config_DW') }} v_mc
        ON IFNULL(moduleconfigid_1, "Not Applicable") = CAST(v_mc.Module_Config_Id AS STRING)
    WHERE 
            mp_event_name <> '$identify'
        
        AND mp_event_name <> 'Back To Login From Verification Page'
        AND mp_event_name <> 'Cancel Active Popup'
        AND (impersonation = False OR impersonation IS NULL)
        AND ep.UserId IS NOT NULL
)


SELECT * FROM base
