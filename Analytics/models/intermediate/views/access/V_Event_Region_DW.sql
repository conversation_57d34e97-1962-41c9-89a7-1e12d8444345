{{ config(
    materialized='view'
    ) 
}}

WITH base AS (
    SELECT
        DISTINCT
        IFNULL(CAST(mp_city AS STRING), "Not Available") AS Event_City_Name
        ,IFNULL(CAST(mp_region AS STRING), "Not Available") AS Event_State_Name

    FROM {{ ref('V_Master_Event_DW') }}
)

SELECT
    {{ dbt_utils.generate_surrogate_key(
        ['Event_City_Name'
        ,'Event_State_Name'
        ]
    )}} AS Region_SK
    ,Event_City_Name
    ,Event_State_Name
    ,current_datetime() AS Created_dt_ts
    ,current_datetime() AS Updated_dt_ts
    ,CAST(NULL AS STRING) AS Created_By
    ,CAST(NULL AS STRING) AS Updated_By

FROM base