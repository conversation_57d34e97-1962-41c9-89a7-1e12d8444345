{{ config(
    materialized='view'
    ) 
}}

WITH base AS (
    SELECT 
        DISTINCT 
        Event_Name
        ,Event_Category_1
        ,Event_Category_2
        ,Event_Action
        ,Event_Action_Qualifier

    FROM {{ ref('V_Master_Event_DW')}}

)

SELECT

    {{ dbt_utils.generate_surrogate_key(
        ['Event_Name'
        ,'Event_Category_1'
        ,'Event_Category_2'
        ,'Event_Action'
        ,'Event_Action_Qualifier'
        ]
    )}} AS Event_SK
    ,Event_Name
    ,Event_Category_1
    ,Event_Category_2
    ,Event_Action
    ,Event_Action_Qualifier
    ,current_datetime() AS Created_dt_ts
    ,current_datetime() AS Updated_dt_ts
    ,CAST(NULL AS STRING) AS Created_By
    ,CAST(NULL AS STRING) AS Updated_By

FROM base
