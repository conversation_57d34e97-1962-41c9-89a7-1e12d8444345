{{ config(
    materialized='view'
    ) 
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

,base AS (
    SELECT 
    -- Module Config fields
    mc.Module_Config_SK,
    mc.Module_Config_Id,
    mc.Module_Id,
    mc.Group_Id,
    mc.Module_Name,
    mc.Module_Config_Settings,
    mc.Module_Config_Menu_Name,
    IFNULL(JSON_EXTRACT_SCALAR(mc.Module_Config_Settings, '$.Uri'),null) AS Module_Link,
    CASE WHEN IFNULL(JSON_EXTRACT_SCALAR(mc.Module_Config_Settings, '$.HasCaseMgmt'),"Not Applicable") = 'true' THEN 1
    ELSE 0
    END AS Has_Case_Mgmt,
    CASE  WHEN IFNULL(JSON_EXTRACT_SCALAR(mc.Module_Config_Settings, '$.HasDiabetesMgmt'),"Not Applicable") = 'true' THEN 1
    ELSE 0
    END AS Has_Diabetes,
    CASE WHEN IFNULL(JSON_EXTRACT_SCALAR(mc.Module_Config_Settings, '$.HasOncology'),"Not Applicable") = 'true' THEN 1
    ELSE 0
    END AS Has_Oncology,
    CASE WHEN IFNULL(JSON_EXTRACT_SCALAR(mc.Module_Config_Settings, '$.HasBehavioralHealth'),"Not Applicable") = 'true' THEN 1
    ELSE 0
    END AS Has_Behavioral_Health,
    CASE WHEN IFNULL(JSON_EXTRACT_SCALAR(mc.Module_Config_Settings, '$.HasWellnessSupport'),"Not Applicable") = 'true' THEN 1
    ELSE 0
    END AS Has_Wellness_Support,
    CASE WHEN mc.Module_Id = 41 THEN 1
    ELSE 0
    END AS Has_Care_Plus

    FROM {{ ref('V_Module_Config_DW') }} mc

    WHERE mc.Module_Config_Active = 1
)

,unpivoted AS (
    SELECT 
        (SELECT batch_date.batch_date FROM batch_date) AS As_Of_Date,
        Group_Id,
        Module_Id,
        Module_Name,
        Module_Config_Menu_Name,
        Module_Link,
        specialty,
        specialty_value,
    FROM base
    UNPIVOT(
        specialty_value FOR specialty IN (
            Has_Case_Mgmt,
            Has_Diabetes,
            Has_Oncology,
            Has_Behavioral_Health,
            Has_Wellness_Support
        )
    )
    WHERE Module_Id = 41
    AND specialty_value = 1
)

,specialty_mapping AS (
    SELECT 
        'Has_Case_Mgmt' as specialty,
        'Care Plus Case Management' as module_header
    UNION ALL
    SELECT 
        'Has_Diabetes',
        'Care Plus Diabetes Management'
    UNION ALL
    SELECT 
        'Has_Oncology',
        'Care Plus Oncology'
    UNION ALL
    SELECT 
        'Has_Behavioral_Health',
        'Care Plus Behavioral Health'
    UNION ALL
    SELECT 
        'Has_Wellness_Support',
        'Care Plus Wellness Support'
)

-- Final query combining regular records and unpivoted specialty records
SELECT 
    (SELECT batch_date.batch_date FROM batch_date) AS As_Of_Date,
    Group_Id,
    Module_Id,
    Module_Name,
    Module_Config_Menu_Name,
    Module_Link,
    '' AS Program_Name
FROM base
WHERE Module_Id NOT IN (7,41)

UNION ALL

-- Base Care Plus record
SELECT 
    (SELECT batch_date.batch_date FROM batch_date) AS As_Of_Date,
    Group_Id,
    Module_Id,
    Module_Name,
    Module_Config_Menu_Name,
    Module_Link,
    '' AS Program_Name
FROM base
WHERE Module_Id = 41

UNION ALL

SELECT 
    (SELECT batch_date.batch_date FROM batch_date) AS As_Of_Date,
    Group_Id,
    Module_Id,
    Module_Name,
    Module_Config_Menu_Name,
    Module_Link,
    '' AS Program_Name
FROM base
WHERE Module_Id = 7

UNION ALL

-- Specialty records
SELECT 
    u.As_Of_Date,
    u.Group_Id,
    u.Module_Id,
    u.Module_Name,
    u.Module_Config_Menu_Name,
    u.Module_Link,
    m.module_header AS Program_Name,
FROM unpivoted u
JOIN specialty_mapping m
    ON u.specialty = m.specialty
JOIN base
    ON u.Group_Id = base.Group_Id
    AND u.Module_Id = base.Module_Id
