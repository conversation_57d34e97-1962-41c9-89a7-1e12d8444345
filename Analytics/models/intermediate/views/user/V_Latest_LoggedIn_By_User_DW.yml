version: 2

models:
  - name: V_Latest_LoggedIn_By_User_DW
    description: |
      Purpose:
      This view provides the most recent successful login attempt for each user in the member portal.
      It's primarily used for tracking user engagement and determining the last time a user accessed the system.

      Data Grain:
      One record per User_Id, representing only their most recent successful login attempt.

      Direct Sources:
      - [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt) (login attempt history)
      - T_Batch_Control (batch date reference)

      Indirect Sources:
      - [mx_authentication_User](/#!/model/model.Analytics.mx_authentication_User) (referenced through UserId)

      Key Business Rules:
      - Filters for successful logins only (Success = 1)
      - Filters for member portal context only (Context = 'mx.portal.member')
      - Applies batch date filter to include only login attempts up to the current batch date
      - Takes the most recent login attempt per user based on CreatedAt timestamp

    config:
      materialized: view
      tags: ["intermediate", "user", "UserLogin", "Authentication", "MxDataMartDailyBuild"]

    columns:
      # User Identification
      - name: User_Id
        description: "Unique identifier for the user who attempted to log in"
        type: string
        source_column: "UserId"
        source_table: "mx_authentication_LoginAttempt"

      - name: User_Name
        description: "Username or email used for the login attempt"
        type: string
        source_column: "Username"
        source_table: "mx_authentication_LoginAttempt"

      # Timestamp
      - name: Created_At
        description: "Timestamp of the most recent successful login attempt"
        type: timestamp
        source_column: "CreatedAt"
        source_table: "mx_authentication_LoginAttempt"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_authentication_LoginAttempt" # [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"

    transformations:
      - name: batch_date_filtering
        description: |
          Applies temporal filtering based on batch control:
          - References MxDataMartDailyBuild batch date
          - Filters login attempts up to batch date
        joins:
          - join: T_Batch_Control
            type: cross
            relationship: many_to_one
            sql: "batch_id = 'MxDataMartDailyBuild'"

      - name: login_deduplication
        description: |
          Ensures single record per user:
          - Partitions by UserId
          - Orders by CreatedAt DESC
          - Takes top record per partition using ROW_NUMBER() window function
          - Filters for successful logins only (Success = 1)
          - Filters for member portal context (Context = 'mx.portal.member')