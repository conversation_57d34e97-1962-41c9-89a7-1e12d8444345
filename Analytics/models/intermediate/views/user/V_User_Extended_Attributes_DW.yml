version: 2

models:
  - name: V_User_Extended_Attributes_DW
    description: |
      Purpose:
      This view provides extended user attributes by joining various base tables to create a comprehensive
      user profile including associated group, billing partner, and engagement partner information.

      Data Grain:
      One record per User_Id, representing the most recent effective policy relationship.

      Direct Sources:
      - [V_Base_Policy_DW](/#!/model/model.Analytics.V_Base_Policy_DW) (policy information)
      - [V_Base_Plan_DW](/#!/model/model.Analytics.V_Base_Plan_DW) (plan details)
      - [V_Base_Group_DW](/#!/model/model.Analytics.V_Base_Group_DW) (group information)
      - [V_Base_PartnerGroup_DW](/#!/model/model.Analytics.V_Base_PartnerGroup_DW) (partner-group relationships)
      - [V_Base_Billing_Partner_DW](/#!/model/model.Analytics.V_Base_Billing_Partner_DW) (billing partner details)
      - [V_Base_Engage_Partner_DW](/#!/model/model.Analytics.V_Base_Engage_Partner_DW) (engagement partner details)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      - [mx_insurance_Policy](/#!/model/model.Analytics.mx_insurance_Policy) (via V_Base_Policy_DW)
      - [mx_insurance_Plan](/#!/model/model.Analytics.mx_insurance_Plan) (via V_Base_Plan_DW)
      - [mx_insurance_Group](/#!/model/model.Analytics.mx_insurance_Group) (via V_Base_Group_DW)
      - [mx_insurance_PartnerGroup](/#!/model/model.Analytics.mx_insurance_PartnerGroup) (via V_Base_PartnerGroup_DW)
      - [mx_insurance_Partner](/#!/model/model.Analytics.mx_insurance_Partner) (via V_Base_Billing_Partner_DW and V_Base_Engage_Partner_DW)

      Key Business Rules:
      - Only includes active records across all joined tables
      - Filters based on batch control date validity
      - Excludes records with null User_Id
      - Takes most recent effective policy per user
      - Requires non-terminated groups

    config:
      materialized: view
      tags: ["intermediate", "user", "UserAttributes", "MxDataMartDailyBuild"]

    transformations:
      - name: base_join_structure
        description: |
          Creates a comprehensive user profile by joining multiple base tables:
          - Starts with base policy data as the foundation
          - Links to plan information for group identification
          - Connects to group data for organizational context
          - Adds partner group relationships
          - Includes both billing and engagement partner details
        joins:
          - join: V_Base_Plan_DW
            type: inner
            relationship: many_to_one
            sql: "p.PlanId = pl.Id"

          - join: V_Base_Group_DW
            type: inner
            relationship: many_to_one
            sql: "pl.GroupId = g.Id"

          - join: V_Base_PartnerGroup_DW
            type: inner
            relationship: many_to_one
            sql: "pg.GroupId = g.Id"

          - join: V_Base_Billing_Partner_DW
            type: inner
            relationship: many_to_one
            sql: "pg.PartnerId = vbp.Id"

          - join: V_Base_Engage_Partner_DW
            type: inner
            relationship: many_to_one
            sql: "pg.PartnerId = vep.Id"

      - name: date_filtering
        description: |
          Applies temporal validity rules:
          - Filters records based on batch control date
          - Ensures policy effective dates are valid
          - Checks group termination dates
          - Takes most recent effective policy per user

    columns:
      # Key Columns
      - name: User_Id
        description: "Unique identifier for the user"
        type: string
        source_column: "UserId"
        source_table: "V_Base_Policy_DW"

      - name: Group_Id
        description: "Identifier for the associated group"
        type: string
        source_column: "Id"
        source_table: "V_Base_Group_DW"

      - name: Billing_Partner_Id
        description: "Identifier for the billing partner"
        type: string
        source_column: "Id"
        source_table: "V_Base_Billing_Partner_DW"

      - name: Engage_Partner_Id
        description: "Identifier for the engagement partner"
        type: string
        source_column: "Id"
        source_table: "V_Base_Engage_Partner_DW"

      # Date Columns
      - name: EffectiveFrom
        description: "Start date of the policy effectiveness"
        type: timestamp
        source_column: "EffectiveFrom"
        source_table: "V_Base_Policy_DW"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Base_Policy_DW" # [V_Base_Policy_DW](/#!/model/model.Analytics.V_Base_Policy_DW)
        - "V_Base_Plan_DW" # [V_Base_Plan_DW](/#!/model/model.Analytics.V_Base_Plan_DW)
        - "V_Base_Group_DW" # [V_Base_Group_DW](/#!/model/model.Analytics.V_Base_Group_DW)
        - "V_Base_PartnerGroup_DW" # [V_Base_PartnerGroup_DW](/#!/model/model.Analytics.V_Base_PartnerGroup_DW)
        - "V_Base_Billing_Partner_DW" # [V_Base_Billing_Partner_DW](/#!/model/model.Analytics.V_Base_Billing_Partner_DW)
        - "V_Base_Engage_Partner_DW" # [V_Base_Engage_Partner_DW](/#!/model/model.Analytics.V_Base_Engage_Partner_DW)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"