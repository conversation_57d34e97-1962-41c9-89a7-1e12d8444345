{{ config(
    materialized='view'
    ) 
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

,re_engagement_threshold AS (
    SELECT 
    Value AS threshold 
    FROM {{ ref("Batch_Configuration_Variables") }}
    WHERE Name = 'Re-Engagement-Threshold'
)

,base AS (
    SELECT 
        *
    FROM {{ ref('mx_authentication_LoginAttempt') }}
    WHERE DATE(CreatedAt) = (SELECT batch_date.batch_date FROM batch_date)
    AND Context = 'mx.portal.member'
    AND Success = 1
    {% if target.name == 'prod' %}
    AND Username NOT LIKE '%+%'
    {% endif %}

)

SELECT
    base.UserId AS User_Id,
    base.CreatedAt AS Login_Dt_ts
FROM base

JOIN {{ ref('V_Base_User_DW') }} bu
ON base.UserId = bu.Id

WHERE DATETIME_DIFF(base.CreatedAt,bu.CreatedAt, HOUR) >= (SELECT re_engagement_threshold.threshold FROM re_engagement_threshold)