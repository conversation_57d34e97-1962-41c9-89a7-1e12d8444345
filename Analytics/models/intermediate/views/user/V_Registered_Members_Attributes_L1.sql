{{ config(
    materialized='view'
    ) 
}}

SELECT

bu.Id AS User_Id
,bu.Username AS User_Name
,l1.Elig_Id
,l1.First_Name
,l1.Last_Name
,l1.Email AS Elig_Email
,l1.Enr_Seq_Number
,l1.Mem_Dep_Code
,bu.CreatedAt AS Registration_Date
,lg.Created_At AS Last_Logged_In
,l1.Elig_Group_Number AS Group_Number
,bg.Name AS Group_Name
,bp.Name AS Plan_Name
,PlnProductMap.PlanProduct AS Plan_Product_Name

FROM {{ ref('V_F_Member_Metrics_L1') }} l1
LEFT JOIN {{ ref('V_Base_Policy_DW') }} po
ON l1.Elig_Id = po.EligibilityId

LEFT JOIN {{ ref('V_Base_User_DW') }} bu
ON po.UserId = bu.Id

LEFT JOIN {{ ref('V_Latest_Login_Attempt_DW') }} lg
ON lg.User_Id = bu.Id

LEFT JOIN {{ ref('V_Base_Group_DW') }} bg
ON l1.Group_Id = bg.Id

LEFT JOIN {{ ref('V_Base_Plan_DW') }} bp
ON l1.Plan_Id = bp.Id

LEFT JOIN {{ ref('Allied_PlanId_To_PlanProduct')}} PlnProductMap 
ON bp.Plan_Number = PlnProductMap.PlanNumber

WHERE po.UserId IS NOT NULL

