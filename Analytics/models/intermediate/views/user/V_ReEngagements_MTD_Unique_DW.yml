version: 2

models:
  - name: V_ReEngagements_MTD_Unique_DW
    description: |
      Purpose:
      This view provides the most recent month-to-date re-engagement for each user in the member portal.
      It captures only the latest successful re-engagement per user from the start of the current month,
      where a re-engagement is defined as a login occurring after a configured threshold period from
      the user's creation date.

      Data Grain:
      One record per User_Id, representing only the most recent re-engagement for each user in the current month.

      Direct Sources:
      - [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt) (login attempt data)
      - [V_Base_User_DW](/#!/model/model.Analytics.V_Base_User_DW) (user registration data)
      - [Batch_Configuration_Variables](/#!/model/model.Analytics.Batch_Configuration_Variables) (re-engagement threshold configuration)
      - T_Batch_Control (batch date reference)

      Indirect Sources:
      - [mx_authentication_User](/#!/model/model.Analytics.mx_authentication_User) (via V_Base_User_DW)

      Key Business Rules:
      - Filters for successful logins only (Success = 1)
      - Filters for member portal context only (Context = 'mx.portal.member')
      - Time between user creation and login must exceed configured threshold (in hours)
      - In production, excludes test accounts (usernames containing '+')
      - Includes login attempts from the start of the current month up to the current batch date
      - Deduplicates to keep only the most recent login per user in the current month

    config:
      materialized: view
      tags: ["intermediate", "user", "UserEngagement", "Authentication", "MxDataMartDailyBuild"]

    columns:
      # User Identification
      - name: User_Id
        description: "Unique identifier for the re-engaged user"
        type: string
        source_column: "UserId"
        source_table: "mx_authentication_LoginAttempt"

      # Timestamp
      - name: Login_Dt_ts
        description: "Timestamp of the most recent re-engagement login attempt"
        type: timestamp
        source_column: "CreatedAt"
        source_table: "mx_authentication_LoginAttempt"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_authentication_LoginAttempt" # [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt)
        - "V_Base_User_DW" # [V_Base_User_DW](/#!/model/model.Analytics.V_Base_User_DW)
        - "T_Batch_Control" # Database table, not a dbt model
        - "Batch_Configuration_Variables" # [Batch_Configuration_Variables](/#!/model/model.Analytics.Batch_Configuration_Variables)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"

    transformations:
      - name: batch_date_filtering
        description: |
          Applies month-to-date temporal filtering:
          - References MxDataMartDailyBuild batch date
          - Filters from month start to batch date
        joins:
          - join: T_Batch_Control
            type: cross
            relationship: many_to_one
            sql: "batch_id = 'MxDataMartDailyBuild'"

      - name: threshold_configuration
        description: |
          Retrieves re-engagement threshold configuration:
          - Gets threshold value from Batch_Configuration_Variables
          - Uses 'Re-Engagement-Threshold' as the configuration name
        joins:
          - join: Batch_Configuration_Variables
            type: cross
            relationship: many_to_one
            sql: "Name = 'Re-Engagement-Threshold'"

      - name: re_engagement_calculation
        description: |
          Determines re-engagement status:
          - Joins login attempts with user registration data
          - Calculates hours between user creation and login
          - Compares to configured threshold
          - Filters for qualifying re-engagements
        joins:
          - join: V_Base_User_DW
            type: inner
            relationship: many_to_one
            sql: "base.UserId = bu.Id"

      - name: mtd_deduplication
        description: |
          Ensures single record per user in the current month:
          - Partitions by User_Id
          - Orders by Login_Dt_ts DESC
          - Takes top record per partition using ROW_NUMBER() window function