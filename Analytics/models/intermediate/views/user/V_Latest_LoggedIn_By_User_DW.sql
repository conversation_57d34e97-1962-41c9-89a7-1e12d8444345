{{ config(
    materialized='view'
    ) 
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)



,base AS (
    SELECT 
        
        UserId
        ,Username
        ,Success
        ,Context
        ,CreatedAt

    FROM {{ ref('mx_authentication_LoginAttempt') }}
    
    WHERE DATE(CreatedAt) <= (SELECT batch_date.batch_date FROM batch_date)
    AND Context = 'mx.portal.member'
    AND Success = 1

)

SELECT

UserId as User_Id
,Username AS User_Name
,CreatedAt as Created_At

FROM 

base

QUALIFY ROW_NUMBER() OVER (
    PARTITION BY UserId
    ORDER BY CreatedAt DESC
) = 1
