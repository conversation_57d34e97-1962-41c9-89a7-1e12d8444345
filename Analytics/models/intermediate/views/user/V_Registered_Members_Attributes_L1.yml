version: 2

models:
  - name: V_Registered_Members_Attributes_L1
    description: |
      Purpose:
      This view combines member attributes with user registration and login information for registered members only.
      It provides a comprehensive view of member details including their registration status, last login,
      and associated group/plan information.

      Data Grain:
      One record per Elig_Id, representing registered members with their associated user and plan information.

      Direct Sources:
      - [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1) (base member metrics)
      - [V_Base_Policy_DW](/#!/model/model.Analytics.V_Base_Policy_DW) (policy information)
      - [V_Base_User_DW](/#!/model/model.Analytics.V_Base_User_DW) (user registration data)
      - [V_Latest_Login_Attempt_DW](/#!/model/model.Analytics.V_Latest_Login_Attempt_DW) (login history)
      - [V_Base_Group_DW](/#!/model/model.Analytics.V_Base_Group_DW) (group information)
      - [V_Base_Plan_DW](/#!/model/model.Analytics.V_Base_Plan_DW) (plan information)
      - [Allied_PlanId_To_PlanProduct](/#!/model/model.Analytics.Allied_PlanId_To_PlanProduct) (plan product mapping)

      Indirect Sources:
      - [V_Eligibility_DW](/#!/model/model.Analytics.V_Eligibility_DW) (via V_F_Member_Metrics_L1)
      - [mx_authentication_User](/#!/model/model.Analytics.mx_authentication_User) (via V_Base_User_DW)
      - [mx_authentication_LoginAttempt](/#!/model/model.Analytics.mx_authentication_LoginAttempt) (via V_Latest_Login_Attempt_DW)
      - [mx_insurance_Group](/#!/model/model.Analytics.mx_insurance_Group) (via V_Base_Group_DW)
      - [mx_insurance_Plan](/#!/model/model.Analytics.mx_insurance_Plan) (via V_Base_Plan_DW)

      Key Business Rules:
      - Filters for registered members only (UserId IS NOT NULL)
      - Joins with policy data for user relationships
      - Maintains member-group-plan relationships
      - Includes latest login information when available

    config:
      materialized: view
      tags: ["intermediate", "user", "MemberAttributes", "Registration", "MxDataMartDailyBuild"]

    columns:
      # User Information
      - name: User_Id
        description: "Unique identifier for the registered user"
        type: string
        source_column: "Id"
        source_table: "V_Base_User_DW"

      - name: User_Name
        description: "User's login username"
        type: string
        source_column: "Username"
        source_table: "V_Base_User_DW"

      # Member Information
      - name: Elig_Id
        description: "Eligibility identifier for the member"
        type: string
        source_column: "Elig_Id"
        source_table: "V_F_Member_Metrics_L1"

      - name: First_Name
        description: "Member's first name from eligibility data"
        type: string
        source_column: "First_Name"
        source_table: "V_F_Member_Metrics_L1"

      - name: Last_Name
        description: "Member's last name from eligibility data"
        type: string
        source_column: "Last_Name"
        source_table: "V_F_Member_Metrics_L1"

      - name: Elig_Email
        description: "Member's email from eligibility data"
        type: string
        source_column: "Email"
        source_table: "V_F_Member_Metrics_L1"

      - name: Enr_Seq_Number
        description: "Enrollment sequence number"
        type: string
        source_column: "Enr_Seq_Number"
        source_table: "V_F_Member_Metrics_L1"

      - name: Mem_Dep_Code
        description: "Member dependency code"
        type: string
        source_column: "Mem_Dep_Code"
        source_table: "V_F_Member_Metrics_L1"

      # Registration Information
      - name: Registration_Date
        description: "Date when the member registered (created user account)"
        type: timestamp
        source_column: "CreatedAt"
        source_table: "V_Base_User_DW"

      - name: Last_Logged_In
        description: "Timestamp of the most recent login"
        type: timestamp
        source_column: "Created_At"
        source_table: "V_Latest_Login_Attempt_DW"

      # Group and Plan Information
      - name: Group_Number
        description: "Eligibility group number"
        type: string
        source_column: "Elig_Group_Number"
        source_table: "V_F_Member_Metrics_L1"

      - name: Group_Name
        description: "Name of the associated group"
        type: string
        source_column: "Name"
        source_table: "V_Base_Group_DW"

      - name: Plan_Name
        description: "Name of the associated plan"
        type: string
        source_column: "Name"
        source_table: "V_Base_Plan_DW"

      - name: Plan_Product_Name
        description: "Mapped plan product name"
        type: string
        source_column: "PlanProduct"
        source_table: "Allied_PlanId_To_PlanProduct"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_F_Member_Metrics_L1" # [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1)
        - "V_Base_Policy_DW" # [V_Base_Policy_DW](/#!/model/model.Analytics.V_Base_Policy_DW)
        - "V_Base_User_DW" # [V_Base_User_DW](/#!/model/model.Analytics.V_Base_User_DW)
        - "V_Latest_Login_Attempt_DW" # [V_Latest_Login_Attempt_DW](/#!/model/model.Analytics.V_Latest_Login_Attempt_DW)
        - "V_Base_Group_DW" # [V_Base_Group_DW](/#!/model/model.Analytics.V_Base_Group_DW)
        - "V_Base_Plan_DW" # [V_Base_Plan_DW](/#!/model/model.Analytics.V_Base_Plan_DW)
        - "Allied_PlanId_To_PlanProduct" # [Allied_PlanId_To_PlanProduct](/#!/model/model.Analytics.Allied_PlanId_To_PlanProduct)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"

    transformations:
      - name: base_join_structure
        description: |
          Joins member metrics with policy and user information:
          - Starts with L1 member metrics as the base
          - Links to base policy data for user identification
          - Connects to base user data for registration details
          - Adds latest login information
        joins:
          - join: V_Base_Policy_DW
            type: left
            relationship: many_to_one
            sql: "l1.Elig_Id = po.EligibilityId"

          - join: V_Base_User_DW
            type: left
            relationship: one_to_one
            sql: "po.UserId = bu.Id"

          - join: V_Latest_Login_Attempt_DW
            type: left
            relationship: one_to_one
            sql: "lg.User_Id = bu.Id"

      - name: group_plan_mapping
        description: |
          Adds group and plan information:
          - Links to base group data
          - Links to base plan data
          - Maps plan products
        joins:
          - join: V_Base_Group_DW
            type: left
            relationship: many_to_one
            sql: "l1.Group_Id = bg.Id"

          - join: V_Base_Plan_DW
            type: left
            relationship: many_to_one
            sql: "l1.Plan_Id = bp.Id"

          - join: Allied_PlanId_To_PlanProduct
            type: left
            relationship: many_to_one
            sql: "bp.Plan_Number = PlnProductMap.PlanNumber"

      - name: registered_user_filtering
        description: |
          Ensures only registered members are included:
          - Applies WHERE po.UserId IS NOT NULL filter
          - Excludes members without user accounts