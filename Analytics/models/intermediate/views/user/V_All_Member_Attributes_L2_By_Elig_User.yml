version: 2

models:
  - name: V_All_Member_Attributes_L2_By_Elig_User
    description: |
      Purpose:
      This view provides a deduplicated view of member attributes at the eligibility level,
      handling both registered and unregistered members differently. It's primarily used for
      member analytics and reporting where we need one record per eligibility ID.

      Data Grain:
      One record per Elig_Id, representing the most relevant member record based on registration status and login history.

      Direct Sources:
      - [V_All_Member_Attributes_L2](/#!/model/model.Analytics.V_All_Member_Attributes_L2) (base member attributes view)

      Indirect Sources:
      - [V_All_Member_Attributes_L1](/#!/model/model.Analytics.V_All_Member_Attributes_L1) (via V_All_Member_Attributes_L2)
      - [V_Eligibility_DW](/#!/model/model.Analytics.V_Eligibility_DW) (via V_All_Member_Attributes_L1)
      - [V_Base_User_DW](/#!/model/model.Analytics.V_Base_User_DW) (via V_All_Member_Attributes_L1)

      Key Business Rules:
      - For Registered Members (Registration_Date IS NOT NULL):
        * One record per Elig_Id
        * Takes the record with most recent login (Latest_Logged_In_ts)

      - For Unregistered Members (Registration_Date IS NULL):
        * One record per Elig_Id
        * Takes any record since no login history exists

      - For Valid SSN Cases (SSN != '999999999'):
        * Further deduplicates on (Subscriber_SSN, Group_Id, Billing_Partner_Id)
        * Takes record with highest Member_Number within each group

      - For Default SSN Cases (SSN = '999999999'):
        * Maintains same deduplication logic as valid SSN cases
        * Processed separately to preserve data integrity

    config:
      materialized: view
      tags: ["intermediate", "user", "MemberAttributes", "UserRegistration"]

    sources:
      - name: V_All_Member_Attributes_L2
        description: "Base view containing all member attributes"
        columns:
          - name: Elig_Id
          - name: Registration_Date
          - name: Latest_Logged_In_ts
          - name: Subscriber_SSN
          - name: SUBSCRIBER_CNT
          - name: REGISTERED_ELIG
          - name: Member_Number
          - name: Group_Id
          - name: Billing_Partner_Id

    columns:
      # Identification
      - name: Elig_Id
        description: "Unique identifier for eligibility record"
        type: string
        source_column: "Elig_Id"
        source_table: "V_All_Member_Attributes_L2"

      - name: Member_Number
        description: "Unique identifier for the member"
        type: string
        source_column: "Member_Number"
        source_table: "V_All_Member_Attributes_L2"

      # Registration Information
      - name: Registration_Date
        description: "Date when the member registered in the system"
        type: timestamp
        source_column: "Registration_Date"
        source_table: "V_All_Member_Attributes_L2"

      - name: Latest_Logged_In_ts
        description: "Most recent login timestamp for registered members"
        type: timestamp
        source_column: "Latest_Logged_In_ts"
        source_table: "V_All_Member_Attributes_L2"

      - name: REGISTERED_ELIG
        description: "Flag indicating if eligibility record is registered (0 or 1)"
        type: integer
        source_column: "REGISTERED_ELIG"
        source_table: "V_All_Member_Attributes_L2"

      # Subscriber Information
      - name: Subscriber_SSN
        description: "Social Security Number of the subscriber"
        type: string
        source_column: "Subscriber_SSN"
        source_table: "V_All_Member_Attributes_L2"

      - name: SUBSCRIBER_CNT
        description: "Count of subscribers associated with the SSN/Group combination"
        type: integer
        source_column: "SUBSCRIBER_CNT"
        source_table: "V_All_Member_Attributes_L2"

      # Organization Information
      - name: Group_Id
        description: "Identifier for the member's group"
        type: string
        source_column: "Group_Id"
        source_table: "V_All_Member_Attributes_L2"

      - name: Billing_Partner_Id
        description: "Identifier for the billing partner"
        type: string
        source_column: "Billing_Partner_Id"
        source_table: "V_All_Member_Attributes_L2"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_All_Member_Attributes_L2" # [V_All_Member_Attributes_L2](/#!/model/model.Analytics.V_All_Member_Attributes_L2)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"

    transformations:
      - name: registered_members_processing
        description: |
          Processes registered members:
          - Filters where Registration_Date IS NOT NULL
          - Takes latest record by Latest_Logged_In_ts
          - Deduplicates by Elig_Id
        joins:
          - join: V_All_Member_Attributes_L2
            type: inner
            relationship: one_to_many
            sql: "Registration_Date IS NOT NULL"

      - name: unregistered_members_processing
        description: |
          Processes unregistered members:
          - Filters where Registration_Date IS NULL
          - Takes one record per Elig_Id
        joins:
          - join: V_All_Member_Attributes_L2
            type: inner
            relationship: one_to_many
            sql: "Registration_Date IS NULL"

      - name: valid_ssn_processing
        description: |
          Processes members with valid SSN:
          - Filters where Subscriber_SSN != '999999999'
          - Handles single and multiple subscriber cases
          - Deduplicates by (Subscriber_SSN, Group_Id, Billing_Partner_Id)
          - Takes record with highest Member_Number

      - name: default_ssn_processing
        description: |
          Processes members with default SSN:
          - Filters where Subscriber_SSN = '999999999'
          - Maintains same deduplication logic as valid SSN cases
          - Processes separately to preserve data integrity