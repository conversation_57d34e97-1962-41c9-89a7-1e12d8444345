{{ config(
    materialized='view'
    ) 
}}

WITH base AS (

    SELECT 

    *

    FROM

    {{ ref('V_All_Member_Attributes_L2') }}

    WHERE Registration_Date IS NOT NULL
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY Elig_Id
        ORDER BY Latest_Logged_In_ts DESC
    ) = 1

    UNION ALL

    SELECT 

    *

    FROM

    {{ ref('V_All_Member_Attributes_L2') }}

    WHERE 
    Registration_Date IS NULL

    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY Elig_Id
    ) = 1

)

,distinct_ssn_reg_subs as (
  SELECT * 
    FROM base
    WHERE Subscriber_SSN <> '999999999'
    AND SUBSCRIBER_CNT = 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Subscriber_SSN,Group_Id,Billing_Partner_Id ORDER BY Member_Number DESC) = 1

  UNION ALL

    SELECT * 
    FROM base
    WHERE Subscriber_SSN = '999999999'
    AND SUBSCRIBER_CNT = 1
)

,distinct_ssn_reg_deps as (
  SELECT * 
    FROM base
    WHERE Member_SSN <> '999999999'
    AND SUBSCRIBER_CNT <> 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Member_SSN,Group_Id,Billing_Partner_Id ORDER BY Member_Number DESC) = 1

  UNION ALL

    SELECT * 
    FROM base
    WHERE Member_SSN = '999999999'
    AND SUBSCRIBER_CNT <> 1
)

SELECT * FROM distinct_ssn_reg_subs

UNION ALL

SELECT * FROM distinct_ssn_reg_deps