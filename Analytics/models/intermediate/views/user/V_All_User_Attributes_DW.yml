version: 2

models:
  - name: V_All_User_Attributes_DW
    description: |
      Purpose:
      This view combines member metrics with user registration information to provide
      a comprehensive view of user attributes. It focuses on registered members only,
      filtering out records where UserId is NULL.

      Data Grain:
      One record per User_Id and Elig_Id combination per batch_control_dt, representing
      the current state of user attributes and their associated eligibility information.

      Direct Sources:
      - [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1) (base L1 member metrics)
      - [V_Policy_User_DW](/#!/model/model.Analytics.V_Policy_User_DW) (policy-user mapping)

      Indirect Sources:
      - [V_Eligibility_DW](/#!/model/model.Analytics.V_Eligibility_DW) (via V_F_Member_Metrics_L1)
      - [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW) (via V_F_Member_Metrics_L1)
      - [V_Group_DW](/#!/model/model.Analytics.V_Group_DW) (via V_F_Member_Metrics_L1)
      - [V_PartnerGroup_DW](/#!/model/model.Analytics.V_PartnerGroup_DW) (via V_F_Member_Metrics_L1)
      - [Conf_Codes](/#!/model/model.Analytics.Conf_Codes) (via V_F_Member_Metrics_L1)

      Key Business Rules:
      - Filters for registered members only (UserId IS NOT NULL)
      - Maintains all L1 member attributes
      - Preserves eligibility and policy relationships

    config:
      materialized: view
      tags: ["intermediate", "user", "UserAttributes", "MemberMetrics", "MxDataMartDailyBuild"]

    columns:
      # Key Identifiers
      - name: User_Id
        description: "Unique identifier for the registered user"
        type: string
        source_column: "UserId"
        source_table: "V_Policy_User_DW"

      - name: Elig_Id
        description: "Eligibility identifier for the member"
        type: string
        source_column: "Elig_Id"
        source_table: "V_F_Member_Metrics_L1"

      # Inherited from L1 Member Metrics
      - name: Member_Number
        description: "Unique identifier for the member"
        type: string
        source_column: "Member_Number"
        source_table: "V_F_Member_Metrics_L1"

      - name: First_Name
        description: "Member's first name"
        type: string
        source_column: "First_Name"
        source_table: "V_F_Member_Metrics_L1"

      - name: Last_Name
        description: "Member's last name"
        type: string
        source_column: "Last_Name"
        source_table: "V_F_Member_Metrics_L1"

      - name: Email
        description: "Member's email address"
        type: string
        source_column: "Email"
        source_table: "V_F_Member_Metrics_L1"

      - name: Age
        description: "Member's age"
        type: integer
        source_column: "Age"
        source_table: "V_F_Member_Metrics_L1"

      - name: State_Cd
        description: "Member's state code"
        type: string
        source_column: "State_Cd"
        source_table: "V_F_Member_Metrics_L1"

      # Group and Partner Information
      - name: Group_Id
        description: "Identifier for the group"
        type: string
        source_column: "Group_Id"
        source_table: "V_F_Member_Metrics_L1"

      - name: Billing_Partner_Id
        description: "Identifier for the billing partner"
        type: string
        source_column: "Billing_Partner_Id"
        source_table: "V_F_Member_Metrics_L1"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_F_Member_Metrics_L1" # [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1)
        - "V_Policy_User_DW" # [V_Policy_User_DW](/#!/model/model.Analytics.V_Policy_User_DW)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"

    transformations:
      - name: base_join_structure
        description: |
          Joins member metrics with policy-user mapping:
          - Starts with L1 member metrics as the base
          - Links to policy-user mapping for user identification
        joins:
          - join: V_Policy_User_DW
            type: left
            relationship: many_to_one
            sql: "l1.Elig_Id = pu.Elig_Id"

      - name: registered_user_filtering
        description: |
          Filters for registered users only:
          - Applies WHERE pu.UserId IS NOT NULL filter
          - Ensures only registered members are included
