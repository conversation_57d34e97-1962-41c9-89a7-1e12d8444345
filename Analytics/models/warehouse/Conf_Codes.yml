version: 2

models:
  - name: Conf_Codes
    description: |
      This model transforms conformance codes data from the underlying conformance_codes table.
      It provides standardized reference data for downstream consumption and ensures
      data consistency through proper validation and standardization.

    config:
      materialized: table
      tags: ["MxDataMartDailyBuild"]

    sources:
      - name: conformance_codes
        description: "Source table containing conformance codes data"
        source_table: "conformance_codes"

    transformations:
      - name: base_transformation
        description: |
          Applies initial transformations and adds derived columns:
          - Standardizes code values
          - Validates code types
          - Ensures data consistency
        filters:
          - "code IS NOT NULL"
          - "code_type IS NOT NULL"
        deduplication:
          partition_by: "code, code_type"
          order_by: "updated_at DESC"
          keep: "first"

      - name: business_rules
        description: |
          Applies business rules to filter codes:
          - Only valid code types
          - Non-empty code values
          - Active codes only

    columns:
      - name: Code_SK
        description: "Surrogate key generated from code attributes"
        source_columns:
          - code
          - code_type
          - description
          - is_active

      - name: Code
        description: "Unique code value"
        source_column: "code"
        source_table: "conformance_codes"

      - name: Code_Type
        description: "Type or category of the code"
        source_column: "code_type"
        source_table: "conformance_codes"

      - name: Description
        description: "Detailed description of the code"
        source_column: "description"
        source_table: "conformance_codes"

      - name: Is_Active
        description: "Flag indicating if the code is currently active"
        source_column: "is_active"
        source_table: "conformance_codes"

      - name: Created_dt_ts
        description: "Date and time when the record was created"
        source_column: "created_at"
        source_table: "conformance_codes"

      - name: Updated_dt_ts
        description: "Date and time when the record was last updated"
        source_column: "updated_at"
        source_table: "conformance_codes"

      - name: Created_By
        description: "User or process that created the record"
        source_column: "created_by"
        source_table: "conformance_codes"

      - name: Updated_By
        description: "User or process that last updated the record"
        source_column: "updated_by"
        source_table: "conformance_codes"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - ref('conformance_codes')
      table_type: "reference"
      refresh_frequency: "daily"
      data_governance:
        classification: "internal"
        retention_period: "permanent"


