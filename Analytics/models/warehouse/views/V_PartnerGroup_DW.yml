version: 2

models:
  - name: V_PartnerGroup_DW
    description: |
      Purpose:
      This view consolidates partner group relationships, providing a denormalized view of
      group-to-partner associations by partner type (Billing, Engage, Auth).

      Data Grain:
      One record per Group ID with associated partner IDs by type.

      Direct Sources:
      - mx_partnerorganization_PartnerGroup (snapshot table with partner-group relationships)
      - V_Partner_DW (partner type classifications)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      - mx_partnerorganization_Partner (underlying source for V_Partner_DW)

      Key Business Rules:
      - Filters records based on batch control date validity
      - Maintains latest version of partner-group relationships
      - Pivots partner relationships by type (Billing, Engage, Auth)
      - Handles temporal validity through SCD Type 2 pattern

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: batch_date_filtering
        description: |
          Establishes temporal context using batch control date:
          - Retrieves current batch date from T_Batch_Control
          - Filters records valid on batch date
          - Ensures consistent temporal snapshot
        sql: >
          SELECT CAST(batch_control_dt AS DATE) AS batch_date
          FROM T_Batch_Control
          WHERE batch_id = 'MxDataMartDailyBuild'

      - name: temporal_validity_handling
        description: |
          Processes temporal validity of partner-group relationships:
          - Converts dbt_valid_from/to to DATE type
          - Adjusts valid_to by subtracting one day when not 9999-12-31
          - Selects latest record per ID based on dbt_updated_at

      - name: partner_type_pivot
        description: |
          Pivots partner relationships by type:
          - Starts with base partner-group relationships
          - Links to partner data for type classification
          - Pivots relationships into separate columns by partner type
        joins:
          - join: V_Partner_DW
            type: inner
            relationship: many_to_one
            sql: "base.PartnerId = partner_data.Partner_Id"

    columns:
      # Key Columns
      - name: Group_Id
        description: "Unique identifier for the group"
        type: string
        source_column: "GroupId"
        source_table: "mx_partnerorganization_PartnerGroup"

      # Partner Relationships
      - name: Billing_Partner_Id
        description: "ID of the associated billing partner"
        type: string
        transformation_logic: "MAX(CASE WHEN partner_data.Partner_Type = 'Billing' THEN base.PartnerId ELSE NULL END)"

      - name: Engage_Partner_Id
        description: "ID of the associated engage partner"
        type: string
        transformation_logic: "MAX(CASE WHEN partner_data.Partner_Type = 'Engage' THEN base.PartnerId ELSE NULL END)"

      - name: Auth_Partner_Id
        description: "ID of the associated auth partner"
        type: string
        transformation_logic: "MAX(CASE WHEN partner_data.Partner_Type = 'Auth' THEN base.PartnerId ELSE NULL END)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_partnerorganization_PartnerGroup"
        - "V_Partner_DW"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "One record per group with associated partner IDs"
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Partners categorized by type (Billing, Engage, Auth)"
        - "NULL values preserved for missing partner relationships"


