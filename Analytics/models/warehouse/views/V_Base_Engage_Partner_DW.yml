version: 2

models:
  - name: V_Base_Engage_Partner_DW
    description: |
      Purpose:
      This view transforms partner data to provide a standardized base view of Engage partners,
      maintaining temporal validity and versioning. It serves as a foundation for 
      engagement-related partner analytics.

      Data Grain:
      One record per partner ID per validity period.

      Direct Sources:
      - mx_partnerorganization_Partner (snapshot table with partner data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes partners with names starting with '[Engage]'
      - Maintains SCD Type 2 temporal validity
      - Records must be valid on the batch control date
      - Latest version selected when multiple versions exist

    config:
      materialized: view
      tags: []

    transformations:
      - name: batch_date_control
        description: |
          Retrieves the current processing date:
          - Gets batch control date for MxDataMartDailyBuild
          - Ensures proper date casting for comparisons
        sql: >
          SELECT 
              CAST(batch_control_dt AS DATE) AS batch_date
          FROM `medxoom-dev-fb5`.`Warehouse`.`T_Batch_Control`
          WHERE batch_id = 'MxDataMartDailyBuild'

      - name: temporal_validity
        description: |
          Manages temporal validity of records:
          - Converts dbt_valid_from to valid_from date
          - Adjusts valid_to by subtracting one day when not 9999-12-31
          - Ensures proper date boundaries and prevents overlaps
        sql: >
          CASE 
              WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
              THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
              ELSE DATE('9999-12-31')
          END AS valid_to

      - name: latest_version_selection
        description: |
          Selects the latest version of each partner:
          - Partitions by id
          - Orders by dbt_updated_at DESC
          - Takes the first record
        sql: >
          ROW_NUMBER() OVER (
              PARTITION BY id
              ORDER BY dbt_updated_at DESC
          ) = 1

      - name: engage_partner_filter
        description: |
          Filters for Engage partners:
          - Only includes partners with names starting with '[Engage]'
        sql: >
          Name LIKE '[Engage]%'

    columns:
      # Key Columns
      - name: id
        description: "Primary identifier for the partner"
        type: string
        source_column: "id"
        source_table: "mx_partnerorganization_Partner"

      # Temporal Columns
      - name: valid_from
        description: "Date when this version becomes valid"
        type: date
        source_column: "dbt_valid_from"
        source_table: "mx_partnerorganization_Partner"
        transformation_logic: "CAST(dbt_valid_from AS DATE)"

      - name: valid_to
        description: "Date when this version expires"
        type: date
        source_column: "dbt_valid_to"
        source_table: "mx_partnerorganization_Partner"
        transformation_logic: >
          CASE 
              WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
              THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
              ELSE DATE('9999-12-31')
          END

      # Audit Columns
      - name: dbt_valid_from
        description: "Original dbt SCD Type 2 valid from timestamp"
        type: timestamp
        source_column: "dbt_valid_from"
        source_table: "mx_partnerorganization_Partner"

      - name: dbt_valid_to
        description: "Original dbt SCD Type 2 valid to timestamp"
        type: timestamp
        source_column: "dbt_valid_to"
        source_table: "mx_partnerorganization_Partner"

      - name: dbt_updated_at
        description: "Timestamp of last dbt update"
        type: timestamp
        source_column: "dbt_updated_at"
        source_table: "mx_partnerorganization_Partner"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_partnerorganization_Partner"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Only partners with names starting with '[Engage]' are included"
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Valid_to dates adjusted by subtracting one day"
        - "Maximum valid_to date is set to '9999-12-31'"




