{{ config(
    materialized='view'
    ) 
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

,base as (
    SELECT 
    *,
    CAST(dbt_valid_from AS DATE) valid_from,
    case when COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') then DATE_SUB(CAST(dbt_valid_to AS DATE) , INTERVAL 1 DAY)
    else DATE('9999-12-31')
    end as valid_to,
    SPLIT(ExternalId,'-')[0] as Plan_Number
    FROM {{ ref('mx_insurance_Plan')}}

    WHERE 
    CAST(dbt_valid_from AS DATE) <= (select batch_date.batch_date from batch_date)
    AND COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) >= (select batch_date.batch_date from batch_date)
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY id
        ORDER BY dbt_updated_at DESC
    ) = 1
)


SELECT 
    {{ dbt_utils.generate_surrogate_key(
        [
            'base.Id',
            'Type',
            'ExternalId',
            'Active',
            'Name',
            'GroupNumber',
            'GroupId',
            'PlnProductMap.PlanProduct'
        ]
     ) -}} as Plan_SK,
    valid_from as Effective_From_Dt,
    valid_to as Effective_To_Dt,
    base.Id as Plan_Id,
    Type as Plan_Type,
    ExternalId as Plan_External_Id,
    base.Plan_Number,
    PlnProductMap.PlanProduct as Plan_Product_Name,
    GroupNumber as Plan_Group_Number,
    Name as Plan_Name,
    Active as Plan_Active,
    GroupId AS Group_Id,
    CURRENT_DATETIME as Created_dt_ts,
    CURRENT_DATETIME as Updated_dt_ts,
    CAST(null AS STRING) as Created_By,
    CAST(null AS STRING) as Updated_By
FROM 
    base
LEFT JOIN {{ ref('Allied_PlanId_To_PlanProduct')}} PlnProductMap 
ON base.Plan_Number = PlnProductMap.PlanNumber

WHERE Active = 1