version: 2

models:
  - name: V_Policy_DW
    description: |
      Purpose:
      This view aggregates policy registration data, providing registration metrics and counts
      at the eligibility and person level. It implements temporal validity handling and ensures
      only active, registered policies are included.

      Data Grain:
      One record per unique combination of EligibilityId and PersonNumber for active registrations.

      Direct Sources:
      - mx_insurance_Policy (snapshot table containing policy data)
      - T_Batch_Control (batch processing dates for MxDataMartDailyBuild)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes active policies (Active = 1)
      - Only includes records with non-null UserId
      - Filters based on batch control date validity
      - Maintains SCD Type 2 temporal handling

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: batch_date_processing
        description: |
          Establishes temporal context using batch control date:
          - Retrieves current batch date from T_Batch_Control
          - Used for filtering valid records
          - Ensures consistent temporal snapshot
        sql: >
          SELECT CAST(batch_control_dt AS DATE) AS batch_date
          FROM T_Batch_Control
          WHERE batch_id = 'MxDataMartDailyBuild'

      - name: temporal_validity_handling
        description: |
          Processes temporal validity of records:
          - Converts dbt_valid_from to DATE type
          - Adjusts dbt_valid_to by subtracting one day when not 9999-12-31
          - Filters records valid on batch date
          - Selects latest record per ID based on dbt_updated_at
        business_logic_steps:
          - "Convert validity dates to DATE type"
          - "Adjust end dates to prevent temporal overlaps"
          - "Filter for records valid on batch date"
          - "Select latest version of each record"

      - name: latest_record_selection
        description: |
          Identifies latest registration per combination:
          - Groups by EligibilityId, PersonId, UserId
          - Orders by CreatedAt descending
          - Selects top record per group
        business_logic_steps:
          - "Partition by key fields"
          - "Order by creation timestamp"
          - "Select row_number = 1"

    columns:
      # Key Columns
      - name: Elig_Id
        description: "Eligibility identifier"
        type: string
        source_column: "EligibilityId"
        source_table: "mx_insurance_Policy"

      - name: Person_Number
        description: "Person identifier number"
        type: string
        source_column: "PersonNumber"
        source_table: "mx_insurance_Policy"

      # Metric Columns
      - name: REGISTERED_CNT
        description: "Count of registrations for the eligibility-person combination"
        type: integer
        transformation_logic: "count(*)"

      - name: REGISTERED_ELIG
        description: "Flag indicating active registration (always 1 for included records)"
        type: integer
        transformation_logic: "1"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_insurance_Policy"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Records must be valid on batch control date"
        - "Only active policies are included"
        - "UserId must not be null"
        - "Latest registration per combination is selected"
        - "Valid_to dates adjusted to prevent temporal overlaps"
        - "Maximum valid_to date is 9999-12-31"




