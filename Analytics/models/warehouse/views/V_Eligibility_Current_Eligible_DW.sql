{{ config(
    materialized='view'
    ) 
}}

WITH filtered_data AS (
  SELECT *
  FROM {{ ref('V_Eligibility_Current_DW') }}
  WHERE (TRIM(personnumber) <> '' AND personnumber IS NOT NULL)
    AND (membernumber IS NOT NULL AND TRIM(membernumber) <> '')
    AND LOWER(membernumber) NOT LIKE '%demo%'
    AND active = 1
    AND (TerminationDate = '' OR TerminationDate IS NULL OR CAST(TerminationDate AS DATE) >= CURRENT_DATE())
    AND CAST(EffectiveDate AS DATE) <=CURRENT_DATE()
),

ranked_data AS (
  SELECT *,
    ROW_NUMBER() OVER (
      PARTITION BY membernumber, groupnumber, personnumber, InsuranceType, PlanId
      ORDER BY CAST(EffectiveDate AS DATE) DESC, CreatedAt DESC
    ) AS row_num
  FROM filtered_data
)

SELECT 

Id as Elig_Id
,membernumber as Member_Number
,GroupNumber as Elig_Group_Number
,PersonNumber as Person_Number
,PlanId as Plan_External_Id
,InsuranceType as Insurance_Type
,FirstName as First_Name
,LastName as Last_Name
,Email
,CAST(DateOfBirth as DATE) as Date_Of_Birth
,SSN as Member_SSN
,COALESCE(SubscriberSSN , '999999999') as Subscriber_SSN
,Relationship as Relationship_Cd
,PayerName as TPA_Name
,TerminationDate as Elig_Termination_Date
,EffectiveDate as Elig_Effective_Date
,Active as Elig_Active
,State
,TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.DirectoryId')) as LOB_Cd
,TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.EnrSeqNumber')) as Enr_Seq_Number
,TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.MemDepCode')) as Mem_Dep_Code

FROM ranked_data
WHERE row_num = 1
AND CAST(DateOfBirth as DATE) < CURRENT_DATE()
AND DateOfBirth <> '0001-01-01'