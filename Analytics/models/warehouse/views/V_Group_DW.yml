version: 2

models:
  - name: V_Group_DW
    description: |
      Purpose:
      This view transforms insurance group data, providing a temporally-aware view of group
      information with standardized attributes and effective dating.

      Data Grain:
      One record per group ID per validity period.

      Direct Sources:
      - mx_insurance_Group (snapshot table with group data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes active groups (Active = 1)
      - Excludes frozen groups (Frozen = 0)
      - Filters out terminated groups (TerminationDate >= batch_date or null)
      - Maintains SCD Type 2 temporal validity
      - Adjusts valid_to dates to prevent temporal overlaps

    config:
      materialized: view
      tags: []

    transformations:
      - name: batch_date_extraction
        description: |
          Extracts the processing date from batch control:
          - Retrieves current batch date for MxDataMartDailyBuild
          - Casts batch control date to DATE type

      - name: temporal_processing
        description: |
          Processes temporal validity for groups:
          - Sets valid_from based on dbt_valid_from
          - Adjusts valid_to by subtracting one day from dbt_valid_to
          - Handles default end date (9999-12-31)
          - Qualifies latest record per group using ROW_NUMBER

      - name: termination_handling
        description: |
          Processes group termination dates:
          - Handles empty strings and null values
          - Casts valid termination dates to DATE type
          - Filters based on termination date vs batch date

    columns:
      # Key Columns
      - name: Group_SK
        description: "Surrogate key for the group"
        type: string
        transformation_logic: "GENERATE_UUID()"

      - name: Group_Id
        description: "Primary identifier for the group"
        type: string
        source_column: "Id"
        source_table: "mx_insurance_Group"

      - name: Group_External_Id
        description: "External identifier for the group"
        type: string
        source_column: "ExternalId"
        source_table: "mx_insurance_Group"

      # Attribute Columns
      - name: Group_Name
        description: "Name of the group"
        type: string
        source_column: "Name"
        source_table: "mx_insurance_Group"

      - name: Group_Frozen_Flag
        description: "Indicates if the group is frozen"
        type: boolean
        source_column: "Frozen"
        source_table: "mx_insurance_Group"

      - name: Group_Active
        description: "Indicates if the group is active"
        type: boolean
        source_column: "Active"
        source_table: "mx_insurance_Group"

      - name: Termination_Date
        description: "Date when the group was terminated"
        type: date
        source_column: "TerminationDate"
        source_table: "mx_insurance_Group"
        transformation_logic: "CASE WHEN TRIM(TerminationDate) = '' THEN NULL WHEN TerminationDate IS NOT NULL AND TRIM(TerminationDate) <> '' THEN CAST(TerminationDate AS DATE) ELSE NULL END"

      # Address Columns
      - name: Address_Line_1
        description: "First line of group address"
        type: string
        source_column: "AddressLine1"
        source_table: "mx_insurance_Group"

      - name: Address_Line_2
        description: "Second line of group address"
        type: string
        source_column: "AddressLine2"
        source_table: "mx_insurance_Group"

      - name: City_Name
        description: "City name from group address"
        type: string
        source_column: "City"
        source_table: "mx_insurance_Group"

      - name: State_Cd
        description: "State code from group address"
        type: string
        source_column: "State"
        source_table: "mx_insurance_Group"

      - name: Postal_Cd
        description: "Postal code from group address"
        type: string
        source_column: "PostalCode"
        source_table: "mx_insurance_Group"

      # Temporal Columns
      - name: Effective_From_Dt
        description: "Date when this version becomes effective"
        type: date
        source_column: "dbt_valid_from"
        source_table: "mx_insurance_Group"
        transformation_logic: "CAST(dbt_valid_from AS DATE)"

      - name: Effective_To_Dt
        description: "Date when this version expires"
        type: date
        source_column: "dbt_valid_to"
        source_table: "mx_insurance_Group"
        transformation_logic: "CASE WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY) ELSE DATE('9999-12-31') END"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when record was created"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Updated_dt_ts
        description: "Timestamp when record was last updated"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_insurance_Group"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Only active groups included (Active = 1)"
        - "Frozen groups excluded (Frozen = 0)"
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Valid_to dates adjusted to prevent temporal overlaps"
        - "Terminated groups filtered based on batch date"


