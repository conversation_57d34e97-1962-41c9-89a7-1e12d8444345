version: 2

models:
  - name: V_Base_Plan_DW
    description: |
      Purpose:
      This view provides a standardized base view of insurance plan data with temporal 
      validity handling. It extracts plan numbers from external IDs and maintains 
      proper versioning for downstream consumption.

      Data Grain:
      One record per plan ID per validity period.

      Direct Sources:
      - mx_insurance_Plan (snapshot table with plan data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes records valid on the batch control date
      - Extracts Plan Number from ExternalId using split function
      - Maintains SCD Type 2 temporal validity
      - Adjusts valid_to dates by subtracting one day to prevent overlaps
      - Selects latest record when multiple versions exist for same date

    config:
      materialized: view
      tags: []

    transformations:
      - name: batch_control_date
        description: |
          Retrieves the current processing date:
          - Gets batch control date for MxDataMartDailyBuild
          - Ensures proper date casting for comparisons
        sql: >
          SELECT 
              CAST(batch_control_dt AS DATE) AS batch_date
          FROM `medxoom-dev-fb5`.`Warehouse`.`T_Batch_Control`
          WHERE batch_id = 'MxDataMartDailyBuild'

      - name: plan_number_extraction
        description: |
          Extracts the plan number from external ID:
          - Takes first element when splitting ExternalId by '-'
          - Maintains original format without transformation
        sql: >
          SPLIT(ExternalId,'-')[0] AS Plan_Number

      - name: temporal_validity
        description: |
          Manages temporal validity of records:
          - Converts dbt_valid_from to valid_from date
          - Adjusts valid_to by subtracting one day when not 9999-12-31
          - Ensures proper date boundaries and prevents overlaps
        sql: >
          CASE 
              WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
              THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
              ELSE DATE('9999-12-31')
          END AS valid_to

    columns:
      # Key Columns
      - name: id
        description: "Primary identifier for the plan"
        type: string
        source_column: "id"
        source_table: "mx_insurance_Plan"

      - name: Plan_Number
        description: "Extracted plan number from ExternalId"
        type: string
        source_column: "ExternalId"
        source_table: "mx_insurance_Plan"
        transformation_logic: "SPLIT(ExternalId,'-')[0]"

      - name: ExternalId
        description: "External identifier for the plan"
        type: string
        source_column: "ExternalId"
        source_table: "mx_insurance_Plan"

      # Temporal Columns
      - name: valid_from
        description: "Date when this version becomes valid"
        type: date
        source_column: "dbt_valid_from"
        source_table: "mx_insurance_Plan"
        transformation_logic: "CAST(dbt_valid_from AS DATE)"

      - name: valid_to
        description: "Date when this version expires"
        type: date
        source_column: "dbt_valid_to"
        source_table: "mx_insurance_Plan"
        transformation_logic: >
          CASE 
              WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
              THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
              ELSE DATE('9999-12-31')
          END

      # Audit Columns
      - name: dbt_valid_from
        description: "Original dbt SCD Type 2 valid from timestamp"
        type: timestamp
        source_column: "dbt_valid_from"
        source_table: "mx_insurance_Plan"

      - name: dbt_valid_to
        description: "Original dbt SCD Type 2 valid to timestamp"
        type: timestamp
        source_column: "dbt_valid_to"
        source_table: "mx_insurance_Plan"

      - name: dbt_updated_at
        description: "Timestamp of last dbt update"
        type: timestamp
        source_column: "dbt_updated_at"
        source_table: "mx_insurance_Plan"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_insurance_Plan"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Valid_to dates adjusted by subtracting one day"
        - "Plan numbers extracted from ExternalId using split function"
        - "Maximum valid_to date is set to '9999-12-31'"






