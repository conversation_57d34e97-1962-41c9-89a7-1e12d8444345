version: 2

models:
  - name: V_Base_Group_DW
    description: |
      Purpose:
      This view provides a standardized base view of insurance group data with temporal 
      validity handling. It serves as a foundation for group-related analytics by maintaining 
      proper versioning and ensuring data currency based on batch control dates.

      Data Grain:
      One record per group ID per validity period.

      Direct Sources:
      - mx_insurance_Group (snapshot table with group data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes records valid on the batch control date
      - Maintains SCD Type 2 temporal validity
      - Adjusts valid_to dates by subtracting one day to prevent overlaps
      - Selects latest record when multiple versions exist for same date

    config:
      materialized: view
      tags: []

    transformations:
      - name: batch_date_control
        description: |
          Retrieves the current processing date:
          - Gets batch control date for MxDataMartDailyBuild
          - Ensures proper date casting for comparisons
        sql: >
          SELECT 
              CAST(batch_control_dt AS DATE) AS batch_date
          FROM `medxoom-dev-fb5`.`Warehouse`.`T_Batch_Control`
          WHERE batch_id = 'MxDataMartDailyBuild'

      - name: temporal_validity
        description: |
          Manages temporal validity of records:
          - Converts dbt_valid_from to valid_from date
          - Adjusts valid_to by subtracting one day when not 9999-12-31
          - Ensures proper date boundaries and prevents overlaps
        sql: >
          CASE 
              WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
              THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
              ELSE DATE('9999-12-31')
          END AS valid_to

      - name: latest_version_selection
        description: |
          Selects the latest version of each group:
          - Partitions by id
          - Orders by dbt_updated_at DESC
          - Takes the first record
        sql: >
          ROW_NUMBER() OVER (
              PARTITION BY id
              ORDER BY dbt_updated_at DESC
          ) = 1

    columns:
      # Key Columns
      - name: id
        description: "Primary identifier for the group"
        type: string
        source_column: "id"
        source_table: "mx_insurance_Group"

      # Temporal Columns
      - name: valid_from
        description: "Date when this version becomes valid"
        type: date
        source_column: "dbt_valid_from"
        source_table: "mx_insurance_Group"
        transformation_logic: "CAST(dbt_valid_from AS DATE)"

      - name: valid_to
        description: "Date when this version expires"
        type: date
        source_column: "dbt_valid_to"
        source_table: "mx_insurance_Group"
        transformation_logic: >
          CASE 
              WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
              THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
              ELSE DATE('9999-12-31')
          END

      # Audit Columns
      - name: dbt_valid_from
        description: "Original dbt SCD Type 2 valid from timestamp"
        type: timestamp
        source_column: "dbt_valid_from"
        source_table: "mx_insurance_Group"

      - name: dbt_valid_to
        description: "Original dbt SCD Type 2 valid to timestamp"
        type: timestamp
        source_column: "dbt_valid_to"
        source_table: "mx_insurance_Group"

      - name: dbt_updated_at
        description: "Timestamp of last dbt update"
        type: timestamp
        source_column: "dbt_updated_at"
        source_table: "mx_insurance_Group"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_insurance_Group"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Valid_to dates adjusted by subtracting one day"
        - "Maximum valid_to date is set to '9999-12-31'"




