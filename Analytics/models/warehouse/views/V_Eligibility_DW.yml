version: 2

models:
  - name: V_Eligibility_DW
    description: |
      Purpose:
      This view transforms eligibility data, providing a standardized view of member eligibility
      information with temporal validity handling and data quality controls.

      Data Grain:
      One record per unique combination of member number, group number, person number,
      insurance type, and plan ID.

      Direct Sources:
      - mx_fileprocessing_Eligibility (snapshot table with eligibility data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Excludes records with empty/null person numbers or member numbers
      - Filters out demo accounts (membernumber NOT LIKE '%demo%')
      - Only includes active eligibilities (active = 1)
      - Excludes terminated eligibilities (TerminationDate >= batch_date or null)
      - Requires valid effective dates (EffectiveDate <= batch_date)
      - Excludes invalid birth dates (DateOfBirth < CURRENT_DATE and != '0001-01-01')

    config:
      materialized: view
      tags: []

    transformations:
      - name: batch_date_processing
        description: |
          Extracts and processes batch control date:
          - Retrieves current batch date for MxDataMartDailyBuild
          - Uses batch date for filtering temporal validity
          - Applies batch date to eligibility effective and termination dates

      - name: temporal_validity
        description: |
          Processes temporal validity for eligibility records:
          - Sets valid_from based on dbt_valid_from
          - Adjusts valid_to by subtracting one day from dbt_valid_to
          - Handles default end date (9999-12-31)
          - Qualifies latest record using ROW_NUMBER

      - name: eligibility_filtering
        description: |
          Applies business rules and data quality filters:
          - Validates person and member numbers
          - Excludes demo accounts
          - Checks active status
          - Validates termination and effective dates
          - Ensures valid birth dates

      - name: record_deduplication
        description: |
          Deduplicates eligibility records:
          - Groups by member number, group number, person number, insurance type, plan ID
          - Orders by effective date (DESC) and creation date (DESC)
          - Selects most recent record per group

    columns:
      # Key Columns
      - name: Elig_Id
        description: "Primary identifier for the eligibility record"
        type: string
        source_column: "Id"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Member_Number
        description: "Unique identifier for the member"
        type: string
        source_column: "membernumber"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Elig_Group_Number
        description: "Group number associated with the eligibility"
        type: string
        source_column: "GroupNumber"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Person_Number
        description: "Unique identifier for the person within the eligibility"
        type: string
        source_column: "PersonNumber"
        source_table: "mx_fileprocessing_Eligibility"

      # Plan Information
      - name: Plan_External_Id
        description: "External identifier for the insurance plan"
        type: string
        source_column: "PlanId"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Insurance_Type
        description: "Type of insurance coverage"
        type: string
        source_column: "InsuranceType"
        source_table: "mx_fileprocessing_Eligibility"

      # Member Information
      - name: First_Name
        description: "Member's first name"
        type: string
        source_column: "FirstName"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Last_Name
        description: "Member's last name"
        type: string
        source_column: "LastName"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Email
        description: "Member's email address"
        type: string
        source_column: "Email"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Date_Of_Birth
        description: "Member's date of birth"
        type: date
        source_column: "DateOfBirth"
        source_table: "mx_fileprocessing_Eligibility"
        transformation_logic: "CAST(DateOfBirth as DATE)"

      # SSN Information
      - name: Member_SSN
        description: "Member's Social Security Number"
        type: string
        source_column: "SSN"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Subscriber_SSN
        description: "Subscriber's Social Security Number"
        type: string
        source_column: "SubscriberSSN"
        source_table: "mx_fileprocessing_Eligibility"
        transformation_logic: "COALESCE(SubscriberSSN , '999999999')"

      # Additional Attributes
      - name: Relationship_Cd
        description: "Code indicating relationship to subscriber"
        type: string
        source_column: "Relationship"
        source_table: "mx_fileprocessing_Eligibility"

      - name: TPA_Name
        description: "Third Party Administrator name"
        type: string
        source_column: "PayerName"
        source_table: "mx_fileprocessing_Eligibility"

      - name: State
        description: "State code for the member"
        type: string
        source_column: "State"
        source_table: "mx_fileprocessing_Eligibility"

      # Dates and Status
      - name: Elig_Termination_Date
        description: "Date when eligibility terminates"
        type: string
        source_column: "TerminationDate"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Elig_Effective_Date
        description: "Date when eligibility becomes effective"
        type: string
        source_column: "EffectiveDate"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Elig_Active
        description: "Flag indicating if eligibility is active"
        type: boolean
        source_column: "Active"
        source_table: "mx_fileprocessing_Eligibility"

      # Custom Attributes
      - name: LOB_Cd
        description: "Line of Business code"
        type: string
        source_column: "CustomAttributes"
        transformation_logic: "TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.DirectoryId'))"

      - name: Enr_Seq_Number
        description: "Enrollment sequence number"
        type: string
        source_column: "CustomAttributes"
        transformation_logic: "TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.EnrSeqNumber'))"

      - name: Mem_Dep_Code
        description: "Member dependency code"
        type: string
        source_column: "CustomAttributes"
        transformation_logic: "TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.MemDepCode'))"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_fileprocessing_Eligibility"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "current_state"
      refresh_frequency: "daily"
      business_rules:
        - "Excludes records with invalid person or member numbers"
        - "Filters out demo accounts"
        - "Only includes active eligibilities"
        - "Requires valid effective dates"
        - "Excludes future or invalid birth dates"
        - "Maintains latest record per member/group/person/plan combination"


