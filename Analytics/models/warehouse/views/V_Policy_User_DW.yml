version: 2

models:
  - name: V_Policy_User_DW
    description: |
      Purpose:
      This view aggregates policy-user relationships to provide registration metrics and flags
      at the eligibility-person-user level. It tracks active policy registrations and maintains
      temporal validity using SCD Type 2 handling.

      Data Grain:
      One record per unique combination of EligibilityId, PersonNumber, and UserId for active registrations.

      Direct Sources:
      - mx_insurance_Policy (snapshot table with policy data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes active policies (Active = 1)
      - Only includes records with non-null UserId
      - Takes latest record per EligibilityId-PersonId-UserId combination
      - Filters records based on batch control date validity

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: temporal_validity_processing
        description: |
          Processes temporal validity of policy records:
          - Retrieves batch control date for MxDataMartDailyBuild
          - Adjusts valid_to dates by subtracting one day when not 9999-12-31
          - Filters records valid on batch control date
          - Selects latest record when multiple versions exist
        sql: >
          Filters records based on dbt_valid_from <= batch_date 
          AND dbt_valid_to >= batch_date

      - name: latest_registration_selection
        description: |
          Identifies latest registration per combination:
          - Groups by EligibilityId, PersonId, and UserId
          - Orders by CreatedAt descending
          - Selects top record per group
        sql: >
          ROW_NUMBER() OVER (
            PARTITION BY EligibilityId, PersonId, UserId
            ORDER BY CreatedAt DESC
          )

      - name: registration_aggregation
        description: |
          Aggregates registration metrics:
          - Counts total registrations per combination
          - Sets registration flag to 1 for active records
          - Groups by eligibility, person, and user identifiers

    columns:
      # Key Columns
      - name: Elig_Id
        description: "Eligibility identifier"
        type: string
        source_column: "EligibilityId"
        source_table: "mx_insurance_Policy"

      - name: Person_Number
        description: "Person identifier number"
        type: string
        source_column: "PersonNumber"
        source_table: "mx_insurance_Policy"

      - name: UserId
        description: "User identifier"
        type: string
        source_column: "UserId"
        source_table: "mx_insurance_Policy"

      # Metric Columns
      - name: REGISTERED_CNT
        description: "Count of registrations for the eligibility-person-user combination"
        type: integer
        transformation_logic: "count(*)"

      - name: REGISTERED_ELIG
        description: "Flag indicating active registration (always 1 for included records)"
        type: integer
        transformation_logic: "1"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_insurance_Policy"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Records must be valid on batch control date"
        - "Only active policies are included"
        - "UserId must not be null"
        - "Latest registration per combination is selected"
        - "Registration count and flag maintained per combination"




