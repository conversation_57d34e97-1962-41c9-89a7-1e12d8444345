{{ config(
    materialized='view'
        ) 
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

SELECT 
    *,
    CAST(dbt_valid_from AS DATE) AS valid_from,
    CASE 
        WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
        THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
        ELSE DATE('9999-12-31')
    END AS valid_to
FROM {{ ref('mx_insurance_Policy') }}
WHERE 
    CAST(dbt_valid_from AS DATE) <= (SELECT batch_date.batch_date FROM batch_date)
    AND COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) >= (SELECT batch_date.batch_date FROM batch_date)
QUALIFY ROW_NUMBER() OVER (
    PARTITION BY id
    ORDER BY dbt_updated_at DESC
) = 1
