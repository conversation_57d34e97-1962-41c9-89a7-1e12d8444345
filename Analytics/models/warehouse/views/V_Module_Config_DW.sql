{{ config(
    materialized='view'
    ) 
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

,base AS (
    SELECT 
        *,
        CAST(dbt_valid_from AS DATE) AS valid_from,
        CASE 
            WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
            THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
            ELSE DATE('9999-12-31')
        END AS valid_to
    FROM {{ ref('mx_configuration_ModuleConfig') }}
    WHERE 
        CAST(dbt_valid_from AS DATE) <= (SELECT batch_date.batch_date FROM batch_date)
        AND COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) >= (SELECT batch_date.batch_date FROM batch_date)
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY id
        ORDER BY dbt_updated_at DESC
    ) = 1
)

SELECT 
    {{ dbt_utils.generate_surrogate_key(
        [
            'Id'
            ,'Type'
            ,'GroupId'
            ,'Settings'
            ,'ExternalId'
            ,'Active'
        ]
    ) }} AS Module_Config_SK,
    valid_from AS Effective_From_Dt,
    valid_to AS Effective_To_Dt,
    Id AS Module_Config_Id,
    Type AS Module_Id,
    GroupId AS Group_Id,
    CASE WHEN TRIM(Settings) = '' OR TRIM(Settings) = '{}' THEN NULL ELSE Settings END AS Module_Config_Settings,
    IFNULL(JSON_EXTRACT_SCALAR(Settings, '$.MenuName'),"Not Applicable") AS Module_Config_Menu_Name,
    IFNULL(ExternalId ,"Not Applicable") AS Module_Config_External_Id,
    Active AS Module_Config_Active,
    m.Module_Name,
    m.Category_Name,
    m.UI_Type,
    m.Vendor_Name,
    m.Module_Description,
    m.Module_Active,
    CURRENT_DATETIME() AS Created_dt_ts,
    CURRENT_DATETIME() AS Updated_dt_ts,
    CAST(NULL AS STRING) AS Created_By,
    CAST(NULL AS STRING) AS Updated_By
FROM 
    base b
JOIN {{ ref('V_Module_DW') }} m
ON b.Type = m.Module_Id
