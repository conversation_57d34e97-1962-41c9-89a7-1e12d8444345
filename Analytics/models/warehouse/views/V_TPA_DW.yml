version: 2

models:
  - name: V_TPA_DW
    description: |
      Purpose:
      This view provides a distinct list of Third Party Administrators (TPAs) with 
      generated surrogate keys, serving as a reference view for TPA dimensions and lookups.

      Data Grain:
      One record per unique TPA name.

      Direct Sources:
      - mx_fileprocessing_Eligibility (source table containing TPA information)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes records where dbt_valid_to is NULL (current records)
      - Excludes NULL TPA names
      - Excludes blank TPA names
      - Trims whitespace from TPA names

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: base_tpa_extraction
        description: |
          Extracts and transforms TPA data:
          - Selects distinct TPA names from eligibility data
          - Trims whitespace from PayerName field
          - Filters for current records and non-null values
          - Generates surrogate keys for each unique TPA
        sql: >
          SELECT DISTINCT(trim(payername)) as Tpa_Name
          FROM mx_fileprocessing_Eligibility
          WHERE dbt_valid_to IS NULL 
            AND PayerName IS NOT NULL
            AND PayerName <> ' '

    columns:
      # Key Columns
      - name: TPA_SK
        description: "Surrogate key for the TPA"
        type: string
        source_column: "Tpa_Name"
        source_table: "mx_fileprocessing_Eligibility"
        transformation_logic: "GENERATE_UUID()"

      # Attribute Columns
      - name: TPA_Name
        description: "Name of the Third Party Administrator"
        type: string
        source_column: "PayerName"
        source_table: "mx_fileprocessing_Eligibility"
        transformation_logic: "trim(PayerName)"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_fileprocessing_Eligibility"
      table_type: "view"
      temporal_type: "current_state"
      refresh_frequency: "daily"
      business_rules:
        - "Only current records included (dbt_valid_to IS NULL)"
        - "TPA names must be non-null and non-blank"
        - "TPA names are trimmed of whitespace"
        - "Each TPA name generates a unique surrogate key"
        - "Audit columns track creation and update timestamps"




