{{ config(
    materialized = 'view',
    tags = ["MxDataMartDailyBuild"]
    )
}}

SELECT 
    {{ dbt_utils.generate_surrogate_key([
        'Age'
    ])}} as Age_SK
    ,CAST(Age as INT64) as Age
    ,Age_Group_1
    ,Age_Group_2
    ,current_datetime() as Created_dt_ts
    ,current_datetime() as Updated_dt_ts
    ,CAST(NULL AS STRING) as Created_By
    ,CAST(NULL AS STRING) as Updated_By
FROM
    {{ ref('stg_Age') }}    
