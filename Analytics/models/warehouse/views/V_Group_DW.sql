{{ config(
    materialized='view'
    ) 
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
),

base AS (
    SELECT 
        *,
        CAST(dbt_valid_from AS DATE) AS valid_from,
        CASE 
            WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
            THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
            ELSE DATE('9999-12-31')
        END AS valid_to
    FROM {{ ref('mx_insurance_Group') }}
    WHERE 
        CAST(dbt_valid_from AS DATE) <= (SELECT batch_date.batch_date FROM batch_date)
        AND COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) >= (SELECT batch_date.batch_date FROM batch_date)
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY id
        ORDER BY dbt_updated_at DESC
    ) = 1
)

SELECT 
    {{ dbt_utils.generate_surrogate_key(
        [
            'Id',
            'ExternalId',
            'Name',
            'Active',
            'Frozen',
            'AddressLine1',
            'AddressLine2',
            'City',
            'State',
            'PostalCode',
            'TerminationDate'
        ]
    ) }} AS Group_SK,
    valid_from AS Effective_From_Dt,
    valid_to AS Effective_To_Dt,
    Id AS Group_Id,
    ExternalId AS Group_External_Id,
    Name AS Group_Name,
    Frozen AS Group_Frozen_Flag,
    Active AS Group_Active,
    CASE 
        WHEN TRIM(TerminationDate) = '' THEN NULL
        WHEN TerminationDate IS NOT NULL AND TRIM(TerminationDate) <> '' THEN CAST(TerminationDate AS DATE)
        ELSE NULL
    END AS Termination_Date,
    AddressLine1 AS Address_Line_1,
    AddressLine2 AS Address_Line_2,
    City AS City_Name,
    State AS State_Cd,
    PostalCode AS Postal_Cd,
    CURRENT_DATETIME() AS Created_dt_ts,
    CURRENT_DATETIME() AS Updated_dt_ts,
    CAST(NULL AS STRING) AS Created_By,
    CAST(NULL AS STRING) AS Updated_By
FROM 
    base

WHERE Active = 1
AND Frozen = 0
AND (TerminationDate = '' OR TerminationDate IS NULL OR CAST(TerminationDate AS DATE) >= (SELECT batch_date.batch_date FROM batch_date))
