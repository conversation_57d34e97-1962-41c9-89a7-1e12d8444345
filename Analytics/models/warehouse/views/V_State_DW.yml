version: 2

models:
  - name: V_State_DW
    description: |
      Purpose:
      This view serves as a reference dimension for US states and regions, providing 
      standardized state codes, names, and regional classifications with surrogate keys.

      Data Grain:
      One record per unique combination of state code, state name, and region type.

      Direct Sources:
      - stg_State (staging table containing state reference data)

      Indirect Sources:
      None

      Key Business Rules:
      - Trims whitespace from state codes, names, and region types
      - Generates surrogate keys for unique state combinations
      - Maintains audit timestamps for all records

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: state_reference_processing
        description: |
          Processes state reference data:
          - Generates surrogate keys based on state attributes
          - Standardizes state codes and names through trimming
          - Adds audit columns for tracking
        business_logic_steps:
          - "Generate surrogate key from State_Cd, State_Name, and Region_Type"
          - "Trim whitespace from all string columns"
          - "Add creation and update timestamps"
          - "Set Created_By and Updated_By as null"

    columns:
      # Key Columns
      - name: State_SK
        description: "Surrogate key for the state record"
        type: string
        source_column: ["State_Cd", "State_Name", "Region_Type"]
        source_table: "stg_State"
        transformation_logic: "GENERATE_UUID()"

      # Attribute Columns
      - name: State_Cd
        description: "State code (e.g., NY, CA)"
        type: string
        source_column: "State_Cd"
        source_table: "stg_State"
        transformation_logic: "trim(State_Cd)"

      - name: State_Name
        description: "Full state name"
        type: string
        source_column: "State_Name"
        source_table: "stg_State"
        transformation_logic: "trim(State_Name)"

      - name: Region_Type
        description: "Regional classification of the state"
        type: string
        source_column: "Region_Type"
        source_table: "stg_State"
        transformation_logic: "trim(Region_Type)"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "null"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "null"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "stg_State"
      table_type: "view"
      temporal_type: "current_state"
      refresh_frequency: "daily"
      business_rules:
        - "Surrogate keys generated from combination of state attributes"
        - "All string values are trimmed of whitespace"
        - "Audit timestamps reflect processing time"
        - "Created_By and Updated_By maintained as null"



