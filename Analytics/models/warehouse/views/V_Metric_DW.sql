{{ config(
    materialized='view'
    ) 
}}


SELECT 
    {{ dbt_utils.generate_surrogate_key(
        [
                "Metric_Id"
                ,"Subject_Area"
                ,"Metric_Type"
                ,"Metric_Sub_Type"
                ,"Metric_Name"
                ,"Metric_Business_Rules"
                ,"DBT_Model_Name"
        ]
    ) }} AS Metric_SK
    ,Metric_Id
    ,Subject_Area
    ,Metric_Type
    ,Metric_Sub_Type
    ,Metric_Name
    ,CAST(Metric_Business_Rules AS STRING) AS Metric_Business_Rules
    ,CAST(DBT_Model_Name AS STRING) AS DBT_Model_Name
    ,CURRENT_DATETIME() AS Created_dt_ts
    ,CURRENT_DATETIME() AS Updated_dt_ts
    ,CAST(NULL AS STRING) AS Created_By
    ,CAST(NULL AS STRING) AS Updated_By
FROM 
    {{ ref('stg_Metric') }}
