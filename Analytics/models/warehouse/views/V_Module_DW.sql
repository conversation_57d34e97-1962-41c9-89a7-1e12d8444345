{{ config(
    materialized='view'
    ) 
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

,base AS (
    SELECT 
        *,
        CAST(dbt_valid_from AS DATE) AS valid_from,
        CASE 
            WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
            THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
            ELSE DATE('9999-12-31')
        END AS valid_to
    FROM {{ ref('mx_configuration_Module') }}
    WHERE 
        CAST(dbt_valid_from AS DATE) <= (SELECT batch_date.batch_date FROM batch_date)
        AND COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) >= (SELECT batch_date.batch_date FROM batch_date)
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY id
        ORDER BY dbt_updated_at DESC
    ) = 1
)

SELECT 
    {{ dbt_utils.generate_surrogate_key(
        [
            'Id'
        ]
    ) }} AS Module_SK,
    valid_from AS Effective_From_Dt,
    valid_to AS Effective_To_Dt,
    Id AS Module_Id,
    Name AS Module_Name,
    CASE WHEN Category = '?' OR Category ='NA' OR LOWER(Category) = 'none' THEN "Not Available"
    ELSE Category
    END AS Category_Name,
    CASE WHEN UIType = '?' OR UIType ='NA' OR LOWER(UIType) = 'none' THEN "Not Available"
    ELSE UIType
    END AS UI_Type,
    CASE WHEN Vendor = '?' OR Vendor ='NA' OR LOWER(Vendor) = 'none' THEN "Not Available"
    ELSE Vendor
    END AS Vendor_Name,
    CASE WHEN Description = '?' OR Description ='NA' OR LOWER(Description) = 'none' THEN "Not Available"
    ELSE Description
    END AS Module_Description,
    Active AS Module_Active,
    CURRENT_DATETIME() AS Created_dt_ts,
    CURRENT_DATETIME() AS Updated_dt_ts,
    CAST(NULL AS STRING) AS Created_By,
    CAST(NULL AS STRING) AS Updated_By
FROM 
    base
