version: 2

models:
  - name: V_LOB_DW
    description: |
      Purpose:
      This view transforms Line of Business (LOB) data from eligibility records, extracting LOB codes
      from JSON attributes and providing standardized LOB names through mapping lookups.

      Data Grain:
      One record per unique LOB code.

      Direct Sources:
      - mx_fileprocessing_Eligibility (source for LOB codes in CustomAttributes)
      - lob_cd_mappings (reference table for LOB name standardization)

      Indirect Sources:
      None

      Key Business Rules:
      - Extracts DirectoryId from CustomAttributes JSON as LOB_Cd
      - Filters out null and blank LOB codes
      - Only includes current records (dbt_valid_to IS NULL)
      - Maps to standardized LOB names, defaulting to "Unknown" if no mapping exists

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: base_extraction
        description: |
          Extracts and prepares base LOB data:
          - Extracts DirectoryId from CustomAttributes JSON
          - Trims whitespace from LOB codes
          - Filters for distinct, non-null, non-blank values
          - Only includes current records
        
      - name: lob_mapping
        description: |
          Maps LOB codes to standardized names:
          - Left joins to lob_cd_mappings table
          - Provides default "Unknown" for unmapped codes
        joins:
          - join: lob_cd_mappings
            type: left
            relationship: one_to_one
            sql: "base.Lob_Cd = CAST(lob.Lob_Cd AS STRING)"

    columns:
      # Key Columns
      - name: LOB_SK
        description: "Surrogate key for the LOB"
        type: string
        transformation_logic: "GENERATE_UUID()"

      - name: LOB_Cd
        description: "Line of Business code extracted from CustomAttributes"
        type: string
        source_column: "CustomAttributes"
        source_table: "mx_fileprocessing_Eligibility"
        transformation_logic: "TRIM(JSON_EXTRACT_SCALAR(CustomAttributes, '$.DirectoryId'))"

      # Attribute Columns
      - name: LOB_Name
        description: "Standardized Line of Business name"
        type: string
        source_column: "LOB_Name"
        source_table: "lob_cd_mappings"
        transformation_logic: "IFNULL(lob.LOB_Name, 'Unknown')"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when record was created"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Updated_dt_ts
        description: "Timestamp when record was last updated"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_fileprocessing_Eligibility"
        - "lob_cd_mappings"
      table_type: "view"
      temporal_type: "current_state"
      refresh_frequency: "daily"
      business_rules:
        - "Only current records included (dbt_valid_to IS NULL)"
        - "LOB codes must be non-null and non-blank"
        - "LOB codes are trimmed of whitespace"
        - "Each LOB code generates a unique surrogate key"
        - "Unmapped LOB codes default to 'Unknown' name"
        - "Audit columns track creation and update timestamps"



