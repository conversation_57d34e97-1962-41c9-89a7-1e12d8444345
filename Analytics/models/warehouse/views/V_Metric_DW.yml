version: 2

models:
  - name: V_Metric_DW
    description: |
      Purpose:
      This view transforms metric metadata from staging, providing a standardized view of metric
      definitions with surrogate keys and audit information. It serves as a central reference
      for all metrics used in the data warehouse.

      Data Grain:
      One record per unique combination of metric attributes (ID, subject area, type, name, rules).

      Direct Sources:
      - stg_Metric (staging table containing metric definitions)

      Indirect Sources:
      None

      Key Business Rules:
      - Generates surrogate keys based on metric attributes
      - Maintains audit columns for tracking changes
      - Preserves business rules as string type
      - Handles NULL values for audit user columns

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: surrogate_key_generation
        description: |
          Generates unique surrogate keys for metrics:
          - Combines multiple attributes to ensure uniqueness
          - Uses dbt_utils.generate_surrogate_key function
          - Includes all identifying attributes in key generation
        attributes:
          - Metric_Id
          - Subject_Area
          - Metric_Type
          - Metric_Sub_Type
          - Metric_Name
          - Metric_Business_Rules
          - DBT_Model_Name

    columns:
      # Key Columns
      - name: Metric_SK
        description: "Surrogate key for the metric"
        type: string
        transformation_logic: "GENERATE_UUID()"

      - name: Metric_Id
        description: "Natural key identifier for the metric"
        type: string
        source_column: "Metric_Id"
        source_table: "stg_Metric"

      # Attribute Columns
      - name: Subject_Area
        description: "Business domain or subject area for the metric"
        type: string
        source_column: "Subject_Area"
        source_table: "stg_Metric"

      - name: Metric_Type
        description: "Primary classification of the metric"
        type: string
        source_column: "Metric_Type"
        source_table: "stg_Metric"

      - name: Metric_Sub_Type
        description: "Secondary classification of the metric"
        type: string
        source_column: "Metric_Sub_Type"
        source_table: "stg_Metric"

      - name: Metric_Name
        description: "Business name of the metric"
        type: string
        source_column: "Metric_Name"
        source_table: "stg_Metric"

      - name: Metric_Business_Rules
        description: "Business rules and logic defining the metric"
        type: string
        source_column: "Metric_Business_Rules"
        source_table: "stg_Metric"
        transformation_logic: "CAST(Metric_Business_Rules AS STRING)"

      - name: DBT_Model_Name
        description: "Name of the DBT model where metric is calculated"
        type: string
        source_column: "DBT_Model_Name"
        source_table: "stg_Metric"
        transformation_logic: "CAST(DBT_Model_Name AS STRING)"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when record was created"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Updated_dt_ts
        description: "Timestamp when record was last updated"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "stg_Metric"
      table_type: "view"
      temporal_type: "current_state"
      refresh_frequency: "daily"
      business_rules:
        - "Surrogate keys generated from combination of metric attributes"
        - "Business rules and model names preserved as strings"
        - "Audit timestamps set to current datetime"
        - "Created_By and Updated_By defaulted to NULL"



