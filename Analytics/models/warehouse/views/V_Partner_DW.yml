version: 2

models:
  - name: V_Partner_DW
    description: |
      Purpose:
      This view transforms partner organization data, providing a standardized view of partner
      information with temporal validity handling and partner type classification.

      Data Grain:
      One record per partner ID per validity period.

      Direct Sources:
      - mx_partnerorganization_Partner (snapshot table with partner data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes active partners (Active = 1)
      - Extracts partner type from name prefix ([Billing], [Engage])
      - Maintains SCD Type 2 temporal validity
      - Removes partner type prefix from display name
      - Filters based on batch control date validity

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: batch_date_filtering
        description: |
          Establishes temporal context using batch control date:
          - Retrieves current batch date from T_Batch_Control
          - Filters records valid on batch date
          - Ensures consistent temporal snapshot

      - name: partner_type_classification
        description: |
          Determines partner type based on name prefix:
          - Extracts type from [Billing] or [Engage] prefix
          - Uses custom macro get_partner_type
          - Removes prefix from display name
          - Trims whitespace from final name

      - name: temporal_validity_handling
        description: |
          Processes temporal validity of partner records:
          - Converts dbt_valid_from/to to DATE type
          - Adjusts valid_to by subtracting one day when not 9999-12-31
          - Selects latest record per ID based on dbt_updated_at

    columns:
      # Key Columns
      - name: Partner_SK
        description: "Surrogate key generated from ID, Name, and Active status"
        type: string
        transformation_logic: "GENERATE_UUID()"

      - name: Partner_Id
        description: "Natural key from source system"
        type: string
        source_column: "Id"
        source_table: "mx_partnerorganization_Partner"

      # Attribute Columns
      - name: Partner_Name
        description: "Cleaned partner name with type prefix removed"
        type: string
        source_column: "Name"
        source_table: "mx_partnerorganization_Partner"
        transformation_logic: "TRIM(REGEXP_REPLACE(Name, r'\\[Billing\\]|\\[Engage\\]', ''))"

      - name: Partner_Type
        description: "Classification of partner (Billing, Engage)"
        type: string
        source_column: "Name"
        source_table: "mx_partnerorganization_Partner"
        transformation_logic: "CASE WHEN Name LIKE '[Billing]%' THEN 'Billing' WHEN Name LIKE '[Engage]%' THEN 'Engage' ELSE 'Other' END"

      - name: Partner_Active
        description: "Flag indicating if partner is active"
        type: boolean
        source_column: "Active"
        source_table: "mx_partnerorganization_Partner"

      # Temporal Columns
      - name: Effective_From_Dt
        description: "Date when this version became active"
        type: date
        source_column: "dbt_valid_from"
        source_table: "mx_partnerorganization_Partner"

      - name: Effective_To_Dt
        description: "Date when this version was superseded"
        type: date
        source_column: "dbt_valid_to"
        source_table: "mx_partnerorganization_Partner"
        transformation_logic: "DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when record was created"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Updated_dt_ts
        description: "Timestamp when record was last updated"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_partnerorganization_Partner"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Only active partners included (Active = 1)"
        - "Partner type determined by name prefix"
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Valid_to dates adjusted by subtracting one day"
        - "Partner names cleaned of type prefixes"



