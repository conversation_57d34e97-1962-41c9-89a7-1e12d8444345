version: 2

models:
  - name: V_Age_DW
    description: |
      Purpose:
      This view transforms age data from staging, providing a standardized view of age
      attributes with surrogate keys and age groupings. It serves as a reference for
      age-based analytics and reporting.

      Data Grain:
      One record per unique age value.

      Direct Sources:
      - stg_Age (staging table containing age values and groupings)

      Indirect Sources:
      None

      Key Business Rules:
      - Generates surrogate keys based on age value
      - Maintains age groupings for different analysis levels
      - Includes audit columns for tracking changes
      - Ages are cast to integer type for consistency

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: surrogate_key_generation
        description: |
          Generates surrogate key for age dimension:
          - Uses age value as the basis for key generation
          - Implements dbt_utils hash function for consistency
        sql: >
          GENERATE_UUID() as Age_SK

      - name: age_type_conversion
        description: |
          Ensures consistent age data type:
          - Casts age values to INTEGER type
          - Validates numeric age values
        sql: >
          CAST(Age as INT64) as Age

    columns:
      # Key Columns
      - name: Age_SK
        description: "Surrogate key for age dimension"
        type: string
        transformation_logic: "GENERATE_UUID()"

      # Attribute Columns
      - name: Age
        description: "Integer value representing age"
        type: integer
        source_column: "Age"
        source_table: "stg_Age"
        transformation_logic: "CAST(Age as INT64)"

      - name: Age_Group_1
        description: "Primary age grouping category"
        type: string
        source_column: "Age_Group_1"
        source_table: "stg_Age"

      - name: Age_Group_2
        description: "Secondary age grouping category"
        type: string
        source_column: "Age_Group_2"
        source_table: "stg_Age"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        transformation_logic: "current_datetime()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "stg_Age"
      table_type: "view"
      temporal_type: "current_state"
      refresh_frequency: "daily"
      business_rules:
        - "Age values must be valid integers"
        - "Surrogate keys are generated using age value"
        - "Age groupings maintained for reporting flexibility"
        - "Audit columns track creation and updates"
        - "Null values for Created_By and Updated_By default to system values"


