version: 2

models:
  - name: V_Eligibility_Current_DW
    description: |
      Purpose:
      This view provides the current snapshot of eligibility records, maintaining temporal
      validity and versioning for member eligibility data. It serves as a foundational
      view for downstream eligibility-based analytics.

      Data Grain:
      One record per eligibility ID for the current valid time period.

      Direct Sources:
      - mx_fileprocessing_Eligibility (snapshot table with SCD Type 2 versioning)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes records valid as of current date
      - Maintains latest version when multiple records exist
      - Preserves SCD Type 2 temporal validity
      - Requires valid dates for effective date and birth date
      - Maintains audit trail through dbt versioning columns

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: temporal_validity
        description: |
          Filters records based on temporal validity:
          - Ensures records are valid as of current date
          - Handles SCD Type 2 versioning
          - Maintains proper date boundaries
        filters:
          - "CAST(dbt_valid_from AS DATE) <= CURRENT_DATE()"
          - "COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) >= CURRENT_DATE()"

      - name: version_selection
        description: |
          Selects the most recent valid version:
          - Partitions data by eligibility ID
          - Orders by update timestamp descending
          - Selects top record per partition
        sql: >
          QUALIFY ROW_NUMBER() OVER (
              PARTITION BY id
              ORDER BY dbt_updated_at DESC
          ) = 1

    columns:
      # Key Columns
      - name: Id
        description: "Primary identifier for the eligibility record"
        type: string
        source_column: "Id"
        source_table: "mx_fileprocessing_Eligibility"

      - name: MemberNumber
        description: "Unique identifier for the member"
        type: string
        source_column: "MemberNumber"
        source_table: "mx_fileprocessing_Eligibility"

      - name: GroupNumber
        description: "Group number associated with the eligibility"
        type: string
        source_column: "GroupNumber"
        source_table: "mx_fileprocessing_Eligibility"

      - name: PersonNumber
        description: "Unique identifier for the person"
        type: string
        source_column: "PersonNumber"
        source_table: "mx_fileprocessing_Eligibility"

      - name: PlanId
        description: "Identifier for the insurance plan"
        type: string
        source_column: "PlanId"
        source_table: "mx_fileprocessing_Eligibility"

      # Member Information
      - name: DateOfBirth
        description: "Member's date of birth"
        type: date
        source_column: "DateOfBirth"
        source_table: "mx_fileprocessing_Eligibility"
        transformation_logic: "CAST(DateOfBirth AS DATE)"

      - name: FirstName
        description: "Member's first name"
        type: string
        source_column: "FirstName"
        source_table: "mx_fileprocessing_Eligibility"

      - name: LastName
        description: "Member's last name"
        type: string
        source_column: "LastName"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Email
        description: "Member's email address"
        type: string
        source_column: "Email"
        source_table: "mx_fileprocessing_Eligibility"

      # Eligibility Details
      - name: EffectiveDate
        description: "Date when eligibility becomes effective"
        type: date
        source_column: "EffectiveDate"
        source_table: "mx_fileprocessing_Eligibility"
        transformation_logic: "CAST(EffectiveDate AS DATE)"

      - name: TerminationDate
        description: "Date when eligibility terminates"
        type: date
        source_column: "TerminationDate"
        source_table: "mx_fileprocessing_Eligibility"
        transformation_logic: "CAST(TerminationDate AS DATE)"

      - name: Relationship
        description: "Relationship to primary subscriber"
        type: string
        source_column: "Relationship"
        source_table: "mx_fileprocessing_Eligibility"

      - name: Active
        description: "Flag indicating if eligibility is active"
        type: integer
        source_column: "Active"
        source_table: "mx_fileprocessing_Eligibility"

      # Identification Information
      - name: SSN
        description: "Member's Social Security Number"
        type: string
        source_column: "SSN"
        source_table: "mx_fileprocessing_Eligibility"

      - name: SubscriberSSN
        description: "Subscriber's Social Security Number"
        type: string
        source_column: "SubscriberSSN"
        source_table: "mx_fileprocessing_Eligibility"

      # Additional Attributes
      - name: PayerName
        description: "Name of the insurance payer"
        type: string
        source_column: "PayerName"
        source_table: "mx_fileprocessing_Eligibility"

      - name: State
        description: "State code"
        type: string
        source_column: "State"
        source_table: "mx_fileprocessing_Eligibility"

      - name: InsuranceType
        description: "Type of insurance coverage"
        type: string
        source_column: "InsuranceType"
        source_table: "mx_fileprocessing_Eligibility"

      - name: CustomAttributes
        description: "JSON field containing additional attributes"
        type: string
        source_column: "CustomAttributes"
        source_table: "mx_fileprocessing_Eligibility"

      # Audit Columns
      - name: dbt_valid_from
        description: "Timestamp when this version of the record became valid"
        type: timestamp
        source_column: "dbt_valid_from"
        source_table: "mx_fileprocessing_Eligibility"

      - name: dbt_valid_to
        description: "Timestamp when this version of the record expires"
        type: timestamp
        source_column: "dbt_valid_to"
        source_table: "mx_fileprocessing_Eligibility"

      - name: dbt_updated_at
        description: "Timestamp when this version was last updated"
        type: timestamp
        source_column: "dbt_updated_at"
        source_table: "mx_fileprocessing_Eligibility"

      - name: dbt_scd_id
        description: "Unique identifier for the snapshot version"
        type: string
        source_column: "dbt_scd_id"
        source_table: "mx_fileprocessing_Eligibility"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_fileprocessing_Eligibility"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "One current record per eligibility ID"
        - "Records must be valid on current date"
        - "Latest record selected when multiple versions exist"
        - "Valid dates required (birth, effective)"
        - "SSN and other sensitive data preserved"



