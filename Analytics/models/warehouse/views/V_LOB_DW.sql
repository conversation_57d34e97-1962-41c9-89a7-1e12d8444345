{{ config(
    materialized = 'view',
    tags = ["MxDataMartDailyBuild"]
    )
}}
WITH base AS (
    SELECT 
        DISTINCT(TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.DirectoryId'))) AS LOB_Cd
    FROM {{ ref('mx_fileprocessing_Eligibility')}}


    WHERE 
        dbt_valid_to IS NULL 
        AND TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.DirectoryId')) IS NOT NULL
        AND TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.DirectoryId')) <> ' '
)
SELECT 
    {{ dbt_utils.generate_surrogate_key(
        ['base.LOB_Cd']
    )}} AS LOB_SK
    ,base.LOB_Cd
    ,IFNULL(lob.LOB_Name , "Unknown") AS LOB_Name
    ,current_datetime() AS Created_dt_ts
    ,current_datetime() AS Updated_dt_ts
    ,CAST(NULL AS STRING) AS Created_By
    ,CAST(NULL AS STRING) AS Updated_By
FROM
    base

LEFT JOIN {{ ref('lob_cd_mappings') }} lob
ON base.Lob_Cd = CAST(lob.Lob_Cd AS STRING)
