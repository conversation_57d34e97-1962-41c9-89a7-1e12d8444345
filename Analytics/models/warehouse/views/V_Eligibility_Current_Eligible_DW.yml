version: 2

models:
  - name: V_Eligibility_Current_Eligible_DW
    description: |
      Purpose:
      This view provides the current snapshot of active and eligible members, filtering out
      terminated, demo, and invalid eligibility records. It serves as a clean, validated
      source of current eligibility information for downstream consumption.

      Data Grain:
      One record per unique combination of member number, group number, person number,
      insurance type, and plan ID for currently eligible members.

      Direct Sources:
      - V_Eligibility_Current_DW (base eligibility view)

      Indirect Sources:
      - mx_fileprocessing_Eligibility (underlying eligibility data)

      Key Business Rules:
      - Excludes records with empty/null person numbers
      - Excludes records with empty/null member numbers
      - Filters out demo accounts (membernumber NOT LIKE '%demo%')
      - Only includes active eligibilities (active = 1)
      - Excludes terminated eligibilities (TerminationDate >= CURRENT_DATE or null)
      - Requires valid effective dates (EffectiveDate <= CURRENT_DATE)
      - Excludes invalid birth dates (DateOfBirth < CURRENT_DATE and != '0001-01-01')

    config:
      materialized: view
      tags: []

    transformations:
      - name: eligibility_filtering
        description: |
          Filters eligibility records based on business rules:
          - Validates person and member numbers are non-empty
          - Excludes demo accounts from processing
          - Ensures active status is true
          - Validates termination and effective dates against current date
          - Confirms birth date validity
        sql: >
          WHERE (TRIM(personnumber) <> '' AND personnumber IS NOT NULL)
            AND (membernumber IS NOT NULL AND TRIM(membernumber) <> '')
            AND LOWER(membernumber) NOT LIKE '%demo%'
            AND active = 1
            AND (TerminationDate = '' OR TerminationDate IS NULL 
                 OR CAST(TerminationDate AS DATE) >= CURRENT_DATE())
            AND CAST(EffectiveDate AS DATE) <= CURRENT_DATE()

      - name: record_deduplication
        description: |
          Deduplicates eligibility records to ensure one current record per member:
          - Groups by member number, group number, person number, insurance type, plan ID
          - Orders by effective date (DESC) and creation timestamp (DESC)
          - Selects most recent record per group
        sql: >
          ROW_NUMBER() OVER (
            PARTITION BY membernumber, groupnumber, personnumber, InsuranceType, PlanId
            ORDER BY CAST(EffectiveDate AS DATE) DESC, CreatedAt DESC
          )

    columns:
      # Key Columns
      - name: Elig_Id
        description: "Primary identifier for the eligibility record"
        type: string
        source_column: "Id"
        source_table: "V_Eligibility_Current_DW"

      - name: Member_Number
        description: "Unique identifier for the member"
        type: string
        source_column: "membernumber"
        source_table: "V_Eligibility_Current_DW"

      - name: Elig_Group_Number
        description: "Group number associated with the eligibility"
        type: string
        source_column: "GroupNumber"
        source_table: "V_Eligibility_Current_DW"

      - name: Person_Number
        description: "Unique identifier for the person within the eligibility"
        type: string
        source_column: "PersonNumber"
        source_table: "V_Eligibility_Current_DW"

      # Plan Information
      - name: Plan_External_Id
        description: "External identifier for the insurance plan"
        type: string
        source_column: "PlanId"
        source_table: "V_Eligibility_Current_DW"

      - name: Insurance_Type
        description: "Type of insurance coverage"
        type: string
        source_column: "InsuranceType"
        source_table: "V_Eligibility_Current_DW"

      # Member Information
      - name: First_Name
        description: "Member's first name"
        type: string
        source_column: "FirstName"
        source_table: "V_Eligibility_Current_DW"

      - name: Last_Name
        description: "Member's last name"
        type: string
        source_column: "LastName"
        source_table: "V_Eligibility_Current_DW"

      - name: Email
        description: "Member's email address"
        type: string
        source_column: "Email"
        source_table: "V_Eligibility_Current_DW"

      - name: Date_Of_Birth
        description: "Member's date of birth"
        type: date
        source_column: "DateOfBirth"
        source_table: "V_Eligibility_Current_DW"
        transformation_logic: "CAST(DateOfBirth as DATE)"

      # SSN Information
      - name: Member_SSN
        description: "Member's Social Security Number"
        type: string
        source_column: "SSN"
        source_table: "V_Eligibility_Current_DW"

      - name: Subscriber_SSN
        description: "Subscriber's Social Security Number, defaults to '*********' if null"
        type: string
        source_column: "SubscriberSSN"
        source_table: "V_Eligibility_Current_DW"
        transformation_logic: "COALESCE(SubscriberSSN , '*********')"

      # Additional Attributes
      - name: Relationship_Cd
        description: "Code indicating relationship to subscriber"
        type: string
        source_column: "Relationship"
        source_table: "V_Eligibility_Current_DW"

      - name: TPA_Name
        description: "Third Party Administrator name"
        type: string
        source_column: "PayerName"
        source_table: "V_Eligibility_Current_DW"

      - name: State
        description: "State code for the member"
        type: string
        source_column: "State"
        source_table: "V_Eligibility_Current_DW"

      # Dates and Status
      - name: Elig_Termination_Date
        description: "Date when eligibility terminates"
        type: string
        source_column: "TerminationDate"
        source_table: "V_Eligibility_Current_DW"

      - name: Elig_Effective_Date
        description: "Date when eligibility becomes effective"
        type: string
        source_column: "EffectiveDate"
        source_table: "V_Eligibility_Current_DW"

      - name: Elig_Active
        description: "Flag indicating if eligibility is active"
        type: boolean
        source_column: "Active"
        source_table: "V_Eligibility_Current_DW"

      # Custom Attributes
      - name: LOB_Cd
        description: "Line of Business code"
        type: string
        source_column: "CustomAttributes"
        source_table: "V_Eligibility_Current_DW"
        transformation_logic: "TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.DirectoryId'))"

      - name: Enr_Seq_Number
        description: "Enrollment sequence number"
        type: string
        source_column: "CustomAttributes"
        source_table: "V_Eligibility_Current_DW"
        transformation_logic: "TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.EnrSeqNumber'))"

      - name: Mem_Dep_Code
        description: "Member dependency code"
        type: string
        source_column: "CustomAttributes"
        source_table: "V_Eligibility_Current_DW"
        transformation_logic: "TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.MemDepCode'))"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Eligibility_Current_DW"
      table_type: "view"
      temporal_type: "current_state"
      refresh_frequency: "daily"
      business_rules:
        - "Excludes records with invalid person or member numbers"
        - "Filters out demo accounts"
        - "Only includes active eligibilities"
        - "Requires valid effective dates"
        - "Excludes future or invalid birth dates"
        - "Maintains latest record per member/group/person/plan combination"
        - "Default SSN value of '*********' for null subscriber SSNs"


