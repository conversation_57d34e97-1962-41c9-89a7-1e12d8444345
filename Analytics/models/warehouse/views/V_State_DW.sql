{{ config(
    materialized = 'view',
    tags = ["MxDataMartDailyBuild"]
    )
}}

SELECT 
    {{ dbt_utils.generate_surrogate_key([
        'State_Cd',
        'State_Name',
        'Region_Type'
    ])}} as State_SK,
    trim(State_Cd) State_Cd,
    trim(State_Name) State_Name,
    trim(Region_Type) Region_Type,
    current_datetime() as Created_dt_ts,
    current_datetime() as Updated_dt_ts,
    null as Created_By,
    null as Updated_By
FROM
    {{ ref('stg_State') }}    
