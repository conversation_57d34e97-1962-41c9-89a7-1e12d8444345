version: 2

models:
  - name: V_Base_User_DW
    description: |
      Purpose:
      This view transforms authentication user data, providing a standardized base view
      with temporal validity handling for downstream consumption.

      Data Grain:
      One record per user ID per validity period.

      Direct Sources:
      - mx_authentication_User (snapshot table with user data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes records valid on the batch control date
      - Maintains SCD Type 2 temporal validity
      - Adjusts valid_to dates by subtracting one day to prevent overlaps
      - Selects latest record when multiple versions exist for same date

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: batch_date_filter
        description: |
          Retrieves the current batch date for processing:
          - Gets batch_control_dt from T_Batch_Control
          - Filters for batch_id 'MxDataMartDailyBuild'
          - Casts date to proper format
        sql: >
          SELECT 
              CAST(batch_control_dt AS DATE) AS batch_date
          FROM `medxoom-dev-fb5`.`Warehouse`.`T_Batch_Control`
          WHERE batch_id = 'MxDataMartDailyBuild'

      - name: temporal_validity
        description: |
          Applies temporal validity rules:
          - Ensures records are valid on batch date
          - Converts dbt_valid_from to valid_from date
          - Adjusts valid_to by subtracting one day when not 9999-12-31
          - Maintains proper date boundaries
        sql: >
          CAST(dbt_valid_from AS DATE) <= batch_date AND
          COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) >= batch_date

      - name: version_selection
        description: |
          Selects the most recent valid version:
          - Partitions by user id
          - Orders by update timestamp descending
          - Takes top record per partition
        sql: >
          ROW_NUMBER() OVER (
              PARTITION BY id
              ORDER BY dbt_updated_at DESC
          ) = 1

    columns:
      # Key Columns
      - name: id
        description: "Primary identifier for the user"
        type: string
        source_column: "id"
        source_table: "mx_authentication_User"

      # Temporal Columns
      - name: valid_from
        description: "Date when this version becomes valid"
        type: date
        source_column: "dbt_valid_from"
        source_table: "mx_authentication_User"
        transformation_logic: "CAST(dbt_valid_from AS DATE)"

      - name: valid_to
        description: "Date when this version expires"
        type: date
        source_column: "dbt_valid_to"
        source_table: "mx_authentication_User"
        transformation_logic: >
          CASE 
              WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
              THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
              ELSE DATE('9999-12-31')
          END

      # Audit Columns
      - name: dbt_valid_from
        description: "Original dbt SCD Type 2 valid from timestamp"
        type: timestamp
        source_column: "dbt_valid_from"
        source_table: "mx_authentication_User"

      - name: dbt_valid_to
        description: "Original dbt SCD Type 2 valid to timestamp"
        type: timestamp
        source_column: "dbt_valid_to"
        source_table: "mx_authentication_User"

      - name: dbt_updated_at
        description: "Timestamp of last dbt update"
        type: timestamp
        source_column: "dbt_updated_at"
        source_table: "mx_authentication_User"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_authentication_User"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Valid_to dates adjusted by subtracting one day"
        - "Username must be unique"




