version: 2

models:
  - name: V_Plan_DW
    description: |
      Purpose:
      This view transforms and standardizes insurance plan data, providing a consolidated view
      of plan information with temporal validity handling and product mappings.

      Data Grain:
      One record per unique combination of Plan ID and validity period.

      Direct Sources:
      - mx_insurance_Plan (snapshot table with plan data)
      - Allied_PlanId_To_PlanProduct (plan product mappings)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes active plans (Active = 1)
      - Maintains SCD Type 2 temporal validity
      - Extracts Plan Number from ExternalId
      - Links to Allied plan product mappings
      - Filters based on batch control date validity

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: batch_date_processing
        description: |
          Establishes temporal context using batch control date:
          - Retrieves current batch date from T_Batch_Control
          - Used for filtering valid records
          - Ensures consistent temporal snapshot
        sql: >
          SELECT CAST(batch_control_dt AS DATE) AS batch_date
          FROM T_Batch_Control
          WHERE batch_id = 'MxDataMartDailyBuild'

      - name: temporal_validity_handling
        description: |
          Processes temporal validity of plan records:
          - Converts dbt_valid_from/to to DATE type
          - Adjusts valid_to by subtracting one day when not 9999-12-31
          - Filters records valid on batch date
          - Selects latest record per ID based on dbt_updated_at

      - name: plan_product_mapping
        description: |
          Links plan data with product information:
          - Starts with base plan data
          - Extracts Plan_Number from ExternalId using SPLIT
          - Links to Allied product mappings for standardized names
        joins:
          - join: Allied_PlanId_To_PlanProduct
            type: left
            relationship: one_to_one
            sql: "base.Plan_Number = PlnProductMap.PlanNumber"

    columns:
      # Key Columns
      - name: Plan_SK
        description: "Surrogate key generated from plan attributes"
        type: string
        transformation_logic: "generate_surrogate_key(['Id', 'Type', 'ExternalId', 'Active', 'Name', 'GroupNumber', 'GroupId', 'PlanProduct'])"

      - name: Plan_Id
        description: "Natural key / ID from source system"
        type: string
        source_column: "Id"
        source_table: "mx_insurance_Plan"

      # Plan Attributes
      - name: Plan_Type
        description: "Type of insurance plan"
        type: string
        source_column: "Type"
        source_table: "mx_insurance_Plan"

      - name: Plan_External_Id
        description: "External identifier for the plan"
        type: string
        source_column: "ExternalId"
        source_table: "mx_insurance_Plan"

      - name: Plan_Number
        description: "Extracted plan number from ExternalId"
        type: string
        transformation_logic: "SPLIT(ExternalId,'-')[0]"

      - name: Plan_Product_Name
        description: "Standardized product name from Allied mappings"
        type: string
        source_column: "PlanProduct"
        source_table: "Allied_PlanId_To_PlanProduct"

      - name: Plan_Group_Number
        description: "Group number associated with the plan"
        type: string
        source_column: "GroupNumber"
        source_table: "mx_insurance_Plan"

      - name: Plan_Name
        description: "Name of the plan"
        type: string
        source_column: "Name"
        source_table: "mx_insurance_Plan"

      - name: Plan_Active
        description: "Flag indicating if plan is active"
        type: boolean
        source_column: "Active"
        source_table: "mx_insurance_Plan"

      - name: Group_Id
        description: "Associated group identifier"
        type: string
        source_column: "GroupId"
        source_table: "mx_insurance_Plan"

      # Temporal Columns
      - name: Effective_From_Dt
        description: "Date when this version becomes valid"
        type: date
        source_column: "dbt_valid_from"
        source_table: "mx_insurance_Plan"

      - name: Effective_To_Dt
        description: "Date when this version expires"
        type: date
        source_column: "dbt_valid_to"
        source_table: "mx_insurance_Plan"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp of record creation"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME"

      - name: Updated_dt_ts
        description: "Timestamp of last update"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(null AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(null AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_insurance_Plan"
        - "Allied_PlanId_To_PlanProduct"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Only active plans included (Active = 1)"
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Plan numbers extracted from ExternalId"
        - "Product names standardized through Allied mappings"
        - "Valid_to dates adjusted to prevent temporal overlaps"


