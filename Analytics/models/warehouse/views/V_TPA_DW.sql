{{ config(
    materialized = 'view',
    tags = ["MxDataMartDailyBuild"]
    )
}}
WITH base AS (
    SELECT 
        DISTINCT(trim(payername)) as Tpa_Name
    FROM {{ ref('mx_fileprocessing_Eligibility')}}


    WHERE 
        dbt_valid_to IS NULL 
        AND PayerName IS NOT NULL
        AND PayerName <> ' '
)
SELECT 
    {{ dbt_utils.generate_surrogate_key(
        ['Tpa_Name']
    )}} AS TPA_SK,
    Tpa_Name AS TPA_Name
    ,current_datetime() AS Created_dt_ts
    ,current_datetime() AS Updated_dt_ts
    ,CAST(NULL AS STRING) AS Created_By
    ,CAST(NULL AS STRING) AS Updated_By
FROM
    base