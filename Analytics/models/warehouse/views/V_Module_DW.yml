version: 2

models:
  - name: V_Module_DW
    description: |
      Purpose:
      This view transforms configuration module data, providing a standardized view of module
      information with temporal validity handling and attribute standardization.

      Data Grain:
      One record per module ID per validity period.

      Direct Sources:
      - mx_configuration_Module (snapshot table with module data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Maintains SCD Type 2 temporal validity
      - Standardizes category, UI type, and vendor values
      - Replaces '?', 'NA', 'none' with "Not Available"
      - Filters based on batch control date validity

    config:
      materialized: view
      tags: []

    transformations:
      - name: batch_date_filtering
        description: |
          Establishes temporal context using batch control date:
          - Retrieves current batch date from T_Batch_Control
          - Filters records valid on batch date
          - Ensures consistent temporal snapshot
          - Selects latest record when multiple versions exist

      - name: temporal_validity_handling
        description: |
          Processes temporal validity of module records:
          - Converts dbt_valid_from to valid_from date
          - Adjusts valid_to by subtracting one day when not 9999-12-31
          - Maintains SCD Type 2 history

      - name: attribute_standardization
        description: |
          Standardizes module attributes:
          - Replaces '?', 'NA', 'none' with "Not Available"
          - Applies to Category, UIType, Vendor, Description
          - Case-insensitive comparison for standardization
          - Maintains original values when not matching standardization criteria

    columns:
      # Key Columns
      - name: Module_SK
        description: "Surrogate key generated from module attributes"
        type: string
        transformation_logic: "GENERATE_UUID()"

      - name: Module_Id
        description: "Natural key from source system"
        type: string
        source_column: "Id"
        source_table: "mx_configuration_Module"

      # Attribute Columns
      - name: Module_Name
        description: "Name of the module"
        type: string
        source_column: "Name"
        source_table: "mx_configuration_Module"

      - name: Category_Name
        description: "Standardized category name"
        type: string
        source_column: "Category"
        source_table: "mx_configuration_Module"
        transformation_logic: "CASE WHEN Category = '?' OR Category ='NA' OR LOWER(Category) = 'none' THEN 'Not Available' ELSE Category END"

      - name: UI_Type
        description: "Standardized UI type"
        type: string
        source_column: "UIType"
        source_table: "mx_configuration_Module"
        transformation_logic: "CASE WHEN UIType = '?' OR UIType ='NA' OR LOWER(UIType) = 'none' THEN 'Not Available' ELSE UIType END"

      - name: Vendor_Name
        description: "Standardized vendor name"
        type: string
        source_column: "Vendor"
        source_table: "mx_configuration_Module"
        transformation_logic: "CASE WHEN Vendor = '?' OR Vendor ='NA' OR LOWER(Vendor) = 'none' THEN 'Not Available' ELSE Vendor END"

      - name: Description_Name
        description: "Standardized description"
        type: string
        source_column: "Description"
        source_table: "mx_configuration_Module"
        transformation_logic: "CASE WHEN Description = '?' OR Description ='NA' OR LOWER(Description) = 'none' THEN 'Not Available' ELSE Description END"

      - name: Module_Active
        description: "Flag indicating if module is active"
        type: boolean
        source_column: "Active"
        source_table: "mx_configuration_Module"

      # Temporal Columns
      - name: Effective_From_Dt
        description: "Date when this version became active"
        type: date
        source_column: "valid_from"
        source_table: "base CTE"

      - name: Effective_To_Dt
        description: "Date when this version was superseded"
        type: date
        source_column: "valid_to"
        source_table: "base CTE"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when record was created"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Updated_dt_ts
        description: "Timestamp when record was last updated"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2024-03-19"
      depends_on:
        - "mx_configuration_Module"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Valid_to dates adjusted by subtracting one day"
        - "Standardized values for category, UI type, vendor, and description"
        - "Null or invalid values replaced with 'Not Available'"



