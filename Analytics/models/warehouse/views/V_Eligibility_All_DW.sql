{{ config(
    materialized='view'
    ) 
}}

-- This view contains all the eligibility data for the warehouse including terminated members
WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
),

base AS (
    SELECT 
        *,
        CAST(dbt_valid_from AS DATE) AS valid_from,
        CASE 
            WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
            THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
            ELSE DATE('9999-12-31')
        END AS valid_to
        ,(SELECT batch_date.batch_date FROM batch_date) as As_Of_Date
    FROM {{ ref('mx_fileprocessing_Eligibility') }}
    WHERE 
        CAST(dbt_valid_from AS DATE) <= (SELECT batch_date.batch_date FROM batch_date)
        AND COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) >= (SELECT batch_date.batch_date FROM batch_date)
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY id
        ORDER BY dbt_updated_at DESC
    ) = 1
)

,filtered_data AS (
  SELECT *
  FROM base
  WHERE (TRIM(personnumber) <> '' AND personnumber IS NOT NULL)
    AND (membernumber IS NOT NULL AND TRIM(membernumber) <> '')
    AND LOWER(membernumber) NOT LIKE '%demo%'
    AND active = 1
),

ranked_data AS (
  SELECT *,
    ROW_NUMBER() OVER (
      PARTITION BY membernumber, groupnumber, personnumber, InsuranceType, PlanId
      ORDER BY CAST(EffectiveDate AS DATE) DESC, CreatedAt DESC
    ) AS row_num
  FROM filtered_data
)

SELECT 

Id as Elig_Id
,membernumber as Member_Number
,GroupNumber as Elig_Group_Number
,PersonNumber as Person_Number
,PlanId as Plan_External_Id
,InsuranceType as Insurance_Type
,FirstName as First_Name
,LastName as Last_Name
,Email
,CAST(DateOfBirth as DATE) as Date_Of_Birth
,SSN as Member_SSN
,COALESCE(SubscriberSSN , '999999999') as Subscriber_SSN
,Relationship as Relationship_Cd
,PayerName as TPA_Name
,CASE WHEN TerminationDate IS NULL OR TRIM(TerminationDate) = '' THEN DATE('9999-12-31')
ELSE CAST(TerminationDate AS DATE)
END AS Elig_Termination_Date
,CAST(EffectiveDate AS DATE) as Elig_Effective_Date
,Active as Elig_Active
,State
,TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.DirectoryId')) as LOB_Cd
,TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.EnrSeqNumber')) as Enr_Seq_Number
,TRIM(JSON_EXTRACT_SCALAR(CustomAttributes , '$.MemDepCode')) as Mem_Dep_Code

FROM ranked_data
WHERE row_num = 1
AND CAST(DateOfBirth as DATE) < CURRENT_DATE()
AND DateOfBirth <> '0001-01-01'
