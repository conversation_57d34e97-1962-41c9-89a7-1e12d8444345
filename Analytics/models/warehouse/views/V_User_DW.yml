version: 2

models:
  - name: V_User_DW
    description: |
      Purpose:
      This view provides a temporal snapshot of user authentication data, implementing
      SCD Type 2 handling for tracking historical changes in user attributes.

      Data Grain:
      One record per user ID per validity period, with only active users included.

      Direct Sources:
      - mx_authentication_User (snapshot table containing user data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Only includes active users (Active = 1)
      - Records must be valid on the batch control date
      - Adjusts valid_to dates to prevent temporal overlaps
      - Takes latest record when multiple versions exist for same date

    config:
      materialized: view
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: batch_date_determination
        description: |
          Retrieves the processing date from T_Batch_Control:
          - Uses MxDataMartDailyBuild batch_id
          - Converts batch_control_dt to DATE type
        sql: >
          SELECT CAST(batch_control_dt AS DATE) AS batch_date
          FROM T_Batch_Control
          WHERE batch_id = 'MxDataMartDailyBuild'

      - name: temporal_processing
        description: |
          Processes temporal validity of records:
          - Converts dbt_valid_from to valid_from date
          - Adjusts dbt_valid_to to valid_to date, subtracting one day when appropriate
          - Sets maximum valid_to date to 9999-12-31
          - Filters records valid on batch date
          - Deduplicates based on latest dbt_updated_at
        sql: >
          Converts and adjusts temporal columns
          Applies batch date filtering
          Uses ROW_NUMBER() for deduplication

    columns:
      # Key Columns
      - name: id
        description: "Unique identifier for the user"
        type: string
        source_column: "id"
        source_table: "mx_authentication_User"

      # Status Columns
      - name: Active
        description: "Flag indicating if the user is active (1) or inactive (0)"
        type: integer
        source_column: "Active"
        source_table: "mx_authentication_User"

      # Temporal Columns
      - name: valid_from
        description: "Start date of the record's validity period"
        type: date
        source_column: "dbt_valid_from"
        source_table: "mx_authentication_User"
        transformation_logic: "CAST(dbt_valid_from AS DATE)"

      - name: valid_to
        description: "End date of the record's validity period"
        type: date
        source_column: "dbt_valid_to"
        source_table: "mx_authentication_User"
        transformation_logic: >
          CASE 
            WHEN COALESCE(CAST(dbt_valid_to AS DATE), DATE('9999-12-31')) <> DATE('9999-12-31') 
            THEN DATE_SUB(CAST(dbt_valid_to AS DATE), INTERVAL 1 DAY)
            ELSE DATE('9999-12-31')
          END

      # Audit Columns
      - name: As_Of_Date
        description: "Batch processing date from T_Batch_Control"
        type: date
        source_column: "batch_control_dt"
        source_table: "T_Batch_Control"
        transformation_logic: "CAST(batch_control_dt AS DATE)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "mx_authentication_User"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Only active users included"
        - "One record per user per validity period"
        - "All records must be valid on the batch control date"
        - "Latest record selected when multiple versions exist"
        - "Valid_to dates adjusted by subtracting one day to prevent temporal overlaps"


