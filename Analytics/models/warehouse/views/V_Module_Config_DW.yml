version: 2

models:
  - name: V_Module_Config_DW
    description: |
      Purpose:
      This view transforms module configuration data, providing a standardized view of module
      settings and configurations with temporal validity handling.

      Data Grain:
      One record per module configuration ID per validity period.

      Direct Sources:
      - mx_configuration_ModuleConfig (snapshot table with module config data)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      None

      Key Business Rules:
      - Maintains SCD Type 2 temporal validity
      - Extracts menu name from Settings JSON field
      - Handles NULL values with "Not Applicable"
      - Filters based on batch control date validity
      - Selects latest record when multiple versions exist

    config:
      materialized: view
      tags: []

    transformations:
      - name: batch_date_filtering
        description: |
          Establishes temporal context using batch control date:
          - Retrieves current batch date from T_Batch_Control for 'MxDataMartDailyBuild'
          - Filters records valid on batch date
          - Ensures consistent temporal snapshot
        sql: >
          SELECT FROM T_Batch_Control WHERE batch_id = 'MxDataMartDailyBuild'

      - name: temporal_validity_handling
        description: |
          Processes temporal validity of configuration records:
          - Converts dbt_valid_from to valid_from date
          - Adjusts valid_to by subtracting one day when not 9999-12-31
          - Maintains SCD Type 2 history
          - Selects latest record per ID based on dbt_updated_at
        sql: >
          QUALIFY ROW_NUMBER() OVER (PARTITION BY id ORDER BY dbt_updated_at DESC) = 1

      - name: json_extraction
        description: |
          Extracts and transforms JSON settings:
          - Extracts MenuName from Settings JSON field
          - Handles NULL values with "Not Applicable"
          - Maintains original JSON structure
        sql: >
          JSON_EXTRACT_SCALAR(Settings, '$.MenuName')

    columns:
      # Key Columns
      - name: Module_Config_SK
        description: "Surrogate key generated from module config attributes"
        type: string
        transformation_logic: "GENERATE_UUID()"

      - name: Module_Config_Id
        description: "Natural key from source system"
        type: string
        source_column: "Id"
        source_table: "mx_configuration_ModuleConfig"

      # Attribute Columns
      - name: Module_Id
        description: "Reference to the associated module"
        type: string
        source_column: "Type"
        source_table: "mx_configuration_ModuleConfig"

      - name: Module_Config_Menu_Name
        description: "Menu name extracted from Settings JSON"
        type: string
        source_column: "Settings"
        source_table: "mx_configuration_ModuleConfig"
        transformation_logic: "IFNULL(JSON_EXTRACT_SCALAR(Settings, '$.MenuName'),'Not Applicable')"

      - name: Module_Config_External_Id
        description: "External identifier for the module configuration"
        type: string
        source_column: "ExternalId"
        source_table: "mx_configuration_ModuleConfig"
        transformation_logic: "IFNULL(ExternalId ,'Not Applicable')"

      - name: Module_Config_Active
        description: "Flag indicating if configuration is active"
        type: boolean
        source_column: "Active"
        source_table: "mx_configuration_ModuleConfig"

      # Temporal Columns
      - name: Effective_From_Dt
        description: "Date when this version became active"
        type: date
        source_column: "valid_from"
        source_table: "base CTE"

      - name: Effective_To_Dt
        description: "Date when this version was superseded"
        type: date
        source_column: "valid_to"
        source_table: "base CTE"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when record was created"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Updated_dt_ts
        description: "Timestamp when record was last updated"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2024-03-19"
      depends_on:
        - "mx_configuration_ModuleConfig"
        - "T_Batch_Control"
      table_type: "view"
      temporal_type: "scd_type_2"
      refresh_frequency: "daily"
      business_rules:
        - "Records must be valid on batch control date"
        - "Latest record selected when multiple versions exist"
        - "Valid_to dates adjusted by subtracting one day"
        - "Menu names extracted from Settings JSON"
        - "Null values replaced with 'Not Applicable'"


