{{ config(tags = ["MxDataMartDailyBuild"]) }}
{{ config(
    materialized = 'table'
    )
}}

WITH base as (
SELECT
        DISTINCT 
        pa.name AS Partner_Name,
        g.name AS Group_Name,
        planProduct.Plan_Product_Name AS PlanProduct_Name,
        CASE 
            WHEN e.Relationship = 1 THEN 'Primary Subscribers'
                ELSE 'Spouses and Dependents'
        END AS Member_Type,
        CAST(REPLACE(json_extract_scalar(e.CustomAttributes, '$.DirectoryId'),"\\","")as string) AS LoB_Number,
        CASE 
            WHEN e.terminationdate IS NULL OR e.terminationdate = '' 
                THEN CAST('{{ var("future_proof_date") }}' AS timestamp) 
                ELSE CAST(e.TerminationDate  AS timestamp) 
        END AS Termination_Date,
        DATE_DIFF(CURRENT_DATE(), CAST(e.DateOfBirth AS DATE), YEAR) AS Age,

    FROM
        {{ source ("ODS","mx_fileprocessing_Eligibility") }} e
        JOIN {{ source ("ODS","mx_insurance_Policy") }} p ON e.id = p.eligibilityid
        JOIN {{ source ("ODS","mx_insurance_Plan") }} pl  ON p.planid = pl.id
        JOIN {{ source ("ODS","mx_insurance_Group") }} g  ON g.id = pl.groupid
        JOIN {{ source ("ODS","mx_partnerorganization_PartnerGroup") }} pg ON pg.groupid = g.id
        JOIN {{ source ("ODS","mx_partnerorganization_Partner") }} pa ON pa.id = pg.partnerid
        LEFT JOIN {{ ref('V_Plan_PlanProduct')}} planProduct on pl.Id = planProduct.Plan_Id 

    WHERE
        e.active = 1
        AND p.active = 1
        AND pl.active = 1
        AND g.active = 1
        AND pg.active = 1
        AND pa.active = 1
        AND pl.Type = 1 -- Only Considering Medical Plans
        AND pa.name LIKE '[Billing]%'
        AND (
            e.terminationdate IS NULL OR e.terminationdate = '' OR CAST(e.TerminationDate  AS DATE) >= CURRENT_DATE()
        )

)

SELECT
    Partner_Name,
    Group_Name,
    PlanProduct_Name,
    Member_Type,
    LoB_Number,
    SUM(CASE WHEN Age < 13 THEN 1 ELSE 0 END) AS Under_13,
    SUM(CASE WHEN Age BETWEEN 13 AND 17 THEN 1 ELSE 0 END) AS Age_13_17,
    SUM(CASE WHEN Age BETWEEN 18 AND 24 THEN 1 ELSE 0 END) AS Age_18_24,
    SUM(CASE WHEN Age BETWEEN 25 AND 34 THEN 1 ELSE 0 END) AS Age_25_34,
    SUM(CASE WHEN Age BETWEEN 35 AND 44 THEN 1 ELSE 0 END) AS Age_35_44,
    SUM(CASE WHEN Age BETWEEN 45 AND 54 THEN 1 ELSE 0 END) AS Age_45_54,
    SUM(CASE WHEN Age BETWEEN 55 AND 64 THEN 1 ELSE 0 END) AS Age_55_64,
    SUM(CASE WHEN Age BETWEEN 65 AND 74 THEN 1 ELSE 0 END) AS Age_65_74,
    SUM(CASE WHEN Age > 74 THEN 1 ELSE 0 END) AS Age_75_Over

FROM 
    base

GROUP BY
    Partner_Name,
    Group_Name,
    PlanProduct_Name,
    Member_Type,
    LoB_Number
