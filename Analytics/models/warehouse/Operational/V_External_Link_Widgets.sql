{{ config(tags = ["MxDataMartDailyBuild"]) }}
{{ config(
    materialized = 'view'
    )
}}

WITH Base as (
    SELECT 
    module_config.Settings as Settings,
    mx_group.`Name` as <PERSON><PERSON><PERSON>,
    {{- get_partner_name_ignore_prefix(partner_name='partner.`Name`') }} as PartnerName
    
    FROM 
    {{ source('ODS','mx_configuration_ModuleConfig') }} module_config
    INNER JOIN {{ source('ODS','mx_insurance_Group') }} mx_group on module_config.GroupId = mx_group.Id
    INNER JOIN {{ source('ODS','mx_partnerorganization_PartnerGroup')}} partner_group on partner_group.GroupId = mx_group.Id
    INNER JOIN {{ source('ODS','mx_partnerorganization_Partner')}} partner ON partner.Id = partner_group.PartnerId

    WHERE module_config.`Type` = 7
    AND {{-get_partner_type(partner_name='partner.`Name`')}} = 'Billing'
)

SELECT 
*
FROM 
Base