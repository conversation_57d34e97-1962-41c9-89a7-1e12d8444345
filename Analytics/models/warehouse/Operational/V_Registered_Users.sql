{{ config(tags = ["MxDataMartDailyBuild"]) }}
{{ config(
    materialized = 'view'
    )
}}
WITH base as (
    SELECT auth.Username as UserName,
    eligibility.Ssn as SSN,
    eligibility.LastName as LastName,
    eligibility.FirstName  as FirstName,
    eligibility.Relationship as Relationship,
    eligibility.InsuranceType as InsuranceType,
    eligibility.MemberNumber as MemberNumber,
    eligibility.PersonNumber as PersonNumber,
    eligibility.CustomAttributes as CustomAttributes,
    grp.`Name` as GroupName,
    grp.Frozen as GroupFrozen,
    ptnr.`Name` as PartnerName
    FROM 
    {{ source('ODS','mx_authentication_User')}} auth
    INNER JOIN {{ source('ODS', 'mx_insurance_Policy')}} policy on policy.UserId = auth.Id
    INNER JOIN {{ source('ODS', 'mx_fileprocessing_Eligibility')}} eligibility on policy.EligibilityId = eligibility.Id
    INNER JOIN {{ source('ODS', 'mx_insurance_Plan') }} plan on policy.PlanId = plan.Id
    INNER JOIN {{ source('ODS', 'mx_insurance_Group') }} grp on plan.GroupId = grp.Id
    INNER JOIN {{ source('ODS', 'mx_partnerorganization_PartnerGroup')}} partnerGroup on partnerGroup.GroupId = grp.Id
    INNER JOIN {{ source('ODS', 'mx_partnerorganization_Partner')}} ptnr on partnerGroup.PartnerId = ptnr.Id

    WHERE 
    {{-get_partner_type(partner_name='ptnr.`Name`')}} = 'Billing'
    
    
)
SELECT * 
FROM 
base