{{ config(tags = ["MxDataMartDailyBuild"]) }}
{{ config(
    materialized = 'view'
    )
}}
WITH base AS (
    SELECT 
        Id,
        ExternalId as PlanExternalId,
        SPLIT(ExternalId,'-')[0] as PlanNumber
    FROM 
        {{ source('ODS','mx_insurance_Plan')}} 
),
PlanToPlanProduct AS (
    SELECT 
    base.Id as Plan_Id,
    base.PlanExternalId as Plan_External_Id,
    base.PlanNumber as Plan_Number,
    PlnProductMap.PlanProduct as Plan_Product_Name
    FROM 
        base
        LEFT JOIN {{ ref('Allied_PlanId_To_PlanProduct')}} PlnProductMap on base.PlanNumber = PlnProductMap.PlanNumber

)

SELECT 
*
FROM 
PlanToPlanProduct