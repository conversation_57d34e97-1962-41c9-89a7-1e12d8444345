version: 2

models:
  - name: stg_Metric
    description: |
      Purpose:
      This staging model provides the initial transformation layer for metric definitions,
      preparing the data for downstream warehouse processing. It serves as a standardized
      staging area for metric metadata.

      Data Grain:
      One record per unique metric definition.

      Direct Sources:
      - Metric (source table containing metric definitions and attributes)

      Indirect Sources:
      None

      Key Business Rules:
      - Direct pass-through of metric data from source
      - No filtering or transformations applied at this stage
      - Preserves all source columns for downstream processing

    config:
      materialized: table
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: source_data_load
        description: |
          Direct load from Metric source table:
          - Preserves all columns from source
          - No transformations applied
          - Maintains source data integrity
        sql: >
          SELECT * FROM Metric

    columns:
      # Key Columns
      - name: Metric_Id
        description: "Unique identifier for the metric"
        type: string
        source_column: "Metric_Id"
        source_table: "Metric"

      # Attribute Columns
      - name: Metric_Name
        description: "Name of the metric"
        type: string
        source_column: "Metric_Name"
        source_table: "Metric"

      - name: Subject_Area
        description: "Business domain or subject area for the metric"
        type: string
        source_column: "Subject_Area"
        source_table: "Metric"

      - name: Metric_Type
        description: "Classification or type of metric"
        type: string
        source_column: "Metric_Type"
        source_table: "Metric"

      - name: Business_Rules
        description: "Business rules or logic defining the metric"
        type: string
        source_column: "Business_Rules"
        source_table: "Metric"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the metric was created"
        type: timestamp
        source_column: "Created_dt_ts"
        source_table: "Metric"

      - name: Updated_dt_ts
        description: "Timestamp when the metric was last updated"
        type: timestamp
        source_column: "Updated_dt_ts"
        source_table: "Metric"

      - name: Created_By
        description: "User who created the metric"
        type: string
        source_column: "Created_By"
        source_table: "Metric"

      - name: Updated_By
        description: "User who last updated the metric"
        type: string
        source_column: "Updated_By"
        source_table: "Metric"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "Metric"
      table_type: "staging"
      temporal_type: "current_state"
      refresh_frequency: "daily"
      business_rules:
        - "Direct pass-through of all metric attributes"
        - "No transformations applied at staging level"
        - "Preserves source data integrity"
        - "Maintains all audit columns from source"


