{{ config(tags = ["MxDataMartDailyBuild"]) }}
WITH base AS (
    SELECT 
        * 
    FROM 
        {{ source(
            'ODS',
            'mx_fileprocessing_Eligibility'
        ) }}
),
refined AS (
    SELECT 
        *,
        JSON_VALUE(CustomAttributes, "$.BenefitClassCode") as BenefitClassCode,
        JSON_VALUE(CustomAttributes, "$.BenefitElections") as BenefitElections,
        JSON_VALUE(CustomAttributes, "$.Benefits") as Benefits,
        JSON_VALUE(CustomAttributes, "$.DirectoryId") as DirectoryId,
        JSON_VALUE(CustomAttributes, "$.DocumentIds") as DocumentIds,
        JSON_VALUE(CustomAttributes, "$.EnrSeqNumber") as EnrSeqNumber,
        JSON_VALUE(CustomAttributes, "$.FlexFlag") as FlexFlag,
        JSON_VALUE(CustomAttributes, "$.IDCardRecordId") as ID<PERSON>ardRecordId,
        JSON_VALUE(CustomAttributes, "$.MemDepCode") as MemDepCode,
        JSON_VALUE(CustomAttributes, "$.PaperlessEobElectionFlag") as PaperlessEobElectionFlag,
        JSON_VALUE(CustomAttributes, "$.SapphireAccount") as SapphireAccount,
        JSON_VALUE(CustomAttributes, "$.SapphireReportingPlanId") as SapphireReportingPlanId,
        JSON_VALUE(CustomAttributes, "$.SapphireTwoFact") as SapphireTwoFact
    FROM
        base
)
SELECT * FROM refined
