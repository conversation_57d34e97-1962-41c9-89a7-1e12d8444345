version: 2

models:
  - name: stg_State
    description: |
      Purpose:
      This staging model provides a standardized view of state reference data,
      serving as the initial transformation layer for state codes and attributes.

      Data Grain:
      One record per unique state.

      Direct Sources:
      - state_codes (seed file containing state reference data)

      Indirect Sources:
      None

      Key Business Rules:
      - Direct pass-through of state reference data
      - Maintains state codes and attributes as-is from source
      - No filtering or exclusions applied

    config:
      materialized: table
      tags: []

    transformations:
      - name: source_data_load
        description: |
          Direct load from state_codes seed file:
          - Preserves all columns from source
          - No transformations applied
          - Maintains source data integrity
        sql: >
          SELECT * FROM state_codes

    columns:
      # State Identification Columns
      - name: state_code
        description: "Two-letter state code"
        type: string
        source_column: "state_code"
        source_table: "state_codes"

      - name: state_name
        description: "Full state name"
        type: string
        source_column: "state_name"
        source_table: "state_codes"

      # Geographic Attributes
      - name: region
        description: "Geographic region of the state"
        type: string
        source_column: "region"
        source_table: "state_codes"

      - name: division
        description: "Geographic division within region"
        type: string
        source_column: "division"
        source_table: "state_codes"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "state_codes"
      table_type: "staging"
      temporal_type: "static"
      refresh_frequency: "on_demand"
      business_rules:
        - "Preserves all columns from state_codes seed file"
        - "No transformations or filtering applied"
        - "Maintains source data integrity"
        - "Serves as reference data for state lookups"


