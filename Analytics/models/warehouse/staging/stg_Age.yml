version: 2

models:
  - name: stg_Age
    description: |
      Purpose:
      This staging model provides a standardized view of age group definitions,
      serving as the initial transformation layer for age-based analytics.

      Data Grain:
      One record per unique age group definition.

      Direct Sources:
      - age_groups (seed file containing age group definitions)

      Indirect Sources:
      None

      Key Business Rules:
      - Direct pass-through of age group data
      - No filtering or transformations applied at this stage
      - Preserves all source attributes for downstream processing

    config:
      materialized: table
      tags: []

    transformations:
      - name: source_data_load
        description: |
          Direct load from age_groups seed file:
          - Preserves all columns from source
          - No transformations applied
          - Maintains source data integrity
        sql: >
          SELECT * FROM age_groups

    columns:
      # Key Columns
      - name: Age_SK
        description: "Surrogate key for the age group"
        type: integer
        source_column: "Age_SK"
        source_table: "age_groups"

      # Range Columns
      - name: Age_Start
        description: "Starting age of the age group range"
        type: integer
        source_column: "Age_Start"
        source_table: "age_groups"

      - name: Age_End
        description: "Ending age of the age group range"
        type: integer
        source_column: "Age_End"
        source_table: "age_groups"

      # Description Columns
      - name: Age_Group
        description: "Descriptive name of the age group"
        type: string
        source_column: "Age_Group"
        source_table: "age_groups"

      - name: Age_Category
        description: "Broader category classification of the age group"
        type: string
        source_column: "Age_Category"
        source_table: "age_groups"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "age_groups"
      table_type: "staging"
      temporal_type: "static"
      refresh_frequency: "on_demand"
      business_rules:
        - "Preserves all columns from age_groups seed file"
        - "No transformations or filtering applied"
        - "Maintains source data integrity"
        - "Serves as reference data for age-based analytics"


