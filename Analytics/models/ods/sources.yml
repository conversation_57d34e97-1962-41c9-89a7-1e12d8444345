sources:
  - name: ODS
    tables:
    - name: ext_GCS_EligibilitiesByDateRaw
    - name: mx_authentication_User
    - name: mx_comdata_VirtualCard
    - name: mx_configuration_BrandConfig
    - name: mx_configuration_MemberContact
    - name: mx_configuration_ModuleConfig
      description: "Configuration settings for modules in the system"
      columns:
        - name: Id
          description: "Unique identifier for the module configuration"
        - name: Active
          description: "Flag indicating if the module configuration is currently active"
        - name: GroupId
          description: "Identifier for the group this configuration belongs to"
        - name: Type
          description: "Type of module configuration"
        - name: Order
          description: "Ordering sequence for the module configuration"
        - name: Settings
          description: "JSON containing module-specific configuration settings"
        - name: ExternalId
          description: "External identifier for the module configuration"
    - name: mx_configuration_Module
      description: "Core module definitions and metadata"
      columns:
        - name: Id
          description: "Unique identifier for the module"
        - name: Name
          description: "Name of the module"
        - name: Category
          description: "Category classification of the module"
        - name: UIType
          description: "User interface type for the module"
        - name: Vendor
          description: "Vendor or provider of the module"
        - name: Description
          description: "Detailed description of the module's purpose and functionality"
        - name: Active
          description: "Flag indicating if the module is currently active"
    - name: mx_configuration_TerminologyConfig
    - name: mx_configuration_WebhookConfig
    - name: mx_fileprocessing_Accumulator
    - name: mx_authentication_LoginAttempt
    - name: mx_fileprocessing_ClaimDetail
    - name: mx_fileprocessing_ClaimHeader
    - name: mx_fileprocessing_CptLookup
    - name: mx_fileprocessing_Eligibility
      columns:
        - name: Id
        - name: Active
        - name: PlanId
        - name: DateOfBirth
        - name: EffectiveDate
        - name: MemberNumber
        - name: PersonNumber
        - name: GroupNumber
    - name: mx_identity_Person
    - name: mx_identity_PersonRelationship
    - name: mx_insurance_Benefit
    - name: mx_insurance_BenefitServiceTypeProcedureCode
    - name: mx_insurance_BenefitTier
    - name: mx_insurance_Document
    - name: mx_insurance_Group
      description: "Core table containing insurance group information and attributes"
      columns:
        - name: Id
          description: "Unique identifier for the group"
        - name: Active
          description: "Flag indicating if the group is currently active"
        - name: Name
          description: "Name of the insurance group"
        - name: Phone
          description: "Contact phone number for the group"
        - name: AddressLine1
          description: "Primary address line of the group"
        - name: AddressLine2
          description: "Secondary address line of the group"
        - name: City
          description: "City of the group's address"
        - name: State
          description: "State of the group's address"
        - name: PostalCode
          description: "Postal code of the group's address"
        - name: CountryCode
          description: "Country code of the group's address"
        - name: BillingCycleDay
          description: "Day of the month for billing cycle"
        - name: StartDate
          description: "Date when the group coverage starts"
        - name: TerminationDate
          description: "Date when the group coverage ends"
        - name: ExternalId
          description: "External identifier for the group"
        - name: BrandKey
          description: "Brand identifier associated with the group"
        - name: Frozen
          description: "Flag indicating if the group is frozen"
    - name: mx_insurance_Network
    - name: mx_insurance_Payer
      description: "Information about insurance payers/carriers"
      columns:
        - name: Id
          description: "Unique identifier for the payer"
        - name: Active
          description: "Flag indicating if the payer is currently active"
        - name: PayerId
          description: "External payer identifier"
        - name: Name
          description: "Name of the insurance payer"
        - name: ExternalId
          description: "External identifier for the payer"
        - name: Type
          description: "Type of insurance payer"
        - name: PlanImageUri
          description: "URI for the payer's plan image"
        - name: RxPcn
          description: "Pharmacy Processor Control Number"
        - name: RxBin
          description: "Pharmacy BIN number"
        - name: RxGroup
          description: "Pharmacy group identifier"
        - name: ClaimSubmittalAddressLine1
          description: "Primary address line for claim submission"
        - name: ClaimSubmittalAddressLine2
          description: "Secondary address line for claim submission"
        - name: ClaimSubmittalCity
          description: "City for claim submission"
        - name: ClaimSubmittalState
          description: "State for claim submission"
        - name: ClaimSubmittalPostalCode
          description: "Postal code for claim submission"
        - name: EdiPayerId
          description: "EDI identifier for the payer"
        - name: MemberSupportPhoneNumber
          description: "Phone number for member support"
        - name: ProviderSupportPhoneNumber
          description: "Phone number for provider support"
    - name: mx_insurance_Plan
      description: "Detailed insurance plan configurations and attributes"
      columns:
        - name: Id
          description: "Unique identifier for the plan"
        - name: Active
          description: "Flag indicating if the plan is currently active"
        - name: PayerId
          description: "Reference to the associated payer"
        - name: Name
          description: "Name of the insurance plan"
        - name: ExternalId
          description: "External identifier for the plan"
        - name: Type
          description: "Type of insurance plan (1=Medical, 2=Dental, 3=Vision)"
        - name: PlanImageUri
          description: "URI for the plan's image"
        - name: RxPcn
          description: "Plan-specific Pharmacy Processor Control Number"
        - name: RxBin
          description: "Plan-specific Pharmacy BIN number"
        - name: RxGroup
          description: "Plan-specific Pharmacy group identifier"
        - name: GroupId
          description: "Reference to the associated group"
        - name: GroupNumber
          description: "Group number for the plan"
        - name: SbcUri
          description: "URI for Summary of Benefits and Coverage document"
        - name: FamilyAccumulatorsMustBeMetForFamilies
          description: "Flag indicating family accumulator requirements"
        - name: SpdUri
          description: "URI for Summary Plan Description document"
        - name: PrefixNote
          description: "Note to display before plan details"
        - name: SuffixNote
          description: "Note to display after plan details"
        - name: HidePolicyIdentifiers
          description: "Flag to control visibility of policy identifiers"
        - name: MaskMemberNumbers
          description: "Flag indicating if member numbers should be masked"
        - name: AdditionalMonthsOfAccessAfterTermination
          description: "Extended access period after termination in months"
    - name: mx_insurance_PlanDocument
    - name: mx_insurance_Policy
    - name: mx_insurance_SapphirePlanTierNetwork
    - name: mx_insurance_SapphireProviderSearchNetwork
    - name: mx_javelina_Accumulator
    - name: mx_javelina_ClaimDetail
    - name: mx_javelina_ClaimHeader
    - name: mx_javelina_Eligibility
    - name: mx_partnerallied_ClaimLog
    - name: mx_partnerallied_ClaimLogDetail
    - name: mx_partnerorganization_Partner
    - name: mx_partnerorganization_PartnerGroup
    - name: mx_partnerorganization_PartnerUser
    - name: mx_payment_LobCheck
    - name: mx_payment_LobCheckRequestStatus
    - name: mx_payment_LobEvent
    - name: mx_payment_LobTrackingEvent
    - name: mx_payment_PaymentCard
    - name: mx_payment_PaymentCardRequest
    - name: mx_payment_PaymentCardRequestStatus
    - name: mx_payment_PaymentCardStatus
    - name: mx_payment_Transaction
    - name: mx_payment_TransactionFee
    - name: mx_profile_Profile
    - name: mx_providersearch_InsuranceNetwork
    - name: mx_providersearch_Network
    - name: mx_providersearch_PlanInsurance
    - name: mx_providersearch_PlanNetwork
    - name: mx_providersearch_PlanTierNetwork
    - name: mx_providersearch_PlanTierRibbonInsurance

  - name: mixpanel
    schema: mixpanel_data
    tables:
      - name: mp_master_event
        description: "Source table containing raw Mixpanel event data"
        columns:
          - name: mp_insert_id
            description: "Unique identifier for each event"
          - name: userid
            description: "User identifier associated with the event"
          - name: time
            description: "Timestamp of the event occurrence"
          - name: mp_event_name
            description: "Name of the event"
          - name: mp_os
            description: "Operating system of the device"
          - name: mp_browser
            description: "Browser used for the event"
          - name: mp_browser_version
            description: "Version of the browser"
          - name: mp_os_version
            description: "Version of the operating system"
          - name: mp_wifi
            description: "Boolean indicating if event occurred over WiFi"
          - name: mp_region
            description: "Geographic region of the user"
          - name: mp_country_code
            description: "Country code of the user"
          - name: mp_city
            description: "City of the user"
          - name: mp_carrier
            description: "Mobile carrier if applicable"
          - name: mp_brand
            description: "Device brand"
          - name: mp_anon_id
            description: "Anonymous identifier for non-logged in users"
          - name: member_number
            description: "Member number if applicable"
          - name: programname
            description: "Name of the program associated with the event"
          - name: cta_type
            description: "Call-to-action type"
          - name: methodchanneltype
            description: "Channel type for the event"
          - name: impersonation
            description: "Boolean indicating if event was from impersonated session"
          - name: mp_mp_api_timestamp_ms
            description: "API timestamp in milliseconds"
          - name: mp_failure_reason
            description: "Reason for event failure if applicable"
          - name: moduleconfigid
            description: "Module configuration identifier"
        meta:
          owner: "Data Engineering Team"
          update_frequency: "daily"
          primary_key: "mp_insert_id"
          contains_pii: true
          upstream_source: "Mixpanel"
          business_rules:
            - "Events must have a valid event name"
            - "Events must have a timestamp"
            - "Duplicate events are filtered by mp_insert_id"
