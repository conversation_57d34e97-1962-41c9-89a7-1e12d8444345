version: 2

models:
  - name: Registration_ReEngagements
    description: |
      Purpose:
      This table provides a comprehensive view of member registration and re-engagement metrics,
      combining registration status with various time-based re-engagement counts (daily, MTD, YTD, LTD).
      It serves as a gold-layer reporting table for analyzing member engagement patterns across
      different dimensions.

      Data Grain:
      One record per member type (subscriber/dependent) per group per LOB per partner per age group
      per state per TPA per as_of_date.

      Direct Sources:
      - [F_Member_Metrics](/#!/model/model.Analytics.F_Member_Metrics) (fact table containing member metrics)
      - [D_Group](/#!/model/model.Analytics.D_Group) (group dimension)
      - [D_Age](/#!/model/model.Analytics.D_Age) (age dimension)
      - [D_State](/#!/model/model.Analytics.D_State) (state dimension)
      - [D_TPA](/#!/model/model.Analytics.D_TPA) (TPA dimension)
      - [D_LOB](/#!/model/model.Analytics.D_LOB) (line of business dimension)
      - [V_Billing_Partner](/#!/model/model.Analytics.V_Billing_Partner) (billing partner view)
      - [D_Metric](/#!/model/model.Analytics.D_Metric) (metric dimension)

      Indirect Sources:
      - [V_F_Member_Metrics_L3](/#!/model/model.Analytics.V_F_Member_Metrics_L3) (via F_Member_Metrics)
      - [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2) (via V_F_Member_Metrics_L3)
      - [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1) (via V_F_Member_Metrics_L2)
      - [V_F_Member_ReEngagements](/#!/model/model.Analytics.V_F_Member_ReEngagements) (via V_F_Member_Metrics_L3)
      - [V_ReEngagements_Daily_DW](/#!/model/model.Analytics.V_ReEngagements_Daily_DW) (via V_F_Member_ReEngagements)
      - [V_ReEngagements_MTD_DW](/#!/model/model.Analytics.V_ReEngagements_MTD_DW) (via V_F_Member_ReEngagements)
      - [V_ReEngagements_YTD_Unique_DW](/#!/model/model.Analytics.V_ReEngagements_YTD_Unique_DW) (via V_F_Member_ReEngagements)
      - [V_ReEngagements_LTD_Unique_DW](/#!/model/model.Analytics.V_ReEngagements_LTD_Unique_DW) (via V_F_Member_ReEngagements)

      Key Business Rules:
      - Includes only active groups (Group_Active = 1)
      - Excludes frozen groups (Group_Frozen_Flag = 0)
      - Filters out terminated groups or includes only groups valid on as_of_date
      - Includes only active billing partners (Billing_Partner_Active = 1)
      - Excludes billing and eligibility metrics
      - Combines data from daily (last 30 days), 10th of month, and month-end snapshots

    config:
      materialized: table
      tags: ["gold", "daily", "registration", "engagement"]

    transformations:
      - name: time_period_data_selection
        description: |
          Selects data from different time periods:
          - Daily data from the last 30 days
          - 10th of month data for dates older than 30 days
          - Month-end data for dates older than 30 days
          - Combines these datasets for comprehensive time coverage

      - name: metric_filtering
        description: |
          Filters metrics to exclude billing and eligibility subject areas:
          - Excludes metrics where Subject_Area = 'Billing' or Subject_Area = 'Eligibility'
          - Ensures focus on registration and engagement metrics only

      - name: registration_metrics_calculation
        description: |
          Calculates registration metrics by member type:
          - Aggregates registered subscriber and dependent counts
          - Determines member type (Subscriber/Dependent) based on metric name
          - Applies group and partner activity filters
          - Groups by dimensional attributes for consistent grain
        joins:
          - join: D_Group
            type: inner
            relationship: many_to_one
            sql: "FMM.Group_SK = DG.Group_SK"

          - join: D_Age
            type: inner
            relationship: many_to_one
            sql: "FMM.Age_SK = DA.Age_SK"

          - join: D_State
            type: inner
            relationship: many_to_one
            sql: "FMM.State_SK = DS.State_SK"

          - join: D_TPA
            type: inner
            relationship: many_to_one
            sql: "FMM.TPA_SK = DT.TPA_SK"

          - join: D_LOB
            type: inner
            relationship: many_to_one
            sql: "FMM.LOB_SK = DL.LOB_SK"

          - join: V_Billing_Partner
            type: inner
            relationship: many_to_one
            sql: "FMM.Billing_Partner_SK = DP.Billing_Partner_SK"

          - join: D_Metric
            type: inner
            relationship: many_to_one
            sql: "FMM.Metric_SK = DM.Metric_SK"

      - name: total_member_calculation
        description: |
          Calculates total member counts (registered and unregistered):
          - Aggregates total eligible subscriber and dependent counts
          - Applies the same dimensional grouping as registration metrics
          - Uses the same filtering criteria for consistency

      - name: reengagement_metrics_calculation
        description: |
          Calculates various re-engagement metrics:
          - Daily total re-engagements
          - Daily unique re-engagements
          - Month-to-date (MTD) total re-engagements
          - Month-to-date (MTD) unique re-engagements
          - Life-to-date (LTD) unique re-engagements
          - Year-to-date (YTD) unique re-engagements
          - Each metric calculated separately and then joined together

      - name: final_data_integration
        description: |
          Combines all metrics into a single comprehensive dataset:
          - Uses all_elig_count as the base dataset
          - Left joins registration counts
          - Left joins all re-engagement metrics
          - Handles nulls with IFNULL to ensure zero values instead of nulls
          - Maintains dimensional grain throughout all joins

    columns:
      # Registration and Member Counts
      - name: RegisteredCount
        description: "Count of registered members (subscribers or dependents)"
        type: integer
        source_column: "Metric_Value"
        source_table: "F_Member_Metrics"
        transformation_logic: "IFNULL(base_reg.RegisteredCount, 0)"

      - name: MemberCount
        description: "Total count of members (registered and unregistered)"
        type: integer
        source_column: "Metric_Value"
        source_table: "F_Member_Metrics"

      # Date Dimension
      - name: AsOfDate
        description: "Reference date for the metrics"
        type: date
        source_column: "As_Of_Date"
        source_table: "F_Member_Metrics"

      # Dimensional Attributes
      - name: Group_Name
        description: "Name of the insurance group"
        type: string
        source_column: "Group_Name"
        source_table: "D_Group"

      - name: LOB_Cd
        description: "Line of business code"
        type: string
        source_column: "LOB_Cd"
        source_table: "D_LOB"

      - name: LOB_Name
        description: "Line of business name"
        type: string
        source_column: "LOB_Name"
        source_table: "D_LOB"

      - name: Billing_Partner_Name
        description: "Name of the billing partner"
        type: string
        source_column: "Billing_Partner_Name"
        source_table: "V_Billing_Partner"

      - name: Age_Group_1
        description: "Age group classification"
        type: string
        source_column: "Age_Group_1"
        source_table: "D_Age"

      - name: State_Cd
        description: "State code"
        type: string
        source_column: "State_Cd"
        source_table: "D_State"

      - name: TPA_Name
        description: "Third-party administrator name"
        type: string
        source_column: "TPA_Name"
        source_table: "D_TPA"

      - name: MemberType
        description: "Type of member (Subscriber or Dependent)"
        type: string
        transformation_logic: "CASE WHEN DM.Metric_Name LIKE '%Dependent%' THEN 'Dependent' WHEN DM.Metric_Name LIKE '%Subscriber%' THEN 'Subscriber' ELSE NULL END"

      # Re-engagement Metrics
      - name: Daily_Total_ReEngagement_Count
        description: "Total number of re-engagements for the day"
        type: integer
        source_column: "Metric_Value"
        source_table: "F_Member_Metrics"
        transformation_logic: "IFNULL(daily_reeng_count.Daily_Total_ReEngagement_Count, 0)"

      - name: MTD_Total_ReEngagement_Count
        description: "Total number of re-engagements month-to-date"
        type: integer
        source_column: "Metric_Value"
        source_table: "F_Member_Metrics"
        transformation_logic: "IFNULL(mtd_reeng_count.MTD_Total_ReEngagement_Count, 0)"

      - name: MTD_Unique_ReEngagement_Count
        description: "Number of unique members with re-engagements month-to-date"
        type: integer
        source_column: "Metric_Value"
        source_table: "F_Member_Metrics"
        transformation_logic: "IFNULL(mtd_unique_reeng_count.MTD_Unique_ReEngagement_Count, 0)"

      - name: LTD_Unique_ReEngagement_Count
        description: "Number of unique members with re-engagements life-to-date"
        type: integer
        source_column: "Metric_Value"
        source_table: "F_Member_Metrics"
        transformation_logic: "IFNULL(ltd_unique_reeng_count.LTD_Unique_ReEngagement_Count, 0)"

      - name: YTD_Unique_ReEngagement_Count
        description: "Number of unique members with re-engagements year-to-date"
        type: integer
        source_column: "Metric_Value"
        source_table: "F_Member_Metrics"
        transformation_logic: "IFNULL(ytd_unique_reeng_count.YTD_Unique_ReEngagement_Count, 0)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "F_Member_Metrics" # [F_Member_Metrics](/#!/model/model.Analytics.F_Member_Metrics)
        - "D_Group" # [D_Group](/#!/model/model.Analytics.D_Group)
        - "D_Age" # [D_Age](/#!/model/model.Analytics.D_Age)
        - "D_State" # [D_State](/#!/model/model.Analytics.D_State)
        - "D_TPA" # [D_TPA](/#!/model/model.Analytics.D_TPA)
        - "D_LOB" # [D_LOB](/#!/model/model.Analytics.D_LOB)
        - "V_Billing_Partner" # [V_Billing_Partner](/#!/model/model.Analytics.V_Billing_Partner)
        - "D_Metric" # [D_Metric](/#!/model/model.Analytics.D_Metric)
      table_type: "table"
      temporal_type: "current"
      refresh_frequency: "daily"
