{{ config(
    materialized='table'
) }}

WITH daily_data AS (
    SELECT *
    FROM {{ ref('F_Member_Metrics') }}
    WHERE As_Of_Date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
),

tenth_of_month AS (
    SELECT *
    FROM {{ ref('F_Member_Metrics') }}
    WHERE As_Of_Date < DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
    AND EXTRACT(DAY FROM As_Of_Date) = 10
),

monthly_data AS (
    SELECT *
    FROM {{ ref('F_Member_Metrics') }}
    WHERE As_Of_Date < DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
    AND EXTRACT(DAY FROM As_Of_Date) = EXTRACT(DAY FROM LAST_DAY(As_Of_Date))
)

,base_1 AS (
  SELECT *
  FROM daily_data

  UNION ALL

  SELECT * 
  FROM tenth_of_month

  UNION ALL

  SELECT *
  FROM monthly_data
)
,base as (
  select * from base_1 where metric_sk not in (select metric_sk from {{ ref('D_Metric') }} where  Subject_Area = 'Billing' or Subject_Area = 'Eligibility')
)

,base_reg as (
    SELECT
    FMM.As_Of_Date AS AsOfDate,
    DG.Group_Name,
    DL.Lob_Cd,
    DL.Lob_Name,
    TRIM(DP.Billing_Partner_Name) AS Billing_Partner_Name,
    'Registered' AS RegistrationStatus,
    CASE 
      WHEN DM.Metric_Name LIKE '%Dependent%' THEN 'Dependent'
      WHEN DM.Metric_Name LIKE '%Subscriber%' THEN 'Subscriber'
      ELSE NULL
    END AS MemberType,
    DA.Age_Group_1,
    DS.State_Cd,
    DT.TPA_Name,
    SUM(FMM.Metric_Value) AS RegisteredCount
  FROM base FMM
  JOIN {{ ref('D_Group') }} DG
    ON FMM.Group_SK = DG.Group_SK
  JOIN {{ ref('D_Age') }} DA
    ON FMM.Age_SK = DA.Age_SK
  JOIN {{ ref('D_State') }} DS
    ON FMM.State_SK = DS.State_SK
  JOIN {{ ref('D_TPA') }} DT
    ON FMM.TPA_SK = DT.TPA_SK
  JOIN {{ ref('D_LOB') }} DL
    ON FMM.LOB_SK = DL.LOB_SK
  JOIN {{ ref('V_Billing_Partner') }} DP
    ON FMM.Billing_Partner_SK = DP.Billing_Partner_SK
  JOIN {{ ref('D_Metric') }} DM
    ON FMM.Metric_SK = DM.Metric_SK


  WHERE DG.Group_Active = 1
  AND DG.Group_Frozen_Flag = 0
  AND (DG.Termination_Date is null OR CAST(DG.Termination_Date AS STRING) = '' OR DG.Termination_Date >= FMM.As_Of_Date)

  AND DM.Metric_Name IN ('Total_Elig_Registered_Subscriber_Cnt','Total_Elig_Registered_Dependent_Cnt')

  AND DP.Billing_Partner_Active = 1

  AND DM.Metric_Name LIKE '%Registered%'
  GROUP BY
  FMM.As_Of_Date
  ,DG.Group_Name
  ,DL.LOB_Cd
  ,DL.LOB_Name
  ,DP.Billing_Partner_Name
  ,DA.Age_Group_1
  ,DS.State_Cd
  ,DT.TPA_Name
  ,MemberType

)

,all_elig_count AS (
        SELECT
      FMM.As_Of_Date AS AsOfDate,
      DG.Group_Name,
      DL.Lob_Cd,
      DL.Lob_Name,
      TRIM(DP.Billing_Partner_Name) AS Billing_Partner_Name,
      'Registered or Unregistered' AS RegistrationStatus,
      CASE 
        WHEN DM.Metric_Name LIKE '%Dependent%' THEN 'Dependent'
        WHEN DM.Metric_Name LIKE '%Subscriber%' THEN 'Subscriber'
        ELSE NULL
      END AS MemberType,
      DA.Age_Group_1,
      DS.State_Cd,
      DT.TPA_Name,
      SUM(FMM.Metric_Value) AS MemberCount
    FROM base FMM
    JOIN {{ ref('D_Group') }} DG
      ON FMM.Group_SK = DG.Group_SK
    JOIN {{ ref('D_Age') }} DA
      ON FMM.Age_SK = DA.Age_SK
    JOIN {{ ref('D_State') }} DS
      ON FMM.State_SK = DS.State_SK
    JOIN {{ ref('D_TPA') }} DT
      ON FMM.TPA_SK = DT.TPA_SK
    JOIN {{ ref('D_LOB') }} DL
      ON FMM.LOB_SK = DL.LOB_SK
    JOIN {{ ref('V_Billing_Partner') }} DP
      ON FMM.Billing_Partner_SK = DP.Billing_Partner_SK
    JOIN {{ ref('D_Metric') }} DM
      ON FMM.Metric_SK = DM.Metric_SK

    WHERE

    DG.Group_Active = 1
    AND DG.Group_Frozen_Flag = 0
    AND (DG.Termination_Date is null OR CAST(DG.Termination_Date AS STRING) = '' OR DG.Termination_Date >= FMM.As_Of_Date)

    AND DM.Metric_Name IN ('Total_Elig_Dependent_Cnt','Total_Elig_Subscriber_Cnt')

    AND DP.Billing_Partner_Active = 1
    
    GROUP BY
    FMM.As_Of_Date
    ,DG.Group_Name
    ,DL.LOB_Cd
    ,DL.LOB_Name
    ,DP.Billing_Partner_Name
    ,DA.Age_Group_1
    ,DS.State_Cd
    ,DT.TPA_Name
    ,MemberType
)

,daily_reeng_count AS (
        SELECT
      FMM.As_Of_Date AS AsOfDate,
      DG.Group_Name,
      DL.Lob_Cd,
      DL.Lob_Name,
      TRIM(DP.Billing_Partner_Name) AS Billing_Partner_Name,
      CASE 
        WHEN DM.Metric_Name LIKE '%Dependent%' THEN 'Dependent'
        WHEN DM.Metric_Name LIKE '%Subscriber%' THEN 'Subscriber'
        ELSE NULL
      END AS MemberType,
      DA.Age_Group_1,
      DS.State_Cd,
      DT.TPA_Name,
      SUM(FMM.Metric_Value) AS Daily_Total_ReEngagement_Count
    FROM base FMM
    JOIN {{ ref('D_Group') }} DG
      ON FMM.Group_SK = DG.Group_SK
    JOIN {{ ref('D_Age') }} DA
      ON FMM.Age_SK = DA.Age_SK
    JOIN {{ ref('D_State') }} DS
      ON FMM.State_SK = DS.State_SK
    JOIN {{ ref('D_TPA') }} DT
      ON FMM.TPA_SK = DT.TPA_SK
    JOIN {{ ref('D_LOB') }} DL
      ON FMM.LOB_SK = DL.LOB_SK
    JOIN {{ ref('V_Billing_Partner') }} DP
      ON FMM.Billing_Partner_SK = DP.Billing_Partner_SK
    JOIN {{ ref('D_Metric') }} DM
      ON FMM.Metric_SK = DM.Metric_SK

    WHERE

    DG.Group_Active = 1
    AND DG.Group_Frozen_Flag = 0
    AND (DG.Termination_Date is null OR CAST(DG.Termination_Date AS STRING) = '' OR DG.Termination_Date >= FMM.As_Of_Date)

    AND DM.Metric_Name  IN ('Total_Subscriber_ReEngagements_Daily','Total_Dependent_ReEngagements_Daily')

    AND DP.Billing_Partner_Active = 1
    
    GROUP BY
    FMM.As_Of_Date
    ,DG.Group_Name
    ,DL.LOB_Cd
    ,DL.LOB_Name
    ,DP.Billing_Partner_Name
    ,DA.Age_Group_1
    ,DS.State_Cd
    ,DT.TPA_Name
    ,MemberType
)

,daily_unique_reeng_count AS (
        SELECT
      FMM.As_Of_Date AS AsOfDate,
      DG.Group_Name,
      DL.Lob_Cd,
      DL.Lob_Name,
      TRIM(DP.Billing_Partner_Name) AS Billing_Partner_Name,
      CASE 
        WHEN DM.Metric_Name LIKE '%Dependent%' THEN 'Dependent'
        WHEN DM.Metric_Name LIKE '%Subscriber%' THEN 'Subscriber'
        ELSE NULL
      END AS MemberType,
      DA.Age_Group_1,
      DS.State_Cd,
      DT.TPA_Name,
      SUM(FMM.Metric_Value) AS Daily_Unique_ReEngagement_Count
    FROM base FMM
    JOIN {{ ref('D_Group') }} DG
      ON FMM.Group_SK = DG.Group_SK
    JOIN {{ ref('D_Age') }} DA
      ON FMM.Age_SK = DA.Age_SK
    JOIN {{ ref('D_State') }} DS
      ON FMM.State_SK = DS.State_SK
    JOIN {{ ref('D_TPA') }} DT
      ON FMM.TPA_SK = DT.TPA_SK
    JOIN {{ ref('D_LOB') }} DL
      ON FMM.LOB_SK = DL.LOB_SK
    JOIN {{ ref('V_Billing_Partner') }} DP
      ON FMM.Billing_Partner_SK = DP.Billing_Partner_SK
    JOIN {{ ref('D_Metric') }} DM
      ON FMM.Metric_SK = DM.Metric_SK

    WHERE

    DG.Group_Active = 1
    AND DG.Group_Frozen_Flag = 0
    AND (DG.Termination_Date is null OR CAST(DG.Termination_Date AS STRING) = '' OR DG.Termination_Date >= FMM.As_Of_Date)

    AND DM.Metric_Name  IN ('Total_Subscriber_ReEngagements_Daily_Unique','Total_Dependent_ReEngagements_Daily_Unique')

    AND DP.Billing_Partner_Active = 1
    
    GROUP BY
    FMM.As_Of_Date
    ,DG.Group_Name
    ,DL.LOB_Cd
    ,DL.LOB_Name
    ,DP.Billing_Partner_Name
    ,DA.Age_Group_1
    ,DS.State_Cd
    ,DT.TPA_Name
    ,MemberType
)

,mtd_reeng_count AS (
        SELECT
      FMM.As_Of_Date AS AsOfDate,
      DG.Group_Name,
      DL.Lob_Cd,
      DL.Lob_Name,
      TRIM(DP.Billing_Partner_Name) AS Billing_Partner_Name,
      CASE 
        WHEN DM.Metric_Name LIKE '%Dependent%' THEN 'Dependent'
        WHEN DM.Metric_Name LIKE '%Subscriber%' THEN 'Subscriber'
        ELSE NULL
      END AS MemberType,
      DA.Age_Group_1,
      DS.State_Cd,
      DT.TPA_Name,
      SUM(FMM.Metric_Value) AS MTD_Total_ReEngagement_Count
    FROM base FMM
    JOIN {{ ref('D_Group') }} DG
      ON FMM.Group_SK = DG.Group_SK
    JOIN {{ ref('D_Age') }} DA
      ON FMM.Age_SK = DA.Age_SK
    JOIN {{ ref('D_State') }} DS
      ON FMM.State_SK = DS.State_SK
    JOIN {{ ref('D_TPA') }} DT
      ON FMM.TPA_SK = DT.TPA_SK
    JOIN {{ ref('D_LOB') }} DL
      ON FMM.LOB_SK = DL.LOB_SK
    JOIN {{ ref('V_Billing_Partner') }} DP
      ON FMM.Billing_Partner_SK = DP.Billing_Partner_SK
    JOIN {{ ref('D_Metric') }} DM
      ON FMM.Metric_SK = DM.Metric_SK

    WHERE

    DG.Group_Active = 1
    AND DG.Group_Frozen_Flag = 0
    AND (DG.Termination_Date is null OR CAST(DG.Termination_Date AS STRING) = '' OR DG.Termination_Date >= FMM.As_Of_Date)

    AND DM.Metric_Name  IN ('Total_Subscriber_ReEngagements_MTD','Total_Dependent_ReEngagements_MTD')

    AND DP.Billing_Partner_Active = 1
    
    GROUP BY
    FMM.As_Of_Date
    ,DG.Group_Name
    ,DL.LOB_Cd
    ,DL.LOB_Name
    ,DP.Billing_Partner_Name
    ,DA.Age_Group_1
    ,DS.State_Cd
    ,DT.TPA_Name
    ,MemberType
)

,mtd_unique_reeng_count AS (
    SELECT
      FMM.As_Of_Date AS AsOfDate,
      DG.Group_Name,
      DL.Lob_Cd,
      DL.Lob_Name,
      TRIM(DP.Billing_Partner_Name) AS Billing_Partner_Name,
      CASE 
        WHEN DM.Metric_Name LIKE '%Dependent%' THEN 'Dependent'
        WHEN DM.Metric_Name LIKE '%Subscriber%' THEN 'Subscriber'
        ELSE NULL
      END AS MemberType,
      DA.Age_Group_1,
      DS.State_Cd,
      DT.TPA_Name,
      SUM(FMM.Metric_Value) AS MTD_Unique_ReEngagement_Count
    FROM base FMM
    JOIN {{ ref('D_Group') }} DG
      ON FMM.Group_SK = DG.Group_SK
    JOIN {{ ref('D_Age') }} DA
      ON FMM.Age_SK = DA.Age_SK
    JOIN {{ ref('D_State') }} DS
      ON FMM.State_SK = DS.State_SK
    JOIN {{ ref('D_TPA') }} DT
      ON FMM.TPA_SK = DT.TPA_SK
    JOIN {{ ref('D_LOB') }} DL
      ON FMM.LOB_SK = DL.LOB_SK
    JOIN {{ ref('V_Billing_Partner') }} DP
      ON FMM.Billing_Partner_SK = DP.Billing_Partner_SK
    JOIN {{ ref('D_Metric') }} DM
      ON FMM.Metric_SK = DM.Metric_SK

    WHERE

    DG.Group_Active = 1
    AND DG.Group_Frozen_Flag = 0
    AND (DG.Termination_Date is null OR CAST(DG.Termination_Date AS STRING) = '' OR DG.Termination_Date >= FMM.As_Of_Date)

    AND DM.Metric_Name  IN ('Total_Subscriber_ReEngagements_MTD_Unique','Total_Dependent_ReEngagements_MTD_Unique')

    AND DP.Billing_Partner_Active = 1
    
    GROUP BY
    FMM.As_Of_Date
    ,DG.Group_Name
    ,DL.LOB_Cd
    ,DL.LOB_Name
    ,DP.Billing_Partner_Name
    ,DA.Age_Group_1
    ,DS.State_Cd
    ,DT.TPA_Name
    ,MemberType
)

,ltd_unique_reeng_count AS (
  SELECT
      FMM.As_Of_Date AS AsOfDate,
      DG.Group_Name,
      DL.Lob_Cd,
      DL.Lob_Name,
      TRIM(DP.Billing_Partner_Name) AS Billing_Partner_Name,
      CASE 
        WHEN DM.Metric_Name LIKE '%Dependent%' THEN 'Dependent'
        WHEN DM.Metric_Name LIKE '%Subscriber%' THEN 'Subscriber'
        ELSE NULL
      END AS MemberType,
      DA.Age_Group_1,
      DS.State_Cd,
      DT.TPA_Name,
      SUM(FMM.Metric_Value) AS LTD_Unique_ReEngagement_Count
    FROM base FMM
    JOIN {{ ref('D_Group') }} DG
      ON FMM.Group_SK = DG.Group_SK
    JOIN {{ ref('D_Age') }} DA
      ON FMM.Age_SK = DA.Age_SK
    JOIN {{ ref('D_State') }} DS
      ON FMM.State_SK = DS.State_SK
    JOIN {{ ref('D_TPA') }} DT
      ON FMM.TPA_SK = DT.TPA_SK
    JOIN {{ ref('D_LOB') }} DL
      ON FMM.LOB_SK = DL.LOB_SK
    JOIN {{ ref('V_Billing_Partner') }} DP
      ON FMM.Billing_Partner_SK = DP.Billing_Partner_SK
    JOIN {{ ref('D_Metric') }} DM
      ON FMM.Metric_SK = DM.Metric_SK

    WHERE

    DG.Group_Active = 1
    AND DG.Group_Frozen_Flag = 0
    AND (DG.Termination_Date is null OR CAST(DG.Termination_Date AS STRING) = '' OR DG.Termination_Date >= FMM.As_Of_Date)

    AND DM.Metric_Name  IN ('Total_Subscriber_ReEngagements_LTD_Unique','Total_Dependent_ReEngagements_LTD_Unique')

    AND DP.Billing_Partner_Active = 1
    
    GROUP BY
    FMM.As_Of_Date
    ,DG.Group_Name
    ,DL.LOB_Cd
    ,DL.LOB_Name
    ,DP.Billing_Partner_Name
    ,DA.Age_Group_1
    ,DS.State_Cd
    ,DT.TPA_Name
    ,MemberType 

)

,ytd_unique_reeng_count AS (
  SELECT
      FMM.As_Of_Date AS AsOfDate,
      DG.Group_Name,
      DL.Lob_Cd,
      DL.Lob_Name,
      TRIM(DP.Billing_Partner_Name) AS Billing_Partner_Name,
      CASE 
        WHEN DM.Metric_Name LIKE '%Dependent%' THEN 'Dependent'
        WHEN DM.Metric_Name LIKE '%Subscriber%' THEN 'Subscriber'
        ELSE NULL
      END AS MemberType,
      DA.Age_Group_1,
      DS.State_Cd,
      DT.TPA_Name,
      SUM(FMM.Metric_Value) AS YTD_Unique_ReEngagement_Count
    FROM base FMM
    JOIN {{ ref('D_Group') }} DG
      ON FMM.Group_SK = DG.Group_SK
    JOIN {{ ref('D_Age') }} DA
      ON FMM.Age_SK = DA.Age_SK
    JOIN {{ ref('D_State') }} DS
      ON FMM.State_SK = DS.State_SK
    JOIN {{ ref('D_TPA') }} DT
      ON FMM.TPA_SK = DT.TPA_SK
    JOIN {{ ref('D_LOB') }} DL
      ON FMM.LOB_SK = DL.LOB_SK
    JOIN {{ ref('V_Billing_Partner') }} DP
      ON FMM.Billing_Partner_SK = DP.Billing_Partner_SK
    JOIN {{ ref('D_Metric') }} DM
      ON FMM.Metric_SK = DM.Metric_SK

    WHERE

    DG.Group_Active = 1
    AND DG.Group_Frozen_Flag = 0
    AND (DG.Termination_Date is null OR CAST(DG.Termination_Date AS STRING) = '' OR DG.Termination_Date >= FMM.As_Of_Date)

    AND DM.Metric_Name  IN ('Total_Subscriber_ReEngagements_YTD_Unique','Total_Dependent_ReEngagements_YTD_Unique')

    AND DP.Billing_Partner_Active = 1
    
    GROUP BY
    FMM.As_Of_Date
    ,DG.Group_Name
    ,DL.LOB_Cd
    ,DL.LOB_Name
    ,DP.Billing_Partner_Name
    ,DA.Age_Group_1
    ,DS.State_Cd
    ,DT.TPA_Name
    ,MemberType 

)


  SELECT
  IFNULL(base_reg.RegisteredCount,0) AS RegisteredCount
  ,all_elig_count.MemberCount
  ,all_elig_count.AsOfDate
  ,all_elig_count.Group_Name
  ,all_elig_count.LOB_Cd
  ,all_elig_count.LOB_Name
  ,all_elig_count.Billing_Partner_Name
  ,all_elig_count.Age_Group_1
  ,all_elig_count.State_Cd
  ,all_elig_count.TPA_Name
  ,all_elig_count.MemberType
  ,IFNULL(daily_reeng_count.Daily_Total_ReEngagement_Count,0) AS Daily_Total_ReEngagement_Count
  ,IFNULL(mtd_reeng_count.MTD_Total_ReEngagement_Count,0) AS MTD_Total_ReEngagement_Count
  ,IFNULL(mtd_unique_reeng_count.MTD_Unique_ReEngagement_Count ,0) AS MTD_Unique_ReEngagement_Count
  ,IFNULL(ltd_unique_reeng_count.LTD_Unique_ReEngagement_Count ,0) AS LTD_Unique_ReEngagement_Count
  ,IFNULL(ytd_unique_reeng_count.YTD_Unique_ReEngagement_Count ,0) AS YTD_Unique_ReEngagement_Count

FROM all_elig_count
LEFT JOIN base_reg
ON all_elig_count.AsOfDate = base_reg.AsOfDate
  AND all_elig_count.Group_Name = base_reg.Group_Name
  AND all_elig_count.LOB_Cd = base_reg.LOB_Cd
  AND all_elig_count.LOB_Name = base_reg.LOB_Name
  AND all_elig_count.Billing_Partner_Name = base_reg.Billing_Partner_Name
  AND all_elig_count.Age_Group_1 = base_reg.Age_Group_1
  AND all_elig_count.State_Cd = base_reg.State_Cd
  AND all_elig_count.TPA_Name = base_reg.TPA_Name
  AND all_elig_count.MemberType = base_reg.MemberType

LEFT JOIN daily_reeng_count
ON all_elig_count.AsOfDate = daily_reeng_count.AsOfDate
  AND all_elig_count.Group_Name = daily_reeng_count.Group_Name
  AND all_elig_count.LOB_Cd = daily_reeng_count.LOB_Cd
  AND all_elig_count.LOB_Name = daily_reeng_count.LOB_Name
  AND all_elig_count.Billing_Partner_Name = daily_reeng_count.Billing_Partner_Name
  AND all_elig_count.Age_Group_1 = daily_reeng_count.Age_Group_1
  AND all_elig_count.State_Cd = daily_reeng_count.State_Cd
  AND all_elig_count.TPA_Name = daily_reeng_count.TPA_Name
  AND all_elig_count.MemberType = daily_reeng_count.MemberType
  
LEFT JOIN mtd_reeng_count
ON all_elig_count.AsOfDate = mtd_reeng_count.AsOfDate
  AND all_elig_count.Group_Name = mtd_reeng_count.Group_Name
  AND all_elig_count.LOB_Cd = mtd_reeng_count.LOB_Cd
  AND all_elig_count.LOB_Name = mtd_reeng_count.LOB_Name
  AND all_elig_count.Billing_Partner_Name = mtd_reeng_count.Billing_Partner_Name
  AND all_elig_count.Age_Group_1 = mtd_reeng_count.Age_Group_1
  AND all_elig_count.State_Cd = mtd_reeng_count.State_Cd
  AND all_elig_count.TPA_Name = mtd_reeng_count.TPA_Name
  AND all_elig_count.MemberType = mtd_reeng_count.MemberType

LEFT JOIN mtd_unique_reeng_count
ON all_elig_count.AsOfDate = mtd_unique_reeng_count.AsOfDate
  AND all_elig_count.Group_Name = mtd_unique_reeng_count.Group_Name
  AND all_elig_count.LOB_Cd = mtd_unique_reeng_count.LOB_Cd
  AND all_elig_count.LOB_Name = mtd_unique_reeng_count.LOB_Name
  AND all_elig_count.Billing_Partner_Name = mtd_unique_reeng_count.Billing_Partner_Name
  AND all_elig_count.Age_Group_1 = mtd_unique_reeng_count.Age_Group_1
  AND all_elig_count.State_Cd = mtd_unique_reeng_count.State_Cd
  AND all_elig_count.TPA_Name = mtd_unique_reeng_count.TPA_Name
  AND all_elig_count.MemberType = mtd_unique_reeng_count.MemberType

LEFT JOIN ltd_unique_reeng_count
ON all_elig_count.AsOfDate = ltd_unique_reeng_count.AsOfDate
  AND all_elig_count.Group_Name = ltd_unique_reeng_count.Group_Name
  AND all_elig_count.LOB_Cd = ltd_unique_reeng_count.LOB_Cd
  AND all_elig_count.LOB_Name = ltd_unique_reeng_count.LOB_Name
  AND all_elig_count.Billing_Partner_Name = ltd_unique_reeng_count.Billing_Partner_Name
  AND all_elig_count.Age_Group_1 = ltd_unique_reeng_count.Age_Group_1
  AND all_elig_count.State_Cd = ltd_unique_reeng_count.State_Cd
  AND all_elig_count.TPA_Name = ltd_unique_reeng_count.TPA_Name
  AND all_elig_count.MemberType = ltd_unique_reeng_count.MemberType

LEFT JOIN ytd_unique_reeng_count
ON all_elig_count.AsOfDate = ytd_unique_reeng_count.AsOfDate
  AND all_elig_count.Group_Name = ytd_unique_reeng_count.Group_Name
  AND all_elig_count.LOB_Cd = ytd_unique_reeng_count.LOB_Cd
  AND all_elig_count.LOB_Name = ytd_unique_reeng_count.LOB_Name
  AND all_elig_count.Billing_Partner_Name = ytd_unique_reeng_count.Billing_Partner_Name
  AND all_elig_count.Age_Group_1 = ytd_unique_reeng_count.Age_Group_1
  AND all_elig_count.State_Cd = ytd_unique_reeng_count.State_Cd
  AND all_elig_count.TPA_Name = ytd_unique_reeng_count.TPA_Name
  AND all_elig_count.MemberType = ytd_unique_reeng_count.MemberType