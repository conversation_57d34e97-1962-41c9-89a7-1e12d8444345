version: 2

models:
  - name: Member_Event_History
    description: |
      Gold layer table containing member event metrics and associated dimensional attributes.
      
      Purpose: Tracks member engagement events with enriched dimensional context for analytics and reporting.
      
      Grain: One row per unique event (Event_Id) per day
      
      Direct Sources:
      - [F_Event_Metrics](/#!/model/model.Analytics.F_Event_Metrics)
      - [D_Group](/#!/model/model.Analytics.D_Group)
      - [V_Billing_Partner](/#!/model/model.Analytics.V_Billing_Partner)
      - [D_LOB](/#!/model/model.Analytics.D_LOB)
      - [D_TPA](/#!/model/model.Analytics.D_TPA)
      - [D_Event_Region](/#!/model/model.Analytics.D_Event_Region)
      - [D_Time_Of_Day](/#!/model/model.Analytics.D_Time_Of_Day)
      - [D_Event](/#!/model/model.Analytics.D_Event)
      - [D_Module_Config](/#!/model/model.Analytics.D_Module_Config)
      - [D_Access_Mode](/#!/model/model.Analytics.D_Access_Mode)
      
      Indirect Sources:
      - [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1) (via F_Event_Metrics)
      - [V_Base_User_DW](/#!/model/model.Analytics.V_Base_User_DW) (via F_Event_Metrics)
      - [mx_insurance_Group](/#!/model/model.Analytics.mx_insurance_Group) (via D_Group)
      - [mx_insurance_GroupAddress](/#!/model/model.Analytics.mx_insurance_GroupAddress) (via D_Group)
      - [mx_insurance_Partner](/#!/model/model.Analytics.mx_insurance_Partner) (via V_Billing_Partner)
      - [mx_partnerorganization_PartnerGroup](/#!/model/model.Analytics.mx_partnerorganization_PartnerGroup) (via V_Billing_Partner)
      - [mx_configuration_ModuleConfig](/#!/model/model.Analytics.mx_configuration_ModuleConfig) (via D_Module_Config)
      - [mx_authentication_User](/#!/model/model.Analytics.mx_authentication_User) (via V_Base_User_DW)
      - [ext_GCS_EligibilitiesByDateRaw](/#!/model/model.Analytics.ext_GCS_EligibilitiesByDateRaw) (via V_F_Member_Metrics_L1)
      
      Key Business Rules:
      - Filters events based on daily batch date from T_Batch_Control
      - Incremental processing with daily refresh
      - Pre-hook deletes existing records for the batch date before insertion
      - Excludes events where Event_Category_1 is 'Unknown'

      Dependent Object Business Rules:
      F_Event_Metrics:
        - Filters events based on batch control date
        - Deduplicates events using ROW_NUMBER on Event_Id
        - Joins to member profile for user attributes
        - Calculates session metrics using 30-minute window

      D_Event:
        - Maps raw event names to business-friendly names
        - Maintains event hierarchy (Category1, Category2, Action)
        - Updates daily with new event patterns

      D_Module_Config:
        - Stores current module configuration settings
        - Links menu items to functional areas
        - Tracks module activation status
        - Updated through configuration management process

      D_Group:
        - Contains active and historical group information
        - Maintains group hierarchy and relationships
        - SCD Type 2 for tracking historical changes
        - Daily refresh from operational systems

      V_Billing_Partner:
        - View combining current billing partner attributes
        - Filters for active partners only
        - Includes partner type classification
        - Updated real-time from source systems

      D_LOB:
        - Standard line of business dimension
        - Contains active LOB codes and descriptions
        - Updated through master data management process
        - SCD Type 2 for historical tracking

      D_TPA:
        - Third Party Administrator dimension
        - Contains active TPAs and their attributes
        - Updated through configuration management
        - Includes TPA service level indicators

      D_Event_Region:
        - Geographic dimension for event locations
        - Standardizes city and state names
        - Includes region classification
        - Updated daily from event data

      D_Time_Of_Day:
        - Time-based dimension for event analysis
        - 24-hour and 12-hour time formats
        - Includes time period classifications
        - Static dimension, rarely updated

      D_Access_Mode:
        - Tracks system access methods
        - Contains device and browser information
        - Updated daily from event data
        - Includes access type classification

    config:
      materialized: incremental
      unique_key: ['Event_Id']

    transformations:
      - name: dimension_joins
        description: |
          Joins event facts with dimension tables:
          - Links events to group information
          - Adds billing partner details
          - Includes LOB (Line of Business) attributes
          - Incorporates TPA information
          - Adds geographical context
          - Includes time-based dimensions
          - Adds event categorization
          - Includes module configuration details
          - Adds access mode information
        joins:
          - join: D_Group
            type: inner
            relationship: many_to_one
            sql: "fem.Group_SK = dg.Group_SK"
          
          - join: V_Billing_Partner
            type: inner
            relationship: many_to_one
            sql: "fem.Billing_Partner_SK = bp.Billing_Partner_SK"
          
          - join: D_LOB
            type: inner
            relationship: many_to_one
            sql: "fem.LOB_SK = dl.LOB_SK"
            
          - join: D_TPA
            type: inner
            relationship: many_to_one
            sql: "fem.TPA_SK = dt.TPA_SK"
            
          - join: D_Event_Region
            type: inner
            relationship: many_to_one
            sql: "fem.Event_Region_SK = dr.Event_Region_SK"
            
          - join: D_Time_Of_Day
            type: inner
            relationship: many_to_one
            sql: "fem.Time_Of_Day_SK = tod.Time_Of_Day_SK"
            
          - join: D_Event
            type: inner
            relationship: many_to_one
            sql: "fem.Event_SK = de.Event_SK"
            
          - join: D_Module_Config
            type: inner
            relationship: many_to_one
            sql: "fem.Module_Config_SK = dmc.Module_Config_SK"
            
          - join: D_Access_Mode
            type: inner
            relationship: many_to_one
            sql: "fem.Access_Mode_SK = dam.Access_Mode_SK"

    columns:
      # Keys
      - name: Event_Id
        description: "Unique identifier for the event"
        data_type: string
        source_column: Event_Id
        source_table: F_Event_Metrics
        
      - name: As_Of_Date
        description: "Batch processing date"
        data_type: date
        source_column: As_Of_Date
        source_table: F_Event_Metrics

      # Event Attributes
      - name: Event_Time
        description: "Timestamp when the event occurred"
        data_type: timestamp
        source_column: Event_Time
        source_table: F_Event_Metrics

      - name: Session_Id
        description: "Unique identifier for the user session"
        data_type: string
        source_column: Session_Id
        source_table: F_Event_Metrics

      - name: Session_Length_Mins
        description: "Duration of the session in minutes"
        data_type: integer
        source_column: Session_Length_Mins
        source_table: F_Event_Metrics
        
      - name: Termination_Status
        description: "Indicates if the member is terminated"
        data_type: string
        source_column: Termination_Status
        source_table: F_Event_Metrics

      - name: Days_Since_Registered
        description: "Number of days since user registration"
        data_type: integer
        source_column: Days_Since_Registered
        source_table: F_Event_Metrics
        
      - name: Days_Since_Terminated
        description: "Number of days since user termination"
        data_type: integer
        source_column: Days_Since_Terminated
        source_table: F_Event_Metrics
        
      - name: Group_Number
        description: "Group identifier number"
        data_type: string
        source_column: Group_Number
        source_table: F_Event_Metrics
        
      - name: User_Id
        description: "Unique identifier for the user"
        data_type: string
        source_column: User_Id
        source_table: F_Event_Metrics
        
      - name: Member_Type
        description: "Type of member (subscriber, dependent)"
        data_type: string
        source_column: Member_Type
        source_table: F_Event_Metrics

      # Event Details
      - name: Event_Name
        description: "Name of the event"
        data_type: string
        source_column: Event_Name
        source_table: D_Event
        
      - name: Event_Category_1
        description: "Primary category of the event"
        data_type: string
        source_column: Event_Category_1
        source_table: D_Event
        
      - name: Event_Category_2
        description: "Secondary category of the event"
        data_type: string
        source_column: Event_Category_2
        source_table: D_Event
        
      - name: Event_Action
        description: "Action performed in the event"
        data_type: string
        source_column: Event_Action
        source_table: D_Event
        
      - name: Event_Action_Qualifier
        description: "Additional qualifier for the event action"
        data_type: string
        source_column: Event_Action_Qualifier
        source_table: D_Event
        
      # Module Configuration
      - name: Module_Config_Menu_Name
        description: "Menu name in the module configuration"
        data_type: string
        source_column: Module_Config_Menu_Name
        source_table: D_Module_Config
        
      - name: Module_Config_Settings
        description: "Configuration settings for the module"
        data_type: string
        source_column: Module_Config_Settings
        source_table: D_Module_Config
        
      - name: Module_Name
        description: "Name of the module"
        data_type: string
        source_column: Module_Name
        source_table: D_Module_Config
        
      - name: Category_Name
        description: "Category name of the module"
        data_type: string
        source_column: Category_Name
        source_table: D_Module_Config
        
      # Raw Event Data
      - name: Raw_Name
        description: "Raw event name from source system"
        data_type: string
        source_column: Raw_Name
        source_table: F_Event_Metrics
        
      - name: Raw_Title
        description: "Raw event title from source system"
        data_type: string
        source_column: Raw_Title
        source_table: F_Event_Metrics

      # Dimensional Attributes
      - name: Group_Name
        description: "Name of the associated group"
        data_type: string
        source_column: Group_Name
        source_table: D_Group

      - name: Billing_Partner_Name
        description: "Name of the billing partner"
        data_type: string
        source_column: Billing_Partner_Name
        source_table: V_Billing_Partner
        
      - name: Billing_Partner_Active
        description: "Indicates if the billing partner is active"
        data_type: boolean
        source_column: Billing_Partner_Active
        source_table: V_Billing_Partner
        
      - name: Lob_Cd
        description: "Line of business code"
        data_type: string
        source_column: Lob_Cd
        source_table: D_LOB

      - name: Lob_Name
        description: "Line of business name"
        data_type: string
        source_column: Lob_Name
        source_table: D_LOB
        
      - name: TPA_Name
        description: "Third Party Administrator name"
        data_type: string
        source_column: TPA_Name
        source_table: D_TPA
        
      - name: Event_City_Name
        description: "City where the event occurred"
        data_type: string
        source_column: Event_City_Name
        source_table: D_Event_Region
        
      - name: Event_State_Name
        description: "State where the event occurred"
        data_type: string
        source_column: Event_State_Name
        source_table: D_Event_Region
        
      # Time Dimensions
      - name: Time
        description: "Time of day when the event occurred"
        data_type: string
        source_column: Time
        source_table: D_Time_Of_Day
        
      - name: Hour_Interval
        description: "Hour interval when the event occurred"
        data_type: string
        source_column: Hour_Interval
        source_table: D_Time_Of_Day

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        data_type: timestamp
        transformation_logic: "current_datetime()"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        data_type: timestamp
        transformation_logic: "current_datetime()"
        
      - name: Created_By
        description: "User who created the record"
        data_type: string
        transformation_logic: "'ETL Process'"
        
      - name: Updated_By
        description: "User who last updated the record"
        data_type: string
        transformation_logic: "'ETL Process'"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2024-03-19"
      depends_on:
        - "F_Event_Metrics"
        - "D_Group"
        - "V_Billing_Partner"
        - "D_LOB"
        - "D_TPA"
        - "D_Event_Region"
        - "D_Time_Of_Day"
        - "D_Event"
        - "D_Module_Config"
        - "D_Access_Mode"
      table_type: "table"
      temporal_type: "current"
      refresh_frequency: "daily"
