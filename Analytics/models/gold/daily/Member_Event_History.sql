{{ 
    config(
        materialized='incremental',
        unique_key=['Event_Id'],
        pre_hook="""
                DECLARE table_exists BOOL;

                -- Check if the table 'Member_Event_History' exists in the dataset
                SET table_exists = (
                    SELECT COUNT(1) > 0
                    FROM `{{ env_var('PROJECT_ID') }}.Gold.INFORMATION_SCHEMA.TABLES`
                    WHERE table_name = 'Member_Event_History'
                );

                -- If the table exists, perform the DELETE operation
                IF table_exists THEN
                    BEGIN
                        -- Retrieve the batch date
                        DECLARE batch_date DATE;

                        SET batch_date = (
                            SELECT CAST(batch_control_dt AS DATE)
                            FROM `{{ env_var('PROJECT_ID') }}.Warehouse.T_Batch_Control`
                            WHERE batch_id = 'MxDataMartDailyBuild'
                        );

                        -- Delete data for both the batch date and the previous day
                        DELETE FROM `{{ env_var('PROJECT_ID') }}.Gold.Member_Event_History`
                        WHERE DATE(Event_Time) = batch_date 
                           OR DATE(Event_Time) = DATE_SUB(batch_date, INTERVAL 1 DAY);
                    END;
                END IF;
                """
    )
}}

WITH batch_date AS (
    SELECT CAST(batch_control_dt AS DATE) AS batch_date
    FROM `{{ env_var("PROJECT_ID") }}.Warehouse.T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

SELECT 
    
    fem.As_Of_Date,
    fem.Event_Id,
    fem.Event_Time,
    fem.Session_Id,
    fem.Session_Length_Mins,
    fem.Termination_Status,
    fem.Days_Since_Registered,
    fem.Days_Since_Terminated,
    fem.Group_Number,
    fem.User_Id,
    fem.Member_Type,
    de.Event_Name,
    de.Event_Category_1,
    de.Event_Category_2,
    de.Event_Action,
    de.Event_Action_Qualifier,

    dmc.Module_Config_Menu_Name,
    dmc.Module_Config_Settings,
    dmc.Module_Name,
    dmc.Category_Name,

    fem.Raw_Name,
    fem.Raw_Title,
    -- Group dimensions
    dg.Group_Name,
    
    -- Billing Partner dimensions
    TRIM(bp.Billing_Partner_Name) AS Billing_Partner_Name,
    bp.Billing_Partner_Active,
    
    -- LOB dimensions
    dl.Lob_Cd,
    dl.Lob_Name,
    
    -- TPA dimensions
    dt.TPA_Name,
    
    -- Region dimensions
    dr.Event_City_Name,
    dr.Event_State_Name,
    
    -- Time of Day dimensions
    dtod.Time,
    dtod.Hour_Interval,
    
    current_datetime() AS Created_dt_ts,
    current_datetime() AS Updated_dt_ts,
    CAST(NULL AS STRING) AS Created_By,
    CAST(NULL AS STRING) AS Updated_By

FROM {{ ref('F_Event_Metrics') }} fem

 JOIN {{ ref('D_Group') }} dg
    ON fem.Group_SK = dg.Group_SK

 JOIN {{ ref('V_Billing_Partner') }} bp
    ON fem.Billing_Partner_SK = bp.Billing_Partner_SK

 JOIN {{ ref('D_LOB') }} dl
    ON fem.LOB_SK = dl.LOB_SK

 JOIN {{ ref('D_TPA') }} dt
    ON fem.TPA_SK = dt.TPA_SK

 JOIN {{ ref('D_Event_Region') }} dr
    ON fem.Region_SK = dr.Region_SK

 JOIN {{ ref('D_Event') }} de
    ON fem.Event_SK = de.Event_SK

 JOIN {{ ref('D_Time_Of_Day') }} dtod
    ON fem.Time_Of_Day_SK = dtod.Time_Of_Day_SK

 JOIN {{ ref('D_Module_Config') }} dmc
    ON fem.Module_Config_SK = dmc.Module_Config_SK

 JOIN {{ ref('D_Access_Mode') }} dam
    ON fem.Access_Mode_SK = dam.Access_Mode_SK

WHERE (DATE(fem.Event_Time) = (SELECT batch_date.batch_date FROM batch_date)
OR DATE(fem.Event_Time) = DATE_SUB((SELECT batch_date.batch_date FROM batch_date), INTERVAL 1 DAY))
AND de.Event_Category_1 <> 'Unknown'

