{{ 
    config(
        materialized = 'view'
    )
}}

WITH distinct_ssn_subs AS (
    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Subscriber_SSN <> '999999999'
    AND SUBSCRIBER_CNT = 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Subscriber_SSN, Group_Id, Billing_Partner_Id ORDER BY Member_Number DESC) = 1

    UNION ALL

    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Subscriber_SSN = '999999999'
    AND SUBSCRIBER_CNT = 1
),

distinct_ssn_deps AS (
    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Member_SSN <> '999999999'
    AND SUBSCRIBER_CNT <> 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Member_SSN, Group_Id, Billing_Partner_Id ORDER BY Member_Number DESC) = 1

    UNION ALL

    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Member_SSN = '999999999'
    AND SUBSCRIBER_CNT <> 1
),

member_counts AS (
    SELECT 
        subs.Group_Id,
        subs.Billing_Partner_Id,
        subs.Lob_Cd,
        COUNT(DISTINCT CONCAT(subs.Member_Number,'|',subs.Person_Number,'|',subs.Elig_Group_Number)) as Total_Elig_Subscriber_Cnt,
        COUNT(DISTINCT CONCAT(deps.Member_Number,'|',deps.Person_Number,'|',deps.Elig_Group_Number)) as Total_Elig_Dependent_Cnt
    FROM distinct_ssn_subs subs
    LEFT JOIN distinct_ssn_deps deps
        ON subs.Group_Id = deps.Group_Id
        AND subs.Billing_Partner_Id = deps.Billing_Partner_Id
        AND subs.Lob_Cd = deps.Lob_Cd
    GROUP BY 
        subs.Group_Id,
        subs.Billing_Partner_Id,
        subs.Lob_Cd
)

SELECT 
    mc.Module_Id,
    mc.Module_Name,
    CASE WHEN mc.Module_Config_Menu_Name = 'Not Applicable' AND Program_Name <> '' THEN Program_Name
    WHEN mc.Module_Config_Menu_Name <> 'Not Applicable' THEN mc.Module_Config_Menu_Name
    END AS Module_Header,
    mc.Module_Link,
    counts.Billing_Partner_Id,
    counts.Lob_Cd,
    COUNT(DISTINCT mc.Group_Id) as Group_Count,
    SUM(COALESCE(counts.Total_Elig_Subscriber_Cnt, 0)) as Total_Elig_Subscriber_Cnt,
    SUM(COALESCE(counts.Total_Elig_Dependent_Cnt, 0)) as Total_Elig_Dependent_Cnt
FROM {{ ref('V_Module_Config_Extended_DW') }} mc
LEFT JOIN member_counts counts
    ON mc.Group_Id = counts.Group_Id
GROUP BY 
    mc.Module_Id,
    mc.Module_Name,
    CASE WHEN mc.Module_Config_Menu_Name = 'Not Applicable' AND Program_Name <> '' THEN Program_Name
    WHEN mc.Module_Config_Menu_Name <> 'Not Applicable' THEN mc.Module_Config_Menu_Name
    END    
    ,mc.Module_Link,
    counts.Billing_Partner_Id,
    counts.Lob_Cd
