{{ 
    config(
        materialized='incremental',
        unique_key=['Group_Number', 'Group_Name', 'Member_Number', 'Member_SSN', 'Subscriber_SSN', 'First_Name', 'Last_Name', 'Is_Primary_Subscriber', 'Enr_Seq_Number','Mem_Dep_Code','Directory_Id','Plan_Id','Plan_Name','Plan_Type','Plan_Product_Name','Login_Email','Registration_Date','Last_Logged_In','Member_Impersonating_Email', 'As_Of_Date'],
        pre_hook="""
                DECLARE table_exists BOOL;

                -- Check if the table 'Allstate_Registrations' exists in the dataset
                SET table_exists = (
                    SELECT COUNT(1) > 0
                    FROM `{{ env_var('PROJECT_ID') }}.Gold.INFORMATION_SCHEMA.TABLES`
                    WHERE table_name = 'Allstate_Registrations'
                );

                -- If the table exists, perform the DELETE operation
                IF table_exists THEN
                    BEGIN
                        -- Retrieve the batch date
                        DECLARE batch_date DATE;

                        SET batch_date = (
                            SELECT CAST(batch_control_dt AS DATE)
                            FROM `{{ env_var('PROJECT_ID') }}.Warehouse.T_Batch_Control`
                            WHERE batch_id = 'MxDataMartDailyBuild'
                        );

                        DELETE FROM `{{ env_var('PROJECT_ID') }}.Gold.Allstate_Registrations`
                        WHERE As_Of_Date = batch_date;
                    END;
                END IF;

                """
    )
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

,base AS (

    SELECT

    pl.Plan_Group_Number AS Group_Number
    ,g.Group_Name
    ,base.Member_Number
    ,base.Member_SSN
    ,base.Subscriber_SSN
    ,base.First_Name
    ,base.Last_Name
    ,CASE WHEN Relationship_Cd = 1 THEN 'TRUE'
    WHEN Relationship_Cd = 2 THEN 'FALSE'
    END AS Is_Primary_Subscriber
    ,base.Enr_Seq_Number
    ,base.Mem_Dep_Code
    ,base.LOB_Cd AS Directory_Id
    ,lob.LOB_Name AS Directory_Name
    ,pl.Plan_Id
    ,pl.Plan_Name
    ,pl.Plan_Type
    ,pl.Plan_Product_Name
    ,base.User_Name AS Login_Email
    ,base.Registration_Date
    ,base.Latest_Logged_In_ts AS Last_Logged_In
    ,'FALSE' AS Member_Impersonating_Email
    ,CURRENT_DATETIME AS Created_dt_ts
    ,DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY) AS As_Of_Date

    FROM

    {{ ref('V_All_Member_Attributes_L2')}} base
    LEFT JOIN {{ ref('V_Plan_DW') }} pl
    ON base.Plan_Id = pl.Plan_Id

    LEFT JOIN {{ ref('V_Group_DW') }} g
    ON base.Group_Id = g.Group_Id

    LEFT JOIN {{ ref('V_LOB_DW') }} lob
    ON base.LOB_Cd = lob.LOB_Cd

    WHERE base.User_Name NOT LIKE '%+%'
    AND REGISTERED_ELIG = 1
    AND pl.Plan_Group_Number IS NOT NULL
    AND base.LOB_Cd IN ('7','15')
)

SELECT 

*

FROM

base

QUALIFY ROW_NUMBER() OVER (PARTITION BY Login_Email ORDER BY Plan_Type ASC) = 1
