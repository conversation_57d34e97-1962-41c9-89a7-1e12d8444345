version: 2

models:
  - name: Allstate_Registrations
    description: |
      Purpose:
      This table provides a daily snapshot of Allstate member registrations, capturing key member
      information, plan details, and registration status for reporting purposes.

      Data Grain:
      One record per registered member per as_of_date, with the earliest plan type when a member
      has multiple plans.

      Direct Sources:
      - [V_All_Member_Attributes_L2](/#!/model/model.Analytics.V_All_Member_Attributes_L2) (base member attributes with registration status)
      - [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW) (plan details)
      - [V_Group_DW](/#!/model/model.Analytics.V_Group_DW) (group information)
      - [V_LOB_DW](/#!/model/model.Analytics.V_LOB_DW) (line of business details)

      Indirect Sources:
      - [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2) (via V_All_Member_Attributes_L2)
      - [V_Policy_User_DW](/#!/model/model.Analytics.V_Policy_User_DW) (via V_All_Member_Attributes_L2)
      - [V_User_DW](/#!/model/model.Analytics.V_User_DW) (via V_All_Member_Attributes_L2)
      - [V_Latest_LoggedIn_By_User_DW](/#!/model/model.Analytics.V_Latest_LoggedIn_By_User_DW) (via V_All_Member_Attributes_L2)
      - mx_insurance_Plan (via V_Plan_DW)
      - mx_insurance_Group (via V_Group_DW)
      - mx_fileprocessing_Eligibility (via V_LOB_DW)

      Key Business Rules:
      - Filters for registered members only (REGISTERED_ELIG = 1)
      - Excludes members with '+' in their username (User_Name NOT LIKE '%+%')
      - Includes only specific LOB codes (7 and 15)
      - Requires non-null Plan_Group_Number
      - Deduplicates members with multiple plans by selecting the earliest Plan_Type

    config:
      materialized: incremental
      tags: ["gold", "daily", "allstate"]

    transformations:
      - name: batch_date_extraction
        description: |
          Retrieves the current processing date:
          - Gets batch control date for MxDataMartDailyBuild
          - Casts to DATE type for consistent date handling
          - Uses this date for as_of_date and filtering

      - name: base_data_preparation
        description: |
          Joins member data with dimension tables:
          - Starts with V_All_Member_Attributes_L2 as the base
          - Joins to V_Plan_DW for plan details
          - Joins to V_Group_DW for group information
          - Joins to V_LOB_DW for line of business details
        joins:
          - join: V_Plan_DW
            type: left
            relationship: many_to_one
            sql: "base.Plan_Id = pl.Plan_Id"

          - join: V_Group_DW
            type: left
            relationship: many_to_one
            sql: "base.Group_Id = g.Group_Id"

          - join: V_LOB_DW
            type: left
            relationship: many_to_one
            sql: "base.LOB_Cd = lob.LOB_Cd"

      - name: filtering_and_deduplication
        description: |
          Applies business rules and deduplication:
          - Filters for registered members (REGISTERED_ELIG = 1)
          - Excludes usernames with '+' character
          - Includes only LOB codes 7 and 15
          - Requires non-null Plan_Group_Number
          - Deduplicates by Login_Email, keeping the record with earliest Plan_Type

    columns:
      # Group and Plan Information
      - name: Group_Number
        description: "Group number from the insurance plan"
        type: string
        source_column: "Plan_Group_Number"
        source_table: "V_Plan_DW"

      - name: Group_Name
        description: "Name of the insurance group"
        type: string
        source_column: "Group_Name"
        source_table: "V_Group_DW"

      - name: Plan_Id
        description: "Unique identifier for the insurance plan"
        type: string
        source_column: "Plan_Id"
        source_table: "V_Plan_DW"

      - name: Plan_Name
        description: "Name of the insurance plan"
        type: string
        source_column: "Plan_Name"
        source_table: "V_Plan_DW"

      - name: Plan_Type
        description: "Type of insurance plan"
        type: string
        source_column: "Plan_Type"
        source_table: "V_Plan_DW"

      - name: Plan_Product_Name
        description: "Product name of the insurance plan"
        type: string
        source_column: "Plan_Product_Name"
        source_table: "V_Plan_DW"

      # Member Information
      - name: Member_Number
        description: "Unique identifier for the member"
        type: string
        source_column: "Member_Number"
        source_table: "V_All_Member_Attributes_L2"

      - name: Member_SSN
        description: "Social Security Number of the member"
        type: string
        source_column: "Member_SSN"
        source_table: "V_All_Member_Attributes_L2"

      - name: Subscriber_SSN
        description: "Social Security Number of the subscriber"
        type: string
        source_column: "Subscriber_SSN"
        source_table: "V_All_Member_Attributes_L2"

      - name: First_Name
        description: "First name of the member"
        type: string
        source_column: "First_Name"
        source_table: "V_All_Member_Attributes_L2"

      - name: Last_Name
        description: "Last name of the member"
        type: string
        source_column: "Last_Name"
        source_table: "V_All_Member_Attributes_L2"

      - name: Is_Primary_Subscriber
        description: "Indicates if the member is the primary subscriber"
        type: string
        source_column: "Relationship_Cd"
        source_table: "V_All_Member_Attributes_L2"
        transformation_logic: "CASE WHEN Relationship_Cd = 1 THEN 'TRUE' WHEN Relationship_Cd = 2 THEN 'FALSE' END"

      - name: Enr_Seq_Number
        description: "Enrollment sequence number"
        type: string
        source_column: "Enr_Seq_Number"
        source_table: "V_All_Member_Attributes_L2"

      - name: Mem_Dep_Code
        description: "Member dependent code"
        type: string
        source_column: "Mem_Dep_Code"
        source_table: "V_All_Member_Attributes_L2"

      # Directory Information
      - name: Directory_Id
        description: "Directory identifier (LOB code)"
        type: string
        source_column: "LOB_Cd"
        source_table: "V_All_Member_Attributes_L2"

      - name: Directory_Name
        description: "Name of the directory (LOB name)"
        type: string
        source_column: "LOB_Name"
        source_table: "V_LOB_DW"

      # Registration Information
      - name: Login_Email
        description: "Email address used for login"
        type: string
        source_column: "User_Name"
        source_table: "V_All_Member_Attributes_L2"

      - name: Registration_Date
        description: "Date when the member registered"
        type: timestamp
        source_column: "Registration_Date"
        source_table: "V_All_Member_Attributes_L2"

      - name: Last_Logged_In
        description: "Timestamp of the member's last login"
        type: timestamp
        source_column: "Latest_Logged_In_ts"
        source_table: "V_All_Member_Attributes_L2"

      - name: Member_Impersonating_Email
        description: "Flag indicating if member is being impersonated"
        type: string
        transformation_logic: "'FALSE'"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: As_Of_Date
        description: "Reference date for the data snapshot"
        type: date
        source_column: "batch_date"
        source_table: "batch_date CTE"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_All_Member_Attributes_L2" # [V_All_Member_Attributes_L2](/#!/model/model.Analytics.V_All_Member_Attributes_L2)
        - "V_Plan_DW" # [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW)
        - "V_Group_DW" # [V_Group_DW](/#!/model/model.Analytics.V_Group_DW)
        - "V_LOB_DW" # [V_LOB_DW](/#!/model/model.Analytics.V_LOB_DW)
      table_type: "table"
      temporal_type: "current"
      refresh_frequency: "daily"
