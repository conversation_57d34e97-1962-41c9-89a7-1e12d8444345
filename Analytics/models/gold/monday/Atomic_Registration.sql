{{ 
    config(
        materialized='incremental',
        unique_key=['Group_Number', 'Group_Name', 'Partner_Name', 'Member_Number', 'Member_SSN', 'Subscriber_SSN', 'First_Name', 'Last_Name', 'Is_Primary_Subscriber', 'Enr_Seq_Number','Mem_Dep_Code','Directory_Id','Login_Email','Registration_Date','Last_Logged_In', 'As_Of_Date'],
        pre_hook="""
                DECLARE table_exists BOOL;

                -- Check if the table 'Atomic_Registration' exists in the dataset
                SET table_exists = (
                    SELECT COUNT(1) > 0
                    FROM `{{ env_var('PROJECT_ID') }}.Gold.INFORMATION_SCHEMA.TABLES`
                    WHERE table_name = 'Atomic_Registration'
                );

                -- If the table exists, perform the DELETE operation
                IF table_exists THEN
                    BEGIN
                        -- Retrieve the batch date
                        DECLARE batch_date DATE;

                        SET batch_date = (
                            SELECT CAST(batch_control_dt AS DATE)
                            FROM `{{ env_var('PROJECT_ID') }}.Warehouse.T_Batch_Control`
                            WHERE batch_id = 'MxDataMartDailyBuild'
                        );

                        DELETE FROM `{{ env_var('PROJECT_ID') }}.Gold.Atomic_Registration`
                        WHERE As_Of_Date = batch_date;
                    END;
                END IF;

                """
    )
}}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)


SELECT

pl.Plan_Group_Number AS Group_Number
,g.Group_Name
,TRIM(p.Partner_Name) AS Partner_Name
,base.Member_Number
,base.Member_SSN
,base.Subscriber_SSN
,base.First_Name
,base.Last_Name
,base.Relationship_Cd
,CASE 
    WHEN Relationship_Cd = 1 
        THEN True
        ELSE False
END AS Is_Primary_Subscriber
,base.Enr_Seq_Number
,base.Mem_Dep_Code
,base.LOB_Cd AS Directory_Id
,base.User_Name AS Login_Email
,base.Registration_Date
,base.Latest_Logged_In_ts AS Last_Logged_In
,CURRENT_DATETIME AS Created_dt_ts
,(SELECT batch_date.batch_date FROM batch_date) AS As_Of_Date

FROM

{{ ref('V_All_Member_Attributes_L2_By_Elig_User')}} base
LEFT JOIN {{ ref('V_Plan_DW') }} pl
ON base.Plan_Id = pl.Plan_Id

LEFT JOIN {{ ref('V_Group_DW') }} g
ON base.Group_Id = g.Group_Id

LEFT JOIN {{ ref('V_Partner_DW') }} p
ON base.Billing_Partner_Id = p.Partner_Id

WHERE base.User_Name NOT LIKE '%+%'
AND REGISTERED_ELIG = 1
AND pl.Plan_Group_Number IS NOT NULL