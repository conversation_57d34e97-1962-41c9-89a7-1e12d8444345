version: 2

models:
  - name: Atomic_Registration
    description: |
      Purpose:
      This table provides a snapshot of all registered members reporting,
      capturing key member information, plan details, and registration status. It serves as a gold-layer
      reporting table for analyzing member registrations across different groups and directories.
      
      Data Grain:
      One record per registered member per as_of_date, with registration details and plan information.
      
      Direct Sources:
      - [V_All_Member_Attributes_L2_By_Elig_User](/#!/model/model.Analytics.V_All_Member_Attributes_L2_By_Elig_User) (base member attributes with registration status)
      - [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW) (plan details)
      - [V_Group_DW](/#!/model/model.Analytics.V_Group_DW) (group information)
      - [V_Partner_DW](/#!/model/model.Analytics.V_Partner_DW) (partner information)
      
      Indirect Sources:
      - [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2) (via V_All_Member_Attributes_L2_By_Elig_User)
      - [V_Policy_User_DW](/#!/model/model.Analytics.V_Policy_User_DW) (via V_All_Member_Attributes_L2_By_Elig_User)
      - [V_User_DW](/#!/model/model.Analytics.V_User_DW) (via V_All_Member_Attributes_L2_By_Elig_User)
      - [V_Latest_LoggedIn_By_User_DW](/#!/model/model.Analytics.V_Latest_LoggedIn_By_User_DW) (via V_All_Member_Attributes_L2_By_Elig_User)
      - mx_insurance_Plan (via V_Plan_DW)
      - mx_insurance_Group (via V_Group_DW)
      - mx_partnerorganization_Partner (via V_Partner_DW)
      
      Key Business Rules:
      - Filters for registered members only (REGISTERED_ELIG = 1)
      - Excludes members with '+' in their username (User_Name NOT LIKE '%+%')
      - Requires non-null Plan_Group_Number
      - Presist all data

    config:
      materialized: incremental
      tags: ["gold", "monday", "registration"]

    transformations:
      - name: batch_date_extraction
        description: |
          Retrieves the current processing date:
          - Gets batch control date for MxDataMartDailyBuild
          - Casts to DATE type for consistent date handling
          - Uses this date for as_of_date and filtering

      - name: base_data_preparation
        description: |
          Joins member data with dimension tables:
          - Starts with V_All_Member_Attributes_L2_By_Elig_User as the base
          - Joins to V_Plan_DW for plan details
          - Joins to V_Group_DW for group information
        joins:
          - join: V_Plan_DW
            type: left
            relationship: many_to_one
            sql: "base.Plan_Id = pl.Plan_Id"
          
          - join: V_Group_DW
            type: left
            relationship: many_to_one
            sql: "base.Group_Id = g.Group_Id"
          
          - join: V_Partner_DW
            type: left
            relationship: many_to_one
            sql: "base.Billing_Partner_Id = p.Partner_Id"

      - name: filtering_and_transformation
        description: |
          Applies business rules and transformations:
          - Filters for registered members (REGISTERED_ELIG = 1)
          - Excludes usernames with '+' character
          - Requires non-null Plan_Group_Number
          - Transforms Relationship_Cd to Is_Primary_Subscriber flag

    columns:
      # Group and Plan Information
      - name: Group_Number
        description: "Group number from the insurance plan"
        type: string
        source_column: "Plan_Group_Number"
        source_table: "V_Plan_DW"

      - name: Group_Name
        description: "Name of the insurance group"
        type: string
        source_column: "Group_Name"
        source_table: "V_Group_DW"

      - name: Partner_Name
        description: "Name of the billing partner"
        type: string
        source_column: "Partner_Name"
        source_table: "V_Partner_DW"

      # Member Information
      - name: Member_Number
        description: "Unique identifier for the member"
        type: string
        source_column: "Member_Number"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      - name: Member_SSN
        description: "Social Security Number of the member"
        type: string
        source_column: "Member_SSN"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      - name: Subscriber_SSN
        description: "Social Security Number of the subscriber"
        type: string
        source_column: "Subscriber_SSN"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      - name: First_Name
        description: "First name of the member"
        type: string
        source_column: "First_Name"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      - name: Last_Name
        description: "Last name of the member"
        type: string
        source_column: "Last_Name"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      - name: Is_Primary_Subscriber
        description: "Indicates if the member is the primary subscriber (TRUE) or a dependent (FALSE)"
        type: boolean
        source_column: "Relationship_Cd"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"
        transformation_logic: "CASE WHEN Relationship_Cd = 1 THEN 'TRUE' WHEN Relationship_Cd = 2 THEN 'FALSE' END"

      - name: Enr_Seq_Number
        description: "Enrollment sequence number"
        type: string
        source_column: "Enr_Seq_Number"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      - name: Mem_Dep_Code
        description: "Member dependent code"
        type: string
        source_column: "Mem_Dep_Code"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      # Directory Information
      - name: Directory_Id
        description: "Directory identifier (LOB code)"
        type: string
        source_column: "LOB_Cd"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      # Registration Information
      - name: Login_Email
        description: "Email address used for login"
        type: string
        source_column: "User_Name"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      - name: Registration_Date
        description: "Date when the member registered"
        type: timestamp
        source_column: "Registration_Date"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      - name: Last_Logged_In
        description: "Timestamp of the member's last login"
        type: timestamp
        source_column: "Latest_Logged_In_ts"
        source_table: "V_All_Member_Attributes_L2_By_Elig_User"

      - name: Member_Impersonating_Email
        description: "Flag indicating if member is being impersonated"
        type: string
        transformation_logic: "'FALSE'"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: As_Of_Date
        description: "Reference date for the data snapshot"
        type: date
        source_column: "batch_date"
        source_table: "batch_date CTE"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2025-04-30"
      depends_on:
        - "V_All_Member_Attributes_L2_By_Elig_User" # [V_All_Member_Attributes_L2_By_Elig_User](/#!/model/model.Analytics.V_All_Member_Attributes_L2_By_Elig_User)
        - "V_Plan_DW" # [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW)
        - "V_Group_DW" # [V_Group_DW](/#!/model/model.Analytics.V_Group_DW)
        - "V_Partner_DW" # [V_Partner_DW](/#!/model/model.Analytics.V_Partner_DW)
      table_type: "table"
      temporal_type: "current"
      refresh_frequency: "weekly"
