{{ config(materialized = 'view') }}

WITH BillingPartner AS (
    SELECT 
    `Name` as BillingPartnerName,
    Id as BillingPartnerId
    FROM {{ source('ODS', 'mx_partnerorganization_Partner') }}
    WHERE `Name` LIKE '[Billing]%'
),
BillingPartner_Group AS (
    SELECT 
        billing_partner.BillingPartnerName as BillingPartnerName,
        grp.`Name` as GroupName,
        grp.Id as GroupId,

    FROM BillingPartner billing_partner
    INNER JOIN {{ source('ODS', 'mx_partnerorganization_PartnerGroup') }} partner_group
        ON billing_partner.BillingPartnerId = partner_group.PartnerId
    INNER JOIN {{ source('ODS', 'mx_insurance_Group') }} grp 
        ON partner_group.GroupId = grp.Id
),
Group_Plan as (
    SELECT 
    grp.BillingPartnerName as BillingPartnerName,
    grp.GroupName as GroupName,
    plan.`Name` as PlanName,
    plan.Id as PlanId,
    CASE
        WHEN plan.Type = 1 THEN 'Medical'
        WHEN plan.Type = 2 THEN 'Dental'
        WHEN plan.Type = 3 THEN 'Vision'
        ELSE 'Other'
    END
     as InsuranceType,
    grp.GroupId as GroupId

    FROM BillingPartner_Group grp
    INNER JOIN {{ source('ODS', 'mx_insurance_Plan') }} plan on grp.GroupId = plan.GroupId
),
Group_Plan_Policy_User as (
    SELECT
    group_plan.BillingPartnerName as BillingPartnerName,
    group_plan.GroupName as GroupName,
    group_plan.PlanName as PlanName,
    group_plan.PlanId as PlanId,
    group_plan.InsuranceType as InsuranceType,
    group_plan.GroupId as GroupId,
    policy.EligibilityId as EligibilityId,
    user.UserName as UserName

    FROM Group_Plan group_plan
    INNER JOIN {{ source('ODS', 'mx_insurance_Policy') }} policy 
        ON policy.PlanId = group_plan.PlanId and policy.UserId IS NOT NULL 
    INNER JOIN {{ source('ODS', 'mx_authentication_User') }} user 
        ON user.Id = policy.UserId and user.Active = 1
),
Group_Plan_Policy_User_Eligibility as (
    SELECT 
    group_plan_policy_user.BillingPartnerName as BillingPartnerName,
    group_plan_policy_user.GroupName as GroupName,
    group_plan_policy_user.PlanName as PlanName,
    group_plan_policy_user.PlanId as PlanId,
    group_plan_policy_user.InsuranceType as InsuranceType,
    group_plan_policy_user.GroupId as GroupId,
    group_plan_policy_user.EligibilityId as EligibilityId,
    group_plan_policy_user.UserName as UserName,
    eligibility.FirstName as FirstName,
    eligibility.LastName as LastName,
    eligibility.MemberNumber as MemberNumber,
    eligibility.SSN as SSN,
    eligibility.Relationship as Relationship,
    eligibility.EffectiveDate as EffectiveDate,
    eligibility.TerminationDate as TerminationDate,
    eligibility.CustomAttributes as CustomAttributes

    FROM Group_Plan_Policy_User group_plan_policy_user
    INNER JOIN {{ source('ODS', 'mx_fileprocessing_Eligibility') }} eligibility
        on group_plan_policy_user.EligibilityId = eligibility.Id
)
SELECT * FROM Group_Plan_Policy_User_Eligibility
ORDER BY GroupName
