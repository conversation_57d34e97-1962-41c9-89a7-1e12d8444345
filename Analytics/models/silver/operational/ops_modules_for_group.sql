{{ config(materialized = 'view') }}
WITH base AS (
    SELECT 
        module.`Name` as ModuleName,
        module.Category as ModuleCategory,
        module.UIType as UIType,
        module.Vendor as Vendor,
        module.Description as Description,
        moduleconfig.Settings as Mo<PERSON>leSettings,
        grp.`Name` as <PERSON><PERSON><PERSON>,
        partner.`Name` as <PERSON><PERSON><PERSON>,
        CASE
            WHEN module.Id = 7 THEN moduleconfig.Settings
            ELSE '{}'
        END AS ExternalLinkSettings

    FROM {{ source('ODS', 'mx_configuration_Module') }} module
    INNER JOIN {{ source('ODS', 'mx_configuration_ModuleConfig') }} moduleconfig
        ON moduleconfig.`Type` = module.Id and moduleconfig.Active = 1
    INNER JOIN {{ source('ODS', 'mx_insurance_Group') }} grp
        on grp.Id = moduleconfig.GroupId
    INNER JOIN {{ source('ODS', 'mx_partnerorganization_PartnerGroup') }} partner_group
        on partner_group.GroupId = grp.Id
    INNER JOIN {{ source('ODS', 'mx_partnerorganization_Partner') }} partner
        on partner.Id = partner_group.PartnerId 
            AND partner.Name LIKE '[Billing]%'
)
SELECT * FROM base