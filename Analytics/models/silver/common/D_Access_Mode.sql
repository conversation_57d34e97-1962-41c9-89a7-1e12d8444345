{{ config( 
          materialized='incremental'
        , unique_key='Access_Mode_SK'
        , tags = ['exclude_when_incremental']
      ) 
}}

{% if not is_incremental() %}

SELECT         
        Access_Mode_SK
        ,OS_Name
        ,OS_Version
        ,Browser_Name
        ,Browser_Version
        ,Portal_Name
        ,current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By
FROM {{ ref('V_Access_Mode_DW') }}

UNION ALL

SELECT         
        "-1" AS Access_Mode_SK
        ,"Not Available" AS OS_Name
        ,"Not Available" AS OS_Version
        ,"Not Available" AS Browser_Name
        ,"Not Available" AS Browser_Version
        ,"Not Available" AS Portal_Name
        ,current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By

{% else %}

WITH source_data AS (
    SELECT
        Access_Mode_SK
        ,OS_Name
        ,OS_Version
        ,Browser_Name
        ,Browser_Version
        ,Portal_Name
        ,current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By,
        MD5(CONCAT('|',
            OS_Name
            ,OS_Version
            ,Browser_Name
            ,Browser_Version
            ,Portal_Name
        )) AS record_hash
    FROM {{ ref('V_Access_Mode_DW') }}
),
-- Identify existing records in the dimension table
existing_records AS (
    SELECT
        Access_Mode_SK
        ,OS_Name
        ,OS_Version
        ,Browser_Name
        ,Browser_Version
        ,Portal_Name
        ,Created_By,
        Updated_By,
        Created_dt_ts,
        Updated_dt_ts,
        MD5(CONCAT('|',
            OS_Name
            ,OS_Version
            ,Browser_Name
            ,Browser_Version
            ,Portal_Name
        )) AS record_hash
    FROM {{this}}
),
-- Find changed or new records
updated_records AS (
    SELECT
        src.OS_Name
        ,src.OS_Version
        ,src.Browser_Name
        ,src.Browser_Version
        ,src.Portal_Name
        ,src.Access_Mode_SK,
        src.Created_dt_ts,
        src.Created_By,
        CASE
            WHEN ex.Access_Mode_SK IS NULL THEN 'new'
            WHEN src.record_hash != ex.record_hash THEN 'changed'
            ELSE 'unchanged'
        END AS change_status
    FROM source_data src
    LEFT JOIN existing_records ex
    ON src.OS_Name = ex.OS_Name
    AND src.OS_Version = ex.OS_Version
    AND src.Browser_Name = ex.Browser_Name
    AND src.Browser_Version = ex.Browser_Version
    AND src.Portal_Name = ex.Portal_Name
),
-- Filter only new or changed records
new_or_changed_records AS (
    SELECT *
    FROM updated_records
    WHERE change_status IN ('new', 'changed')
)
-- Final dimension table logic
SELECT
    COALESCE(ex.Access_Mode_SK, src.Access_Mode_SK) AS Access_Mode_SK,
    src.OS_Name
    ,src.OS_Version
    ,src.Browser_Name
    ,src.Browser_Version
    ,src.Portal_Name,
    CASE
        WHEN ex.Access_Mode_SK IS NULL THEN CURRENT_DATETIME
        ELSE ex.Created_dt_ts
    END AS Created_dt_ts,
    CASE
        WHEN ex.Access_Mode_SK IS NULL THEN CURRENT_DATETIME
        ELSE CURRENT_DATETIME
    END AS Updated_dt_ts,
    CASE
        WHEN ex.Access_Mode_SK IS NULL THEN CAST(NULL AS STRING)
        ELSE CAST(ex.Created_By as STRING)
    END AS Created_By,
    CAST(NULL AS STRING) AS Updated_By
FROM new_or_changed_records src
LEFT JOIN existing_records ex
ON src.OS_Name = ex.OS_Name
AND src.OS_Version = ex.OS_Version
AND src.Browser_Name = ex.Browser_Name
AND src.Browser_Version = ex.Browser_Version
AND src.Portal_Name = ex.Portal_Name

{% endif %}
