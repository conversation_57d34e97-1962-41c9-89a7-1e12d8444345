version: 2

models:
  - name: D_LOB
    description: |
      Purpose:
      This dimension table maintains Line of Business (LOB) attributes with Type 1 SCD handling,
      providing a standardized reference for LOB categorization across the data warehouse.

      Data Grain:
      One record per unique LOB code, plus one default record.

      Direct Sources:
      - [V_LOB_DW](/#!/model/model.Analytics.V_LOB_DW) (view containing LOB attributes)

      Indirect Sources:
      - [mx_insurance_LineOfBusiness](/#!/model/model.Analytics.mx_insurance_LineOfBusiness) (via V_LOB_DW)

      Key Business Rules:
      - Maintains Type 1 SCD with no historical versions
      - Includes default record with LOB_SK = "-1"
      - Changes detected using MD5 hash of business attributes
      - Only processes new or changed records during incremental loads

    config:
      materialized: incremental
      unique_key: LOB_SK
      tags: ['silver', 'common', 'dimension', 'exclude_when_incremental']

    transformations:
      - name: initial_load
        description: |
          Full refresh process when is_incremental() is false:
          - Extracts all records from V_LOB_DW
          - Adds default "Not Applicable" record
          - Sets initial audit timestamps

      - name: incremental_update
        description: |
          Incremental update process when is_incremental() is true:
          - Calculates MD5 hash of business attributes
          - Identifies new or changed records
          - Preserves original creation timestamps
          - Updates modification timestamps for changed records
        steps:
          - name: hash_calculation
            description: "Creates MD5 hash from LOB_Cd and LOB_Name"

          - name: change_detection
            description: "Compares source and target hashes to identify changes"

          - name: record_update
            description: "Updates existing or inserts new records"

    columns:
      # Key Columns
      - name: LOB_SK
        description: "Surrogate key for the LOB"
        type: string
        source_column: "LOB_SK"
        source_table: "V_LOB_DW"

      # Business Attributes
      - name: LOB_Cd
        description: "Business code for the Line of Business"
        type: string
        source_column: "LOB_Cd"
        source_table: "V_LOB_DW"

      - name: LOB_Name
        description: "Business name for the Line of Business"
        type: string
        source_column: "LOB_Name"
        source_table: "V_LOB_DW"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: >
          CASE
            WHEN ex.LOB_SK IS NULL THEN CURRENT_DATETIME
            ELSE ex.Created_dt_ts
          END

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_LOB_DW" # [V_LOB_DW](/#!/model/model.Analytics.V_LOB_DW)
      table_type: "table"
      temporal_type: "scd_type_1"
      refresh_frequency: "incremental"