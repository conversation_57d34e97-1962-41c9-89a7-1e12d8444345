{{ config(
    materialized='incremental',
    unique_key='Partner_SK'
    , tags = ['exclude_when_incremental']
) }}

{% if not is_incremental() %}

-- Source data extraction
    SELECT
        Partner_SK,
        Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        Partner_Id,
        Partner_Name,
        Partner_Type,
        Partner_Active,
        1 AS Current_Flag,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Partner_DW') }}

    UNION ALL

    SELECT
        "-1" AS Partner_SK,
        DATE('{{ var("past_init_date") }}') AS Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        0 AS Partner_Id,
        "NA" AS Partner_Name,
        "NA" AS Partner_Type,
        1 AS Partner_Active,
        1 AS Current_Flag,
        CURRENT_DATETIME AS Created_dt_ts,
        CURRENT_DATETIME AS Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By

{% else %}

WITH source_data AS (
    SELECT
        Partner_SK,
        Effective_From_Dt,
        Effective_To_Dt,
        Partner_Id,
        Partner_Name,
        Partner_Type,
        Partner_Active,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Partner_DW') }}
),

effective_to_date AS (
    SELECT 
        DATE_SUB(batch_control_dt, INTERVAL 1 DAY) AS Effective_To_Dt
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
),

rows_to_deactivate AS (
    SELECT
        d.Partner_SK AS Partner_SK_to_deactivate
    FROM {{ this }} d
    INNER JOIN source_data s
        ON s.Partner_Id = d.Partner_Id
       AND d.Current_Flag = 1
    WHERE d.Partner_Id IS NOT NULL
      AND d.Partner_SK <> s.Partner_SK
)

-- Update existing rows (set Current_Flag to 0)
SELECT
    d.Partner_SK,
    d.Partner_Id,
    d.Partner_Name,
    d.Partner_Type,
    d.Partner_Active,
    0 AS Current_Flag, -- Mark as inactive
    d.Effective_From_Dt,
    (SELECT effective_to_date.Effective_To_Dt FROM effective_to_date) AS Effective_To_Dt, -- Update Effective_To_Dt
    CURRENT_DATETIME AS Updated_dt_ts,
    d.Created_dt_ts,
    d.Created_By,
    d.Updated_By
FROM {{ this }} d
WHERE d.Current_Flag = 1
  AND d.Partner_SK IN (SELECT Partner_SK_to_deactivate FROM rows_to_deactivate)

UNION ALL

-- Insert new or updated rows
SELECT
    s.Partner_SK,
    s.Partner_Id,
    s.Partner_Name,
    s.Partner_Type,
    s.Partner_Active,
    1 AS Current_Flag, -- Mark as active
    s.Effective_From_Dt,
    s.Effective_To_Dt,
    s.Updated_dt_ts,
    s.Created_dt_ts,
    s.Created_By,
    s.Updated_By
FROM source_data s
LEFT JOIN {{ this }} d
    ON s.Partner_Id = d.Partner_Id
   AND d.Current_Flag = 1
WHERE d.Partner_Id IS NULL OR d.Partner_SK <> s.Partner_SK

{% endif %}
