version: 2

models:
  - name: V_Engage_Partner
    description: |
      Purpose:
      This view filters and transforms partner data to provide a standardized view of
      engagement partners, including a default record for handling "Not Applicable" scenarios.
      It serves as a reference view for engagement partner relationships across the warehouse.

      Data Grain:
      One record per engagement partner per validity period, plus one default record.

      Direct Sources:
      - [D_Partner](/#!/model/model.Analytics.D_Partner) (dimension table containing partner data)

      Indirect Sources:
      - [V_Partner_DW](/#!/model/model.Analytics.V_Partner_DW) (via D_Partner)

      Key Business Rules:
      - Filters for Partner_Type = 'Engage'
      - Only includes active partners (Partner_Active = 1)
      - Includes default record (Partner_SK = "-1")
      - Renames columns with 'Engage_' prefix for partner-specific attributes

    config:
      materialized: view
      tags: ['silver', 'common', 'view', 'MxDataMartDailyBuild']

    transformations:
      - name: engage_partner_filtering
        description: |
          Filters and transforms partner records:
          - Selects active engagement partners
          - Renames key columns with 'Engage_' prefix
          - Maintains temporal validity columns
        joins:
          - join: D_Partner
            type: source
            relationship: one_to_one

      - name: default_record_addition
        description: |
          Adds default record for "Not Applicable" scenarios:
          - Uses Partner_SK = "-1"
          - Sets Partner_Type to "Engage"
          - Maintains same column structure as partner records

    columns:
      # Key Columns
      - name: Engage_Partner_SK
        description: "Surrogate key for engagement partner"
        type: string
        source_column: "Partner_SK"
        source_table: "D_Partner"

      - name: Engage_Partner_Id
        description: "Business identifier for engagement partner"
        type: string
        source_column: "Partner_Id"
        source_table: "D_Partner"

      - name: Engage_Partner_Name
        description: "Name of the engagement partner"
        type: string
        source_column: "Partner_Name"
        source_table: "D_Partner"

      # Attribute Columns
      - name: Partner_Type
        description: "Type of partner relationship"
        type: string
        source_column: "Partner_Type"
        source_table: "D_Partner"
        transformation_logic: "Set to 'Engage' for default record"

      - name: Engage_Partner_Active
        description: "Flag indicating if partner is active"
        type: integer
        source_column: "Partner_Active"
        source_table: "D_Partner"

      # Temporal Validity Columns
      - name: Effective_From_Dt
        description: "Date from which the record is effective"
        type: date
        source_column: "Effective_From_Dt"
        source_table: "D_Partner"

      - name: Effective_To_Dt
        description: "Date until which the record is effective"
        type: date
        source_column: "Effective_To_Dt"
        source_table: "D_Partner"

      - name: Current_Flag
        description: "Flag indicating if this is the current record"
        type: integer
        source_column: "Current_Flag"
        source_table: "D_Partner"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        source_column: "Created_dt_ts"
        source_table: "D_Partner"

      - name: Created_By
        description: "User who created the record"
        type: string
        source_column: "Created_By"
        source_table: "D_Partner"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        source_column: "Updated_dt_ts"
        source_table: "D_Partner"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        source_column: "Updated_By"
        source_table: "D_Partner"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "D_Partner" # [D_Partner](/#!/model/model.Analytics.D_Partner)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"