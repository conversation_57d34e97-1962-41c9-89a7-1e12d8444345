{{ config( 
          materialized='incremental'
        , unique_key='State_SK'
        , tags = ['exclude_when_incremental']
      ) 
}}

{% if not is_incremental() %}

SELECT         
        State_SK,
        State_Cd,
        State_Name,
        Region_Type,
        current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By
FROM {{ ref('V_State_DW') }}

UNION ALL

SELECT         
        "-1" AS State_SK,
        "Not Applicable" AS State_Cd,
        "Not Applicable" AS State_Name,
        "Not Applicable" AS Region_Type,
        current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By

{% else %}

WITH source_data AS (
    SELECT
        State_SK,
        State_Cd,
        State_Name,
        Region_Type,
        current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By,
        MD5(CONCAT('|',
            State_Cd,
            State_Name,
            Region_Type
        )) AS record_hash
    FROM {{ ref('V_State_DW') }}
),
-- Identify existing records in the dimension table
existing_records AS (
    SELECT
        State_SK,
        State_Cd,
        State_Name,
        Region_Type,
        Created_By,
        Updated_By,
        Created_dt_ts,
        Updated_dt_ts,
        MD5(CONCAT('|',
            State_Cd,
            State_Name,
            Region_Type
        )) AS record_hash
    FROM {{this}}
),
-- Find changed or new records
updated_records AS (
    SELECT
        src.State_Cd,
        src.State_Name,
        src.Region_Type,
        src.record_hash,
        src.State_SK,
        src.Created_dt_ts,
        src.Created_By,
        CASE
            WHEN ex.State_SK IS NULL THEN 'new'
            WHEN src.record_hash != ex.record_hash THEN 'changed'
            ELSE 'unchanged'
        END AS change_status
    FROM source_data src
    LEFT JOIN existing_records ex
    ON src.State_Cd = ex.State_Cd
),
-- Filter only new or changed records
new_or_changed_records AS (
    SELECT *
    FROM updated_records
    WHERE change_status IN ('new', 'changed')
)
-- Final dimension table logic
SELECT
    COALESCE(ex.State_SK, src.State_SK) AS State_SK,
    src.State_Cd,
    src.State_Name,
    src.Region_Type,
    CASE
        WHEN ex.State_SK IS NULL THEN CURRENT_DATETIME
        ELSE ex.Created_dt_ts
    END AS Created_dt_ts,
    CASE
        WHEN ex.State_SK IS NULL THEN CURRENT_DATETIME
        ELSE CURRENT_DATETIME
    END AS Updated_dt_ts,
    CASE
        WHEN ex.State_SK IS NULL THEN CAST(NULL AS STRING)
        ELSE CAST(ex.Created_By as STRING)
    END AS Created_By,
    CAST(NULL AS STRING) AS Updated_By
FROM new_or_changed_records src
LEFT JOIN existing_records ex
ON src.State_Cd = ex.State_Cd

{% endif %}