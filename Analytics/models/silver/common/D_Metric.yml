version: 2

models:
  - name: D_Metric
    description: |
      Purpose:
      This dimension table maintains metric definitions and their attributes with Type 2 SCD handling,
      providing a historical record of changes in metric configurations and business rules.

      Data Grain:
      One record per Metric_Id per validity period, plus one default record.

      Direct Sources:
      - [V_Metric_DW](/#!/model/model.Analytics.V_Metric_DW) (view containing metric definitions and attributes)
      - T_Batch_Control (batch processing dates for MxDataMartDailyBuild)

      Indirect Sources:
      - [stg_Metric](/#!/model/model.Analytics.stg_Metric) (staging table containing raw metric definitions)

      Key Business Rules:
      - Maintains SCD Type 2 history with effective dates
      - Includes default record with Metric_SK = "-1"
      - Only one current record per Metric_Id
      - Uses batch control dates for temporal validity
      - Maintains version control through Current_Flag

    config:
      materialized: incremental
      unique_key: Metric_SK
      tags: ['silver', 'common', 'dimension', 'exclude_when_incremental']

    transformations:
      - name: initial_load
        description: |
          Full refresh process when is_incremental() is false:
          - Extracts all records from V_Metric_DW
          - Sets effective dates from batch_control_dt to future_proof_date
          - Adds default record with Metric_SK = "-1"
          - Sets Current_Flag = 1 for all records

      - name: incremental_update
        description: |
          Incremental update process when is_incremental() is true:
          - Identifies changed records using Metric_SK comparison
          - Deactivates existing records when changes detected
          - Inserts new versions with updated attributes
        steps:
          - name: batch_date_extraction
            description: "Gets current batch date from T_Batch_Control"

          - name: source_data_extraction
            description: "Extracts current metric data from V_Metric_DW"

          - name: change_detection
            description: "Identifies records requiring updates"

          - name: record_deactivation
            description: "Updates existing records to inactive status"

          - name: new_version_insertion
            description: "Inserts new versions of changed records"

    columns:
      # Key Columns
      - name: Metric_SK
        description: "Surrogate key for the metric"
        type: string
        source_column: "Metric_SK"
        source_table: "V_Metric_DW"

      - name: Metric_Id
        description: "Business identifier for the metric"
        type: integer
        source_column: "Metric_Id"
        source_table: "V_Metric_DW"

      # Business Attributes
      - name: Subject_Area
        description: "Business domain or subject area for the metric"
        type: string
        source_column: "Subject_Area"
        source_table: "V_Metric_DW"

      - name: Metric_Type
        description: "Primary classification of the metric"
        type: string
        source_column: "Metric_Type"
        source_table: "V_Metric_DW"

      - name: Metric_Sub_Type
        description: "Secondary classification of the metric"
        type: string
        source_column: "Metric_Sub_Type"
        source_table: "V_Metric_DW"

      - name: Metric_Name
        description: "Business name of the metric"
        type: string
        source_column: "Metric_Name"
        source_table: "V_Metric_DW"

      - name: Metric_Business_Rules
        description: "Business rules defining the metric calculation"
        type: string
        source_column: "Metric_Business_Rules"
        source_table: "V_Metric_DW"

      - name: DBT_Model_Name
        description: "Name of the DBT model where metric is implemented"
        type: string
        source_column: "DBT_Model_Name"
        source_table: "V_Metric_DW"

      # Version Control
      - name: Metric_Version
        description: "Version number of the metric definition"
        type: integer
        transformation_logic: "Incremented when changes detected"

      - name: Current_Flag
        description: "Flag indicating if this is the current version"
        type: integer
        transformation_logic: "1 for current version, 0 for historical"

      # Temporal Columns
      - name: Effective_From_Dt
        description: "Date when this version becomes effective"
        type: date
        source_column: "batch_control_dt"
        source_table: "T_Batch_Control"

      - name: Effective_To_Dt
        description: "Date when this version expires"
        type: date
        transformation_logic: "future_proof_date for current, batch_date - 1 for historical"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        source_column: "Created_dt_ts"
        source_table: "V_Metric_DW"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        source_column: "Updated_dt_ts"
        source_table: "V_Metric_DW"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Metric_DW" # [V_Metric_DW](/#!/model/model.Analytics.V_Metric_DW)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "table"
      temporal_type: "scd_type_2"
      refresh_frequency: "incremental"