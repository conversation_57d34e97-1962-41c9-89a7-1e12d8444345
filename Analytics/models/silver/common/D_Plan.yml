version: 2

models:
  - name: D_Plan
    description: |
      Purpose:
      This dimension table maintains insurance plan information with Type 2 SCD handling,
      tracking historical changes in plan attributes and maintaining temporal validity.

      Data Grain:
      One record per Plan_ID per validity period, plus one default record.

      Direct Sources:
      - [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW) (view containing plan data with temporal validity)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      - [mx_insurance_Plan](/#!/model/model.Analytics.mx_insurance_Plan) (snapshot table)

      Key Business Rules:
      - Maintains SCD Type 2 history with effective dates
      - Includes default record with Plan_SK = "-1"
      - Only one current record per Plan_ID
      - Uses batch control dates for temporal validity

    config:
      materialized: incremental
      unique_key: Plan_SK
      tags: ['silver', 'common', 'dimension', 'exclude_when_incremental']

    transformations:
      - name: initial_load
        description: |
          Initial data load process when table is empty:
          - Loads all current plan records from source view
          - Adds default "Not Applicable" record
          - Sets initial effective dates and flags
        condition: "not is_incremental()"

      - name: incremental_update
        description: |
          Incremental update process for existing records:
          - Identifies changed records using Plan_SK comparison
          - Deactivates existing records when changes detected
          - Inserts new versions of changed records
          - Updates effective dates based on batch control
        steps:
          - name: change_detection
            description: |
              Identifies records requiring updates:
              - Compares source and target Plan_SK
              - Flags records needing deactivation
              - Prepares new versions for changed records

          - name: temporal_handling
            description: |
              Manages temporal validity:
              - Sets Effective_To_Dt for deactivated records
              - Establishes Effective_From_Dt for new versions
              - Maintains Current_Flag for latest records

    columns:
      # Key Columns
      - name: Plan_SK
        description: "Surrogate key for the plan"
        type: string
        source_column: "Plan_SK"
        source_table: "V_Plan_DW"

      # Temporal Columns
      - name: Effective_From_Dt
        description: "Date when this version becomes effective"
        type: date
        source_column: "Effective_From_Dt"
        source_table: "V_Plan_DW"

      - name: Effective_To_Dt
        description: "Date when this version expires"
        type: date
        source_column: "Effective_To_Dt"
        source_table: "V_Plan_DW"

      # Business Attributes
      - name: Plan_Id
        description: "Business identifier for the plan"
        type: integer
        source_column: "Plan_Id"
        source_table: "V_Plan_DW"

      - name: Plan_Name
        description: "Name of the insurance plan"
        type: string
        source_column: "Plan_Name"
        source_table: "V_Plan_DW"

      - name: Plan_Type
        description: "Type classification of the plan"
        type: integer
        source_column: "Plan_Type"
        source_table: "V_Plan_DW"

      - name: Plan_External_Id
        description: "External identifier for the plan"
        type: string
        source_column: "Plan_External_Id"
        source_table: "V_Plan_DW"

      - name: Plan_Number
        description: "Plan number extracted from external ID"
        type: string
        source_column: "Plan_Number"
        source_table: "V_Plan_DW"

      - name: Plan_Product_Name
        description: "Product name associated with the plan"
        type: string
        source_column: "Plan_Product_Name"
        source_table: "V_Plan_DW"

      - name: Plan_Group_Number
        description: "Group number associated with the plan"
        type: string
        source_column: "Plan_Group_Number"
        source_table: "V_Plan_DW"

      - name: Plan_Active
        description: "Flag indicating if the plan is active"
        type: integer
        source_column: "Plan_Active"
        source_table: "V_Plan_DW"

      - name: Group_Id
        description: "Identifier for the associated group"
        type: integer
        source_column: "Group_Id"
        source_table: "V_Plan_DW"

      - name: Current_Flag
        description: "Flag indicating if this is the current version"
        type: integer
        transformation_logic: "1 for current version, 0 for historical"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        source_column: "Created_dt_ts"
        source_table: "V_Plan_DW"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        source_column: "Updated_dt_ts"
        source_table: "V_Plan_DW"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Plan_DW" # [V_Plan_DW](/#!/model/model.Analytics.V_Plan_DW)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "table"
      temporal_type: "scd_type_2"
      refresh_frequency: "incremental"