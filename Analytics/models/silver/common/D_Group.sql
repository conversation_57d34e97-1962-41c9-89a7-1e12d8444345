{{ config(
    materialized='incremental',
    unique_key='Group_SK'
    , tags = ['exclude_when_incremental']
) }}

-- Incremental logic
{% if not is_incremental() %}
-- Source data extraction
    SELECT
        Group_SK,
        Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        Group_Id,
        Group_External_Id,
        Group_Name,
        Group_Frozen_Flag,
        Group_Active,
        Termination_Date,
        Address_Line_1,
        Address_Line_2,
        City_Name,
        State_Cd,
        Postal_Cd,
        1 AS Current_Flag,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Group_DW') }}

    UNION ALL

    SELECT
        "-1" as Group_SK,
        DATE('{{ var("past_init_date") }}') AS Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        0 Group_Id,
        "NA" AS Group_External_Id,
        "NA" AS Group_Name,
        0 AS Group_Frozen_Flag,
        1 AS Group_Active,
        DATE('{{ var("future_proof_date") }}') AS Termination_Date,
        "NA" AS Address_Line_1,
        "NA" AS Address_Line_2,
        "NA" AS City_Name,
        "NA" AS State_Cd,
        "NA" AS Postal_Cd,
        NULL AS Current_Flag,
        CURRENT_DATETIME AS Created_dt_ts,
        CURRENT_DATETIME AS Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By    
        
{% else %}

WITH source_data AS  (
     SELECT
        Group_SK,
        Effective_From_Dt,
        Effective_To_Dt,
        Group_Id,
        Group_External_Id,
        Group_Name,
        Group_Frozen_Flag,
        Group_Active,
        Termination_Date,
        Address_Line_1,
        Address_Line_2,
        City_Name,
        State_Cd,
        Postal_Cd,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Group_DW') }}
)

,effective_to_date AS (
    SELECT 
        DATE_SUB(batch_control_dt, INTERVAL 1 DAY) as Effective_To_Dt
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)
-- Identify rows to deactivate
, rows_to_deactivate AS (
    SELECT
        d.Group_SK AS Group_SK_to_deactivate
    FROM {{ this }} d
    INNER JOIN source_data s
        ON s.Group_Id = d.Group_Id
       AND d.Current_Flag = 1
    WHERE d.Group_Id IS NOT NULL
      AND d.Group_SK <> s.Group_SK
)



-- Update existing rows (set Current_Flag to 0)
SELECT
    d.Group_SK,
    d.Group_Id,
    d.Group_External_Id,
    d.Group_Name,
    d.Group_Frozen_Flag,
    d.Group_Active,
    d.Termination_Date,
    d.Address_Line_1,
    d.Address_Line_2,
    d.City_Name,
    d.State_Cd,
    d.Postal_Cd,
    0 AS Current_Flag, -- Mark as inactive
    d.Effective_From_Dt,
    (select effective_to_date.Effective_To_Dt from effective_to_date) as Effective_To_Dt, -- Update Effective_To_Dt
    CURRENT_DATETIME AS updated_dt_ts,
    d.Created_dt_ts,
    d.Created_By,
    d.Updated_By
FROM {{ this }} d
WHERE d.Current_Flag = 1
  AND d.Group_SK IN (SELECT Group_SK_to_deactivate FROM rows_to_deactivate)

UNION ALL

-- Insert new or updated rows
SELECT
    s.Group_SK,
    s.Group_Id,
    s.Group_External_Id,
    s.Group_Name,
    s.Group_Frozen_Flag,
    s.Group_Active,
    s.Termination_Date,
    s.Address_Line_1,
    s.Address_Line_2,
    s.City_Name,
    s.State_Cd,
    s.Postal_Cd,
    1 AS Current_Flag, -- Mark as active
    s.Effective_From_Dt,
    s.Effective_To_Dt,
    s.Updated_dt_ts,
    s.Created_dt_ts,
    s.Created_By,
    s.Updated_By
FROM source_data s
LEFT JOIN {{ this }} d
    ON s.Group_Id = d.Group_Id
   AND d.Current_Flag = 1
WHERE d.Group_Id IS NULL OR d.Group_SK <> s.Group_SK

{% endif %}
