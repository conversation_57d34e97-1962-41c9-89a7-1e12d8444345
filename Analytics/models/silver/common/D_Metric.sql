{{ config(
    materialized='incremental',
    unique_key='Metric_SK'
    , tags = ['exclude_when_incremental']
) }}



{% if not is_incremental() %}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

-- Source data extraction
    SELECT
        Metric_SK,
        (SELECT batch_date.batch_date FROM batch_date) AS Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        Metric_Id,
        Subject_Area,
        Metric_Type,
        Metric_Sub_Type,
        Metric_Name,
        Metric_Business_Rules,
        DBT_Model_Name,
        1 AS Metric_Version,
        1 AS Current_Flag,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Metric_DW') }}

    UNION ALL

    SELECT
        "-1" AS Metric_SK,
        DATE('{{ var("past_init_date") }}') AS Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        0 AS Metric_Id,
        "NA" AS Subject_Area,
        "NA" AS Metric_Type,
        "NA" AS Metric_Sub_Type,
        "NA" AS Metric_Name,
        "NA" AS Metric_Business_Rules,
        "NA" AS DBT_Model_Name,
        1 AS Metric_Version,
        1 AS Current_Flag,
        CURRENT_DATETIME AS Created_dt_ts,
        CURRENT_DATETIME AS Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By

{% else %}

WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

,effective_to_date AS (
    SELECT 
        DATE_SUB(batch_control_dt, INTERVAL 1 DAY) AS Effective_To_Dt
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

,source_data AS (
    SELECT
        Metric_SK,
        Metric_Id,
        Subject_Area,
        Metric_Type,
        Metric_Sub_Type,
        Metric_Name,
        Metric_Business_Rules,
        DBT_Model_Name,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Metric_DW') }}
),


rows_to_deactivate AS (
    SELECT
        d.Metric_SK AS Metric_SK_to_deactivate
    FROM {{ this }} d
    INNER JOIN source_data s
        ON s.Metric_Id = d.Metric_Id
       AND d.Current_Flag = 1
    WHERE d.Metric_Id IS NOT NULL
      AND d.Metric_SK <> s.Metric_SK
)

-- Update existing rows (set Current_Flag to 0)
SELECT
    d.Metric_SK,
    d.Metric_Id,
    d.Subject_Area,
    d.Metric_Type,
    d.Metric_Sub_Type,
    d.Metric_Name,
    d.Metric_Business_Rules,
    d.DBT_Model_Name,
    d.Metric_Version,
    0 AS Current_Flag, -- Mark as inactive
    d.Effective_From_Dt,
    (SELECT effective_to_date.Effective_To_Dt FROM effective_to_date) AS Effective_To_Dt, -- Update Effective_To_Dt
    CURRENT_DATETIME AS Updated_dt_ts,
    d.Created_dt_ts,
    d.Created_By,
    d.Updated_By
FROM {{ this }} d
WHERE d.Current_Flag = 1
  AND d.Metric_SK IN (SELECT Metric_SK_to_deactivate FROM rows_to_deactivate)

UNION ALL

-- Insert new or updated rows
SELECT
    s.Metric_SK,
    s.Metric_Id,
    s.Subject_Area,
    s.Metric_Type,
    s.Metric_Sub_Type,
    s.Metric_Name,
    s.Metric_Business_Rules,
    s.DBT_Model_Name,
    CASE WHEN d.Metric_SK <> s.Metric_SK THEN d.Metric_Version + 1
    ELSE 1
    END AS Metric_Version,
    1 AS Current_Flag, -- Mark as active
    (SELECT batch_date.batch_date FROM batch_date) AS Effective_From_Dt,
    DATE('9999-12-31') AS Effective_To_Dt,
    s.Updated_dt_ts,
    s.Created_dt_ts,
    s.Created_By,
    s.Updated_By
FROM source_data s
LEFT JOIN {{ this }} d
    ON s.Metric_Id = d.Metric_Id
   AND d.Current_Flag = 1
WHERE d.Metric_Id IS NULL OR d.Metric_SK <> s.Metric_SK

{% endif %}
