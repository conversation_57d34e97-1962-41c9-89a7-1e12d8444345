{{ config(
    materialized='table'
) }}

WITH minutes_cte AS (
  SELECT 
    FORMAT("%02d:%02d:00", hour, minute) AS time,
    FORMAT("%02d:00:00 - %02d:59:00", hour, hour) AS hour_interval
  FROM 
    UNNEST(GENERATE_ARRAY(0, 23)) AS hour,
    UNNEST(GENERATE_ARRAY(0, 59)) AS minute
)
SELECT 
    {{ dbt_utils.generate_surrogate_key([
        'time'
    ])}} AS Time_Of_Day_SK,
    time AS Time,
    hour_interval AS Hour_Interval
    
FROM minutes_cte
