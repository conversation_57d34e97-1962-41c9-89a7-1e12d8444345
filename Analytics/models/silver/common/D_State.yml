version: 2

models:
  - name: D_State
    description: |
      Purpose:
      This dimension table maintains state/province reference data with Type 1 SCD handling,
      providing standardized geographical state information and regional classifications.

      Data Grain:
      One record per unique state/province, plus one default record.

      Direct Sources:
      - [V_State_DW](/#!/model/model.Analytics.V_State_DW) (view containing state reference data)

      Indirect Sources:
      - [stg_State](/#!/model/model.Analytics.stg_State) (staging table containing raw state data)

      Key Business Rules:
      - Maintains single current record per state (Type 1 SCD)
      - Includes default record with State_SK = "-1"
      - Uses MD5 hash to detect changes
      - Updates existing records rather than creating new versions

    config:
      materialized: incremental
      unique_key: State_SK
      tags: ['silver', 'common', 'dimension', 'exclude_when_incremental']

    transformations:
      - name: initial_load
        description: |
          Initial data load process when table is empty:
          - Loads all state records from source view
          - Adds default "Not Applicable" record
          - Sets initial audit timestamps
        condition: "not is_incremental()"

      - name: incremental_update
        description: |
          Incremental update process for existing table:
          - Calculates MD5 hash of business attributes
          - Identifies new or changed records
          - Updates only modified records
          - Preserves creation timestamps
        steps:
          - name: hash_calculation
            description: |
              Creates MD5 hash from concatenated fields:
              - State_Cd
              - State_Name
              - Region_Type
          - name: change_detection
            description: |
              Compares source and target hashes to identify:
              - New records (not in target)
              - Changed records (different hash)
              - Unchanged records (matching hash)
          - name: record_update
            description: |
              Updates records based on change status:
              - New records get current timestamp
              - Changed records keep original Created_dt_ts
              - All modified records get current Updated_dt_ts

    columns:
      # Key Columns
      - name: State_SK
        description: "Surrogate key for the state"
        type: string
        source_column: "State_SK"
        source_table: "V_State_DW"
        transformation_logic: "From source view, '-1' for default record"

      # Attribute Columns
      - name: State_Cd
        description: "State/province code"
        type: string
        source_column: "State_Cd"
        source_table: "V_State_DW"
        transformation_logic: "'Not Applicable' for default record"

      - name: State_Name
        description: "Full state/province name"
        type: string
        source_column: "State_Name"
        source_table: "V_State_DW"
        transformation_logic: "'Not Applicable' for default record"

      - name: Region_Type
        description: "Regional classification of the state"
        type: string
        source_column: "Region_Type"
        source_table: "V_State_DW"
        transformation_logic: "'Not Applicable' for default record"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: "current_datetime() for new records"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        transformation_logic: "current_datetime() for all modified records"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_State_DW" # [V_State_DW](/#!/model/model.Analytics.V_State_DW)
      table_type: "table"
      temporal_type: "scd_type_1"
      refresh_frequency: "incremental"