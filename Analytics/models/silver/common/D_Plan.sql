{{ config(
    materialized='incremental',
    unique_key='Plan_SK'
    , tags = ['exclude_when_incremental']
) }}

{% if not is_incremental() %}

-- Source data extraction
    SELECT
        Plan_SK,
        Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        Plan_Id,
        Plan_Name,
        Plan_Type,
        Plan_External_Id,
        Plan_Number,
        Plan_Product_Name,
        Plan_Group_Number,
        Plan_Active,
        Group_Id,
        1 AS Current_Flag,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Plan_DW') }}

    UNION ALL

    SELECT
        "-1" AS Plan_SK,
        DATE('{{ var("past_init_date") }}') AS Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        0 AS Plan_Id,
        "NA" AS Plan_Name,
        0 AS Plan_Type,
        "NA" AS Plan_External_Id,
        "NA" AS Plan_Number,
        "NA" AS Plan_Product_Name,
        "NA" AS Plan_Group_Number,
        1 AS Plan_Active,
        0 AS Group_Id,
        1 AS Current_Flag,
        CURRENT_DATETIME AS Created_dt_ts,
        CURRENT_DATETIME AS Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By

{% else %}

WITH source_data AS (
    SELECT
        Plan_SK,
        Effective_From_Dt,
        Effective_To_Dt,
        Plan_Id,
        Plan_Name,
        Plan_Type,
        Plan_External_Id,
        Plan_Number,
        Plan_Product_Name,
        Plan_Group_Number,
        Plan_Active,
        Group_Id,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Plan_DW') }}
),

effective_to_date AS (
    SELECT 
        DATE_SUB(batch_control_dt, INTERVAL 1 DAY) AS Effective_To_Dt
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
),

rows_to_deactivate AS (
    SELECT
        d.Plan_SK AS Plan_SK_to_deactivate
    FROM {{ this }} d
    INNER JOIN source_data s
        ON s.Plan_Id = d.Plan_Id
       AND d.Current_Flag = 1
    WHERE d.Plan_Id IS NOT NULL
      AND d.Plan_SK <> s.Plan_SK
)

-- Update existing rows (set Current_Flag to 0)
SELECT
    d.Plan_SK,
    d.Plan_Id,
    d.Plan_Name,
    d.Plan_Type,
    d.Plan_External_Id,
    d.Plan_Number,
    d.Plan_Product_Name,
    d.Plan_Group_Number,
    d.Plan_Active,
    d.Group_Id,
    0 AS Current_Flag, -- Mark as inactive
    d.Effective_From_Dt,
    (SELECT effective_to_date.Effective_To_Dt FROM effective_to_date) AS Effective_To_Dt, -- Update Effective_To_Dt
    CURRENT_DATETIME AS Updated_dt_ts,
    d.Created_dt_ts,
    d.Created_By,
    d.Updated_By
FROM {{ this }} d
WHERE d.Current_Flag = 1
  AND d.Plan_SK IN (SELECT Plan_SK_to_deactivate FROM rows_to_deactivate)

UNION ALL

-- Insert new or updated rows
SELECT
    s.Plan_SK,
    s.Plan_Id,
    s.Plan_Name,
    s.Plan_Type,
    s.Plan_External_Id,
    s.Plan_Number,
    s.Plan_Product_Name,
    s.Plan_Group_Number,
    s.Plan_Active,
    s.Group_Id,
    1 AS Current_Flag, -- Mark as active
    s.Effective_From_Dt,
    s.Effective_To_Dt,
    s.Updated_dt_ts,
    s.Created_dt_ts,
    s.Created_By,
    s.Updated_By
FROM source_data s
LEFT JOIN {{ this }} d
    ON s.Plan_Id = d.Plan_Id
   AND d.Current_Flag = 1
WHERE d.Plan_Id IS NULL OR d.Plan_SK <> s.Plan_SK

{% endif %}
