version: 2

models:
  - name: D_Partner
    description: |
      Purpose:
      This dimension table maintains partner information with Type 2 SCD handling,
      tracking historical changes in partner attributes and maintaining temporal validity.

      Data Grain:
      One record per Partner_ID per validity period, plus one default record.

      Direct Sources:
      - [V_Partner_DW](/#!/model/model.Analytics.V_Partner_DW) (view containing partner data with temporal validity)
      - T_Batch_Control (batch processing dates)

      Indirect Sources:
      - [mx_configuration_Partner](/#!/model/model.Analytics.mx_configuration_Partner) (snapshot table)

      Key Business Rules:
      - Maintains SCD Type 2 history with effective dates
      - Includes default record with Partner_SK = "-1"
      - Only one current record per Partner_Id
      - Partner types must be one of: Billing, Engage, or Auth
      - Changes to existing records create new versions

    config:
      materialized: incremental
      unique_key: Partner_SK
      tags: ['silver', 'common', 'dimension', 'exclude_when_incremental']

    transformations:
      - name: initial_load
        description: |
          Initial data load process when table is empty:
          - Loads all current partner records from source view
          - Adds default "Not Applicable" record with Partner_SK = "-1"
          - Sets initial effective dates and flags
        condition: "not is_incremental()"

      - name: incremental_update
        description: |
          Incremental update process for existing records:
          - Identifies changed records using Partner_SK comparison
          - Deactivates existing records when changes detected
          - Inserts new versions of changed records
          - Updates effective dates based on batch control
        steps:
          - name: change_detection
            description: |
              Identifies records requiring updates:
              - Compares source and target Partner_SK
              - Flags records needing deactivation
              - Prepares new versions for changed records

          - name: temporal_handling
            description: |
              Manages temporal validity:
              - Sets Effective_To_Dt for deactivated records
              - Establishes Effective_From_Dt for new versions
              - Maintains Current_Flag for latest records

    columns:
      # Key Columns
      - name: Partner_SK
        description: "Surrogate key for the partner"
        type: string
        source_column: "Partner_SK"
        source_table: "V_Partner_DW"

      - name: Partner_Id
        description: "Business identifier for the partner"
        type: integer
        source_column: "Partner_Id"
        source_table: "V_Partner_DW"

      # Business Attributes
      - name: Partner_Name
        description: "Name of the partner organization"
        type: string
        source_column: "Partner_Name"
        source_table: "V_Partner_DW"

      - name: Partner_Type
        description: "Type classification of the partner (Billing, Engage, or Auth)"
        type: string
        source_column: "Partner_Type"
        source_table: "V_Partner_DW"

      - name: Partner_Active
        description: "Flag indicating if the partner is active"
        type: integer
        source_column: "Partner_Active"
        source_table: "V_Partner_DW"

      # Temporal Columns
      - name: Effective_From_Dt
        description: "Date when this version becomes effective"
        type: date
        source_column: "Effective_From_Dt"
        source_table: "V_Partner_DW"

      - name: Effective_To_Dt
        description: "Date when this version expires"
        type: date
        source_column: "Effective_To_Dt"
        source_table: "V_Partner_DW"

      - name: Current_Flag
        description: "Flag indicating if this is the current version"
        type: integer
        transformation_logic: "1 for current version, 0 for historical"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        source_column: "Created_dt_ts"
        source_table: "V_Partner_DW"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        source_column: "Updated_dt_ts"
        source_table: "V_Partner_DW"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Partner_DW" # [V_Partner_DW](/#!/model/model.Analytics.V_Partner_DW)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "table"
      temporal_type: "scd_type_2"
      refresh_frequency: "incremental"