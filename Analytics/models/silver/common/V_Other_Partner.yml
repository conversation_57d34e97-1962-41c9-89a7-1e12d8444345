version: 2

models:
  - name: V_Other_Partner
    description: |
      Purpose:
      This view provides a standardized view of Auth partners by transforming partner data
      and applying specific type filtering. It includes a default record for handling
      "Not Applicable" scenarios.

      Data Grain:
      One record per Auth partner per validity period, plus one default record.

      Direct Sources:
      - [D_Partner](/#!/model/model.Analytics.D_Partner) (dimension table containing partner information)

      Indirect Sources:
      - [V_Partner_DW](/#!/model/model.Analytics.V_Partner_DW) (via D_Partner)

      Key Business Rules:
      - Filters for Partner_Type = 'Auth'
      - Includes default record (Partner_SK = '-1')
      - Renames partner attributes with 'Auth_' prefix
      - Maintains temporal validity from source

    config:
      materialized: view
      tags: ['silver', 'common', 'view', 'MxDataMartDailyBuild']

    transformations:
      - name: auth_partner_selection
        description: |
          Selects and transforms Auth partner records:
          - Filters for Auth partner type
          - Renames columns with Auth prefix
          - Preserves temporal validity

      - name: default_record_addition
        description: |
          Adds default record for "Not Applicable" scenarios:
          - Includes Partner_SK = '-1' record
          - Sets Partner_Type to 'Auth'
          - Maintains consistent column structure

    columns:
      # Key Columns
      - name: Auth_Partner_SK
        description: "Surrogate key for the Auth partner"
        type: string
        source_column: "Partner_SK"
        source_table: "D_Partner"

      - name: Auth_Partner_Id
        description: "Business identifier for the Auth partner"
        type: string
        source_column: "Partner_Id"
        source_table: "D_Partner"

      # Attribute Columns
      - name: Auth_Partner_Name
        description: "Name of the Auth partner"
        type: string
        source_column: "Partner_Name"
        source_table: "D_Partner"

      - name: Partner_Type
        description: "Type of partner (always 'Auth' in this view)"
        type: string
        source_column: "Partner_Type"
        source_table: "D_Partner"

      - name: Auth_Partner_Active
        description: "Flag indicating if the Auth partner is active"
        type: boolean
        source_column: "Partner_Active"
        source_table: "D_Partner"

      # Temporal Columns
      - name: Effective_From_Dt
        description: "Date from which the record is effective"
        type: date
        source_column: "Effective_From_Dt"
        source_table: "D_Partner"

      - name: Effective_To_Dt
        description: "Date until which the record is effective"
        type: date
        source_column: "Effective_To_Dt"
        source_table: "D_Partner"

      - name: Current_Flag
        description: "Flag indicating if this is the current record"
        type: boolean
        source_column: "Current_Flag"
        source_table: "D_Partner"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        source_column: "Created_dt_ts"
        source_table: "D_Partner"

      - name: Created_By
        description: "User who created the record"
        type: string
        source_column: "Created_By"
        source_table: "D_Partner"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        source_column: "Updated_dt_ts"
        source_table: "D_Partner"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        source_column: "Updated_By"
        source_table: "D_Partner"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "D_Partner" # [D_Partner](/#!/model/model.Analytics.D_Partner)
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"