{{ config(
    materialized='incremental',
    unique_key='TPA_Name',
    tags=['exclude_when_incremental']
) }}
{% if not is_incremental() %}
SELECT
    TPA_SK,
    TPA_Name,
    current_datetime() AS Created_dt_ts,
    current_datetime() AS Updated_dt_ts,
    CAST(NULL AS STRING) AS Created_By,
    CAST(NULL AS STRING) AS Updated_By
FROM {{ ref('V_TPA_DW') }}
UNION ALL
SELECT
    "-1" AS TPA_SK,
    "Not Applicable" AS TPA_Name,
    current_datetime() AS Created_dt_ts,
    current_datetime() AS Updated_dt_ts,
    CAST(NULL AS STRING) AS Created_By,
    CAST(NULL AS STRING) AS Updated_By
{% else %}
WITH source_data AS (
    SELECT
        TPA_SK,
        TPA_Name,
        current_datetime() AS Created_dt_ts,
        current_datetime() AS Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_TPA_DW') }}
),
-- Identify new records by excluding existing records based on TPA_Name
new_records AS (
    SELECT
        src.TPA_SK,
        src.TPA_Name,
        src.Created_dt_ts,
        src.Updated_dt_ts,
        src.Created_By,
        src.Updated_By
    FROM source_data src
    LEFT JOIN {{ this }} tgt
    ON src.TPA_Name = tgt.TPA_Name
    WHERE tgt.TPA_Name IS NULL  -- Only select records that do not exist in the target table
)
-- Insert only new records into the final table
SELECT
    TPA_SK,
    TPA_Name,
    Created_dt_ts,
    Updated_dt_ts,
    Created_By,
    Updated_By
FROM new_records
{% endif %}
