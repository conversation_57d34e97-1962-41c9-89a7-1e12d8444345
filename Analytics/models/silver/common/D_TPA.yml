version: 2

models:
  - name: D_TPA
    description: |
      Purpose:
      This dimension table maintains Third Party Administrator (TPA) information with Type 1 SCD
      handling. It provides a standardized reference for TPA relationships across the warehouse,
      including a default record for "Not Applicable" scenarios.

      Data Grain:
      One record per unique TPA name, plus one default record.

      Direct Sources:
      - [V_TPA_DW](/#!/model/model.Analytics.V_TPA_DW) (view containing TPA data)

      Indirect Sources:
      - [stg_TPA](/#!/model/model.Analytics.stg_TPA) (staging table containing raw TPA data)

      Key Business Rules:
      - Maintains single current record per TPA (Type 1 SCD)
      - Includes default record with TPA_SK = "-1"
      - TPA names must be unique
      - Updates existing records for changes rather than creating new versions

    config:
      materialized: incremental
      unique_key: TPA_Name
      tags: ['silver', 'common', 'dimension', 'exclude_when_incremental']

    transformations:
      - name: initial_load
        description: |
          Initial data load process when table is empty:
          - Loads all TPA records from source view
          - Adds default "Not Applicable" record
          - Sets initial audit timestamps
        condition: "not is_incremental()"

      - name: incremental_update
        description: |
          Incremental update process for existing table:
          - Identifies new TPA records not in current table
          - Maintains existing Created_dt_ts for unchanged records
          - Updates Updated_dt_ts for changed records
          - Preserves default record
        condition: "is_incremental()"

      - name: default_record
        description: |
          Adds default record for "Not Applicable" scenarios:
          - Sets TPA_SK = "-1"
          - Sets TPA_Name = "Not Applicable"
          - Maintains same audit columns as regular records

    columns:
      # Key Columns
      - name: TPA_SK
        description: "Surrogate key for the TPA"
        type: string
        source_column: "TPA_SK"
        source_table: "V_TPA_DW"
        transformation_logic: "Generated in source view, '-1' for default record"

      - name: TPA_Name
        description: "Unique name identifier for the TPA"
        type: string
        source_column: "TPA_Name"
        source_table: "V_TPA_DW"
        transformation_logic: "'Not Applicable' for default record"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: "current_datetime() for new records"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        transformation_logic: "current_datetime() for all records"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_TPA_DW" # [V_TPA_DW](/#!/model/model.Analytics.V_TPA_DW)
      table_type: "table"
      temporal_type: "scd_type_1"
      refresh_frequency: "incremental"