version: 2

models:
  - name: D_Age
    description: |
      Purpose:
      This dimension table maintains age-related attributes and groupings with Type 1 SCD handling,
      providing standardized age classifications for analytical purposes.

      Data Grain:
      One record per unique Age value, plus one default record.

      Direct Sources:
      - [V_Age_DW](/#!/model/model.Analytics.V_Age_DW) (view containing age classifications and groupings)

      Indirect Sources:
      - [mx_eligibility_Eligibility](/#!/model/model.Analytics.mx_eligibility_Eligibility) (via V_Age_DW)

      Key Business Rules:
      - Includes default record with Age_SK = "-1"
      - Updates existing records when age groupings change
      - Maintains audit timestamps for creation and updates
      - Uses MD5 hash to detect changes in business attributes
      - Preserves original creation timestamp for existing records

    config:
      materialized: incremental
      unique_key: Age_SK
      tags: ['silver', 'common', 'dimension', 'exclude_when_incremental']

    transformations:
      - name: initial_load
        description: |
          Initial data load process when is_incremental() is false:
          - Loads age records from V_Age_DW
          - Adds default record (Age_SK = "-1")
          - Sets initial audit timestamps
          - Nullifies Created_By and Updated_By

      - name: incremental_update
        description: |
          Incremental update process with change detection:
          - Calculates MD5 hash of business attributes (Age, Age_Group_1, Age_Group_2)
          - Identifies new and changed records
          - Updates existing records while preserving creation info
          - Inserts new records with current timestamps
        steps:
          - name: source_data_preparation
            description: |
              Creates source_data CTE with:
              - Age attributes from V_Age_DW
              - MD5 hash of age and grouping attributes
              - Current timestamp for audit fields

          - name: existing_records
            description: |
              Identifies existing records in dimension table:
              - Retrieves current records with attributes
              - Calculates MD5 hash for comparison
              - Preserves existing audit information

          - name: change_detection
            description: |
              Determines record status through updated_records CTE:
              - New records (not in existing data)
              - Changed records (different hash)
              - Unchanged records (matching hash)

    columns:
      # Key Columns
      - name: Age_SK
        description: "Surrogate key for the age dimension"
        type: string
        source_column: "Age_SK"
        source_table: "V_Age_DW"

      # Business Attributes
      - name: Age
        description: "Numeric age value"
        type: integer
        source_column: "Age"
        source_table: "V_Age_DW"

      - name: Age_Group_1
        description: "Primary age grouping classification"
        type: string
        source_column: "Age_Group_1"
        source_table: "V_Age_DW"

      - name: Age_Group_2
        description: "Secondary age grouping classification"
        type: string
        source_column: "Age_Group_2"
        source_table: "V_Age_DW"

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: >
          CASE
            WHEN ex.Age_SK IS NULL THEN CURRENT_DATETIME
            ELSE ex.Created_dt_ts
          END

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        transformation_logic: >
          CASE
            WHEN ex.Age_SK IS NULL THEN CURRENT_DATETIME
            ELSE CURRENT_DATETIME
          END

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: >
          CASE
            WHEN ex.Age_SK IS NULL THEN CAST(NULL AS STRING)
            ELSE CAST(ex.Created_By as STRING)
          END

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Age_DW" # [V_Age_DW](/#!/model/model.Analytics.V_Age_DW)
      table_type: "table"
      temporal_type: "scd_type_1"
      refresh_frequency: "incremental"