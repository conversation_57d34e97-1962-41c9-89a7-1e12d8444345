{{ config(
    materialized='incremental',
    unique_key='Module_Config_SK',
    tags = ['exclude_when_incremental']
)}}

{% if not is_incremental() %}
    -- Full-refresh: extract all records from the source plus a default record
    SELECT
        Module_Config_SK,
        Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        Module_Config_Id,
        Module_Id,
        Group_Id,
        Module_Config_Settings,
        Module_Config_Menu_Name,
        Module_Config_External_Id,
        Module_Config_Active,
        -- Adding Module attributes from the join
        Module_Name,
        Category_Name,
        UI_Type,
        Vendor_Name,
        Module_Description,
        Module_Active,
        1 AS Current_Flag,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Module_Config_DW') }}

    UNION ALL

    SELECT
        "-1" AS Module_Config_SK,
        DATE('{{ var("past_init_date") }}') AS Effective_From_Dt,
        DATE('{{ var("future_proof_date") }}') AS Effective_To_Dt,
        0 AS Module_Config_Id,
        0 AS Module_Id,
        0 AS Group_Id,
        'NA' AS Module_Config_Settings,
        'NA' AS Module_Config_Menu_Name,
        'NA' AS Module_Config_External_Id,
        1 AS Module_Config_Active,
        'NA' AS Module_Name,
        'NA' AS Category_Name,
        'NA' AS UI_Type,
        'NA' AS Vendor_Name,
        'NA' AS Module_Description,
        1 AS Module_Active,
        1 AS Current_Flag,
        CURRENT_DATETIME() AS Created_dt_ts,
        CURRENT_DATETIME() AS Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By

{% else %}

WITH source_data AS (
    SELECT
        Module_Config_SK,
        Effective_From_Dt,
        Effective_To_Dt,
        Module_Config_Id,
        Module_Id,
        Group_Id,
        Module_Config_Settings,
        Module_Config_Menu_Name,
        Module_Config_External_Id,
        Module_Config_Active,
        Module_Name,
        Category_Name,
        UI_Type,
        Vendor_Name,
        Module_Description,
        Module_Active,
        Created_dt_ts,
        Updated_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref('V_Module_Config_DW') }}
),

effective_to_date AS (
    SELECT 
        DATE_SUB(batch_control_dt, INTERVAL 1 DAY) AS Effective_To_Dt
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
),

rows_to_deactivate AS (
    SELECT
        d.Module_Config_SK AS Module_Config_SK_to_deactivate
    FROM {{ this }} d
    INNER JOIN source_data s
        ON s.Module_Config_Id = d.Module_Config_Id
       AND d.Current_Flag = 1
    WHERE d.Module_Config_Id IS NOT NULL
      AND d.Module_Config_SK <> s.Module_Config_SK
)

-- First, update (deactivate) the current records that are no longer current
SELECT
    d.Module_Config_SK,
    d.Module_Config_Id,
    d.Module_Id,
    d.Group_Id,
    d.Module_Config_Settings,
    d.Module_Config_Menu_Name,
    d.Module_Config_External_Id,
    d.Module_Config_Active,
    d.Module_Name,
    d.Category_Name,
    d.UI_Type,
    d.Vendor_Name,
    d.Module_Description,
    d.Module_Active,
    0 AS Current_Flag, -- mark as inactive
    d.Effective_From_Dt,
    (SELECT effective_to_date.Effective_To_Dt FROM effective_to_date) AS Effective_To_Dt,
    CURRENT_DATETIME() AS Updated_dt_ts,
    d.Created_dt_ts,
    d.Created_By,
    d.Updated_By
FROM {{ this }} d
WHERE d.Current_Flag = 1
  AND d.Module_Config_SK IN (SELECT Module_Config_SK_to_deactivate FROM rows_to_deactivate)

UNION ALL

-- Then, insert the new (or changed) records from the source
SELECT
    s.Module_Config_SK,
    s.Module_Config_Id,
    s.Module_Id,
    s.Group_Id,
    s.Module_Config_Settings,
    s.Module_Config_Menu_Name,
    s.Module_Config_External_Id,
    s.Module_Config_Active,
    s.Module_Name,
    s.Category_Name,
    s.UI_Type,
    s.Vendor_Name,
    s.Module_Description,
    s.Module_Active,
    1 AS Current_Flag, -- mark as active
    s.Effective_From_Dt,
    s.Effective_To_Dt,
    s.Updated_dt_ts,
    s.Created_dt_ts,
    s.Created_By,
    s.Updated_By
FROM source_data s
LEFT JOIN {{ this }} d
    ON s.Module_Config_Id = d.Module_Config_Id
   AND d.Current_Flag = 1
WHERE d.Module_Config_Id IS NULL OR d.Module_Config_SK <> s.Module_Config_SK

{% endif %}
