{{ config( 
          materialized='incremental'
        , unique_key='Age_SK'
        , tags = ['exclude_when_incremental']
      ) 
}}

{% if not is_incremental() %}

SELECT         
        Age_SK,
        Age,
        Age_Group_1,
        Age_Group_2,
        current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By
FROM {{ ref('V_Age_DW') }}

UNION ALL

SELECT         
        "-1" AS Age_SK,
        -1 AS Age,
        "Not Applicable" AS Age_Group_1,
        "Not Applicable" AS Age_Group_2,
        current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By

{% else %}

WITH source_data AS (
    SELECT
        Age_SK,
        Age,
        Age_Group_1,
        Age_Group_2,
        current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By,
        MD5(CONCAT('|',
          Age,
          Age_Group_1,
          Age_Group_2
        )) AS record_hash
    FROM {{ ref('V_Age_DW') }}
),
-- Identify existing records in the dimension table
existing_records AS (
    SELECT
        Age_SK,
        Age,
        Age_Group_1,
        Age_Group_2,
        Created_By,
        Updated_By,
        Created_dt_ts,
        Updated_dt_ts,
        MD5(CONCAT('|',
          Age,
          Age_Group_1,
          Age_Group_2
        )) AS record_hash
    FROM {{this}}
),
-- Find changed or new records
updated_records AS (
    SELECT
        src.Age,
        src.Age_Group_1,
        src.Age_Group_2,
        src.record_hash,
        src.Age_SK,
        src.Created_dt_ts,
        src.Created_By,
        CASE
            WHEN ex.Age_SK IS NULL THEN 'new'
            WHEN src.record_hash != ex.record_hash THEN 'changed'
            ELSE 'unchanged'
        END AS change_status
    FROM source_data src
    LEFT JOIN existing_records ex
    ON src.Age = ex.Age
),
-- Filter only new or changed records
new_or_changed_records AS (
    SELECT *
    FROM updated_records
    WHERE change_status IN ('new', 'changed')
)
-- Final dimension table logic
SELECT
    COALESCE(ex.Age_SK, src.Age_SK) AS Age_SK,
    src.Age,
    src.Age_Group_1,
    src.Age_Group_2,
    CASE
        WHEN ex.Age_SK IS NULL THEN CURRENT_DATETIME
        ELSE ex.Created_dt_ts
    END AS Created_dt_ts,
    CASE
        WHEN ex.Age_SK IS NULL THEN CURRENT_DATETIME
        ELSE CURRENT_DATETIME
    END AS Updated_dt_ts,
    CASE
        WHEN ex.Age_SK IS NULL THEN CAST(NULL AS STRING)
        ELSE CAST(ex.Created_By as STRING)
    END AS Created_By,
    CAST(NULL AS STRING) AS Updated_By
FROM new_or_changed_records src
LEFT JOIN existing_records ex
ON src.Age = ex.Age

{% endif %}
