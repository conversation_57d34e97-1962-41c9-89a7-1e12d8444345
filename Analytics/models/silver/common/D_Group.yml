version: 2

models:
  - name: D_Group
    description: |
      Purpose:
      This dimension table maintains group information with Type 2 SCD handling,
      tracking historical changes in group attributes and maintaining temporal validity.

      Data Grain:
      One record per Group_Id per validity period, plus one default record.

      Direct Sources:
      - [V_Group_DW](/#!/model/model.Analytics.V_Group_DW) (view containing group data with temporal validity)
      - T_Batch_Control (batch processing dates for MxDataMartDailyBuild)

      Indirect Sources:
      - [mx_insurance_Group](/#!/model/model.Analytics.mx_insurance_Group) (via V_Group_DW)
      - [mx_insurance_GroupAddress](/#!/model/model.Analytics.mx_insurance_GroupAddress) (via V_Group_DW)

      Key Business Rules:
      - Maintains SCD Type 2 history with effective dates
      - Includes default record with Group_SK = "-1"
      - Only one current record per Group_Id
      - Uses batch control dates for temporal validity

    config:
      materialized: incremental
      unique_key: Group_SK
      tags: ['silver', 'common', 'dimension', 'exclude_when_incremental']

    transformations:
      - name: initial_load
        description: |
          Initial data load process when is_incremental() is false:
          - Extracts all records from V_Group_DW
          - Sets future_proof_date for Effective_To_Dt
          - Adds default "Not Applicable" record with past_init_date
          - Sets initial Current_Flag and audit timestamps

      - name: incremental_update
        description: |
          Incremental update process with SCD Type 2 handling:
          - Identifies changed records using Group_SK comparison
          - Updates effective dates for expired versions
          - Inserts new versions for changed records
          - Maintains Current_Flag for active records
        steps:
          - name: source_data_extraction
            description: "Extracts current group data from V_Group_DW"

          - name: effective_date_calculation
            description: "Calculates effective dates using batch control date"

          - name: version_management
            description: |
              Handles SCD Type 2 versioning:
              - Deactivates outdated versions
              - Creates new versions for changes
              - Updates effective dates

    columns:
      # Key Columns
      - name: Group_SK
        description: "Surrogate key for the group"
        type: string
        source_column: "Group_SK"
        source_table: "V_Group_DW"

      - name: Group_Id
        description: "Business identifier for the group"
        type: integer
        source_column: "Group_Id"
        source_table: "V_Group_DW"

      # Business Attributes
      - name: Group_External_Id
        description: "External identifier for the group"
        type: string
        source_column: "Group_External_Id"
        source_table: "V_Group_DW"

      - name: Group_Name
        description: "Name of the group"
        type: string
        source_column: "Group_Name"
        source_table: "V_Group_DW"

      - name: Group_Frozen_Flag
        description: "Flag indicating if the group is frozen"
        type: integer
        source_column: "Group_Frozen_Flag"
        source_table: "V_Group_DW"

      - name: Group_Active
        description: "Flag indicating if the group is active"
        type: integer
        source_column: "Group_Active"
        source_table: "V_Group_DW"

      - name: Termination_Date
        description: "Date when the group was terminated"
        type: date
        source_column: "Termination_Date"
        source_table: "V_Group_DW"

      # Address Attributes
      - name: Address_Line_1
        description: "First line of group address"
        type: string
        source_column: "Address_Line_1"
        source_table: "V_Group_DW"

      - name: Address_Line_2
        description: "Second line of group address"
        type: string
        source_column: "Address_Line_2"
        source_table: "V_Group_DW"

      - name: City_Name
        description: "City name of group address"
        type: string
        source_column: "City_Name"
        source_table: "V_Group_DW"

      - name: State_Cd
        description: "State code of group address"
        type: string
        source_column: "State_Cd"
        source_table: "V_Group_DW"

      - name: Postal_Cd
        description: "Postal code of group address"
        type: string
        source_column: "Postal_Cd"
        source_table: "V_Group_DW"

      # SCD Type 2 Attributes
      - name: Current_Flag
        description: "Flag indicating if this is the current version"
        type: integer
        transformation_logic: >
          CASE
            WHEN d.Group_SK IN (SELECT Group_SK_to_deactivate FROM rows_to_deactivate) THEN 0
            ELSE 1
          END

      - name: Effective_From_Dt
        description: "Date when this version becomes effective"
        type: date
        source_column: "Effective_From_Dt"
        source_table: "V_Group_DW"

      - name: Effective_To_Dt
        description: "Date when this version expires"
        type: date
        transformation_logic: >
          CASE
            WHEN d.Group_SK IN (SELECT Group_SK_to_deactivate FROM rows_to_deactivate) THEN (select effective_to_date.Effective_To_Dt from effective_to_date)
            ELSE DATE('{{ var("future_proof_date") }}')
          END

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        source_column: "Created_dt_ts"
        source_table: "V_Group_DW"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        source_column: "Updated_dt_ts"
        source_table: "V_Group_DW"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_Group_DW" # [V_Group_DW](/#!/model/model.Analytics.V_Group_DW)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "table"
      temporal_type: "scd_type_2"
      refresh_frequency: "incremental"