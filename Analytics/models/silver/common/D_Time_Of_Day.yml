version: 2

models:
  - name: D_Time_Of_Day
    description: |
      Purpose:
      This dimension table generates a complete set of minute-level time references for a 24-hour period,
      providing standardized time intervals for temporal analysis and reporting.

      Data Grain:
      One record per minute in a 24-hour period (1440 records total).

      Direct Sources:
      - Generated data using GENERATE_ARRAY functions

      Indirect Sources:
      - None

      Key Business Rules:
      - Covers full 24-hour period (00:00:00 to 23:59:00)
      - Times formatted as HH:MM:00
      - Hour intervals formatted as HH:00:00 - HH:59:00
      - Surrogate keys generated from time values

    config:
      materialized: table
      tags: ['silver', 'common', 'dimension']

    transformations:
      - name: time_generation
        description: |
          Generates time dimension data:
          - Creates array of hours (0-23)
          - Creates array of minutes (0-59)
          - <PERSON> joins hours and minutes for all combinations
          - Formats time values in consistent HH:MM:00 pattern
          - Creates hour interval ranges

      - name: surrogate_key_generation
        description: |
          Generates unique surrogate keys:
          - Uses dbt_utils.generate_surrogate_key function
          - Based on time value for consistency
          - Ensures uniqueness across all records

    columns:
      # Key Columns
      - name: Time_Of_Day_SK
        description: "Surrogate key for the time of day"
        type: string
        transformation_logic: "dbt_utils.generate_surrogate_key(['time'])"

      # Time Attributes
      - name: Time
        description: "Time of day in HH:MM:00 format"
        type: string
        transformation_logic: "FORMAT('%02d:%02d:00', hour, minute)"

      - name: Hour_Interval
        description: "Hour range containing this time (HH:00:00 - HH:59:00)"
        type: string
        transformation_logic: "FORMAT('%02d:00:00 - %02d:59:00', hour, hour)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on: []
      table_type: "table"
      temporal_type: "static"
      refresh_frequency: "static"