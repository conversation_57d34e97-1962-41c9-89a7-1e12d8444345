{{ config( 
          materialized='incremental'
        , unique_key='Event_SK'
        , tags = ['exclude_when_incremental']
      ) 
}}

{% if not is_incremental() %}

SELECT         
        Event_SK
        ,Event_Name
        ,Event_Category_1
        ,Event_Category_2
        ,Event_Action
        ,Event_Action_Qualifier
        ,current_datetime() as Created_dt_ts
        ,current_datetime() as Updated_dt_ts
        ,CAST(NULL AS STRING) as Created_By
        ,CAST(NULL AS STRING) as Updated_By
FROM {{ ref('V_MP_Events_DW') }}

{% else %}

WITH source_data AS (
    SELECT
        Event_SK
        ,Event_Name
        ,Event_Category_1
        ,Event_Category_2
        ,Event_Action
        ,Event_Action_Qualifier
        ,current_datetime() as Created_dt_ts
        ,current_datetime() as Updated_dt_ts
        ,CAST(NULL AS STRING) as Created_By
        ,CAST(NULL AS STRING) as Updated_By
        ,MD5(CONCAT('|',
            Event_Name
            ,Event_Category_1
            ,Event_Category_2
            ,Event_Action
            ,Event_Action_Qualifier
        )) AS record_hash
    FROM {{ ref('V_MP_Events_DW') }}
),
-- Identify existing records in the dimension table
existing_records AS (
    SELECT
        Event_SK
        ,Event_Name
        ,Event_Category_1
        ,Event_Category_2
        ,Event_Action
        ,Event_Action_Qualifier
        ,Created_By
        ,Updated_By
        ,Created_dt_ts
        ,Updated_dt_ts
        ,MD5(CONCAT('|',
            Event_Name
            ,Event_Category_1
            ,Event_Category_2
            ,Event_Action
            ,Event_Action_Qualifier
        )) AS record_hash
    FROM {{this}}
),
-- Find changed or new records
updated_records AS (
    SELECT
        src.Event_Name
        ,src.Event_Category_1
        ,src.Event_Category_2
        ,src.Event_Action
        ,src.Event_Action_Qualifier
        ,src.Event_SK
        ,src.Created_dt_ts
        ,src.Created_By
        ,CASE
            WHEN ex.Event_SK IS NULL THEN 'new'
            WHEN src.record_hash != ex.record_hash THEN 'changed'
            ELSE 'unchanged'
        END AS change_status
    FROM source_data src
    LEFT JOIN existing_records ex
    ON src.Event_Name = ex.Event_Name
    AND src.Event_Category_1 = ex.Event_Category_1
    AND src.Event_Category_2 = ex.Event_Category_2
    AND src.Event_Action = ex.Event_Action
    AND src.Event_Action_Qualifier = ex.Event_Action_Qualifier
),
-- Filter only new or changed records
new_or_changed_records AS (
    SELECT *
    FROM updated_records
    WHERE change_status IN ('new', 'changed')
)
-- Final dimension table logic
SELECT
    COALESCE(ex.Event_SK, src.Event_SK) AS Event_SK
    ,src.Event_Name
    ,src.Event_Category_1
    ,src.Event_Category_2
    ,src.Event_Action
    ,src.Event_Action_Qualifier
    ,CASE
        WHEN ex.Event_SK IS NULL THEN CURRENT_DATETIME
        ELSE ex.Created_dt_ts
    END AS Created_dt_ts
    ,CASE
        WHEN ex.Event_SK IS NULL THEN CURRENT_DATETIME
        ELSE CURRENT_DATETIME
    END AS Updated_dt_ts
    ,CASE
        WHEN ex.Event_SK IS NULL THEN CAST(NULL AS STRING)
        ELSE CAST(ex.Created_By as STRING)
    END AS Created_By
    ,CAST(NULL AS STRING) AS Updated_By
FROM new_or_changed_records src
LEFT JOIN existing_records ex
ON src.Event_Name = ex.Event_Name
AND src.Event_Category_1 = ex.Event_Category_1
AND src.Event_Category_2 = ex.Event_Category_2
AND src.Event_Action = ex.Event_Action
AND src.Event_Action_Qualifier = ex.Event_Action_Qualifier

{% endif %}
