{{ config(
    materialized='view',
    tags = ["MxDataMartDailyBuild"]
    ) 
}}

SELECT 
  Partner_SK as Billing_Partner_SK,
  Partner_Id as Billing_Partner_Id,
  Partner_Name AS Billing_Partner_Name,
  Partner_Type,
  Partner_Active AS Billing_Partner_Active,
  Effective_From_Dt,
  Effective_To_Dt,
  Current_Flag,
  Created_dt_ts,
  Created_By,
  Updated_dt_ts,
  Updated_By

FROM {{ ref('D_Partner') }}

WHERE Partner_Type = 'Billing'
AND Partner_Active = 1

UNION ALL

SELECT 
  Partner_SK as Billing_Partner_SK,
  Partner_Id as Billing_Partner_Id,
  Partner_Name AS Billing_Partner_Name,
  "Billing" as Partner_Type,
  Partner_Active AS Billing_Partner_Active,
  Effective_From_Dt,
  Effective_To_Dt,
  Current_Flag,
  Created_dt_ts,
  Created_By,
  Updated_dt_ts,
  Updated_By

FROM {{ ref('D_Partner') }}

WHERE Partner_SK = "-1"