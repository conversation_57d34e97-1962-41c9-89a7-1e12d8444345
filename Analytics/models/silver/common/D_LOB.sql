{{ config( 
          materialized='incremental'
        , unique_key='LOB_SK'
        , tags = ['exclude_when_incremental']
      ) 
}}

{% if not is_incremental() %}

SELECT         
        LOB_SK,
        LOB_Cd,
        LOB_Name,
        current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By
FROM {{ ref('V_LOB_DW') }}

UNION ALL

SELECT         
        "-1" AS LOB_SK,
        "Not Applicable" AS LOB_Cd,
        "Not Applicable" AS LOB_Name,
        current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By

{% else %}

WITH source_data AS (
    SELECT
        LOB_SK,
        LOB_Cd,
        LOB_Name,
        current_datetime() as Created_dt_ts,
        current_datetime() as Updated_dt_ts,
        CAST(NULL AS STRING) as Created_By,
        CAST(NULL AS STRING) as Updated_By,
        MD5(CONCAT('|',
            LOB_Cd,
            LOB_Name
        )) AS record_hash
    FROM {{ ref('V_LOB_DW') }}
),
-- Identify existing records in the dimension table
existing_records AS (
    SELECT
        LOB_SK,
        LOB_Cd,
        LOB_Name,
        Created_By,
        Updated_By,
        Created_dt_ts,
        Updated_dt_ts,
        MD5(CONCAT('|',
            LOB_Cd,
            LOB_Name
        )) AS record_hash
    FROM {{this}}
),
-- Find changed or new records
updated_records AS (
    SELECT
        src.LOB_Cd,
        src.LOB_Name,
        src.LOB_SK,
        src.Created_dt_ts,
        src.Created_By,
        CASE
            WHEN ex.LOB_SK IS NULL THEN 'new'
            WHEN src.record_hash != ex.record_hash THEN 'changed'
            ELSE 'unchanged'
        END AS change_status
    FROM source_data src
    LEFT JOIN existing_records ex
    ON src.LOB_Cd = ex.LOB_Cd
),
-- Filter only new or changed records
new_or_changed_records AS (
    SELECT *
    FROM updated_records
    WHERE change_status IN ('new', 'changed')
)
-- Final dimension table logic
SELECT
    COALESCE(ex.LOB_SK, src.LOB_SK) AS LOB_SK,
    src.LOB_Cd,
    src.LOB_Name,
    CASE
        WHEN ex.LOB_SK IS NULL THEN CURRENT_DATETIME
        ELSE ex.Created_dt_ts
    END AS Created_dt_ts,
    CASE
        WHEN ex.LOB_SK IS NULL THEN CURRENT_DATETIME
        ELSE CURRENT_DATETIME
    END AS Updated_dt_ts,
    CASE
        WHEN ex.LOB_SK IS NULL THEN CAST(NULL AS STRING)
        ELSE CAST(ex.Created_By as STRING)
    END AS Created_By,
    CAST(NULL AS STRING) AS Updated_By
FROM new_or_changed_records src
LEFT JOIN existing_records ex
ON src.LOB_Cd = ex.LOB_Cd

{% endif %}
