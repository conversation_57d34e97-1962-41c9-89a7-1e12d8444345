{{ config(
    materialized='view',
    tags = ["MxDataMartDailyBuild"]
    ) 
}}

SELECT 
  Partner_SK as Auth_Partner_SK,
  Partner_Id as Auth_Partner_Id,
  Partner_Name AS Auth_Partner_Name,
  Partner_Type,
  Partner_Active AS Auth_Partner_Active,
  Effective_From_Dt,
  Effective_To_Dt,
  Current_Flag,
  Created_dt_ts,
  Created_By,
  Updated_dt_ts,
  Updated_By

FROM {{ ref('D_Partner') }}

WHERE Partner_Type = 'Auth'

UNION ALL

SELECT 
  Partner_SK as Auth_Partner_SK,
  Partner_Id as Auth_Partner_Id,
  Partner_Name AS Auth_Partner_Name,
  "Auth" as Partner_Type,
  Partner_Active AS Auth_Partner_Active,
  Effective_From_Dt,
  Effective_To_Dt,
  Current_Flag,
  Created_dt_ts,
  Created_By,
  Updated_dt_ts,
  Updated_By

FROM {{ ref('D_Partner') }}

WHERE Partner_SK = "-1"