version: 2

models:
  - name: F_Event_Metrics
    description: |
      Silver layer fact table containing member event metrics with dimensional surrogate keys.
      
      Purpose: Serves as the primary fact table for member event analysis, tracking user interactions
      and engagement metrics across the platform.
      
      Grain: One row per unique event (Event_Id) per event timestamp
      
      Direct Sources:
      - [V_Master_Event_DW](/#!/model/model.Analytics.V_Master_Event_DW)
      - [D_Event](/#!/model/model.Analytics.D_Event)
      - [D_Group](/#!/model/model.Analytics.D_Group)
      - [V_Billing_Partner](/#!/model/model.Analytics.V_Billing_Partner)
      - [D_LOB](/#!/model/model.Analytics.D_LOB)
      - [D_TPA](/#!/model/model.Analytics.D_TPA)
      - [D_Event_Region](/#!/model/model.Analytics.D_Event_Region)
      - [D_Time_Of_Day](/#!/model/model.Analytics.D_Time_Of_Day)
      
      Key Business Rules:
      - Pre-hook deletes existing records for the batch date
      - Uses batch date from T_Batch_Control for MxDataMartDailyBuild
      - Handles missing dimension keys by assigning "-1" (unknown member)
      - Tracks both terminated and active members

    config:
      materialized: incremental
      unique_key: ['Event_Id']
      tags: ["MxDataMartDailyBuild"]

    transformations:
      - name: dimension_key_assignment
        description: |
          Assigns surrogate keys from dimension tables with default handling:
          - Uses CASE statements to assign "-1" for unknown members
          - Ensures temporal alignment between fact and dimension effective dates
          - Maintains referential integrity with dimension tables
        joins:
          - join: D_Event
            type: inner
            relationship: many_to_one
            sql: |
              me.Event_Name = de.Event_Name
              AND me.Event_Category_1 = de.Event_Category_1
              AND me.Event_Category_2 = de.Event_Category_2
              AND me.Event_Action = de.Event_Action
              AND me.Event_Action_Qualifier = de.Event_Action_Qualifier
          
          - join: D_Group
            type: left
            relationship: many_to_one
            sql: |
              me.Group_Id = dg.Group_Id
              AND dg.Effective_From_Dt <= DATE(me.Event_Time)
              AND dg.Effective_To_Dt >= DATE(me.Event_Time)
          
          - join: V_Billing_Partner
            type: left
            relationship: many_to_one
            sql: |
              me.Billing_Partner_Id = vbp.Billing_Partner_Id
              AND vbp.Effective_From_Dt <= DATE(me.Event_Time)
              AND vbp.Effective_To_Dt >= DATE(me.Event_Time)

    columns:
      # Surrogate Keys
      - name: Group_SK
        description: "Surrogate key for the group dimension"
        data_type: string
        source_column: Group_SK
        source_table: D_Group
        transformation_logic: "CASE WHEN dg.Group_SK IS NULL THEN '-1' ELSE dg.Group_SK END"

      - name: Billing_Partner_SK
        description: "Surrogate key for the billing partner dimension"
        data_type: string
        source_column: Billing_Partner_SK
        source_table: V_Billing_Partner
        transformation_logic: "CASE WHEN vbp.Billing_Partner_SK IS NULL THEN '-1' ELSE vbp.Billing_Partner_SK END"

      - name: LOB_SK
        description: "Surrogate key for the line of business dimension"
        data_type: string
        source_column: LOB_SK
        source_table: D_LOB
        transformation_logic: "CASE WHEN lob.LOB_SK IS NULL THEN '-1' ELSE lob.LOB_SK END"

      # Event Attributes
      - name: Event_Id
        description: "Unique identifier for the event"
        data_type: string
        source_column: Event_Id
        source_table: V_Master_Event_DW

      - name: Session_Id
        description: "Unique identifier for the user session"
        data_type: string
        source_column: sessionid
        source_table: V_Master_Event_DW

      - name: Session_Length_Mins
        description: "Duration of the session in minutes"
        data_type: integer
        source_column: session_length
        source_table: V_Master_Event_DW

      # User Attributes
      - name: User_Id
        description: "Unique identifier for the user"
        data_type: string
        source_column: userid
        source_table: V_Master_Event_DW

      - name: Elig_Id
        description: "Eligibility identifier for the member"
        data_type: string
        source_column: Elig_Id
        source_table: V_Master_Event_DW

      # Temporal Attributes
      - name: Event_Time
        description: "Timestamp when the event occurred"
        data_type: timestamp
        source_column: Event_Time
        source_table: V_Master_Event_DW

      - name: Days_Since_Registered
        description: "Number of days since user registration"
        data_type: integer
        source_column: Days_Since_Registered
        source_table: V_Master_Event_DW

      - name: Days_Since_Terminated
        description: "Number of days since user termination"
        data_type: integer
        source_column: Days_Since_Terminated
        source_table: V_Master_Event_DW

      # Audit Columns
      - name: As_Of_Date
        description: "Batch processing date"
        data_type: date
        transformation_logic: "SELECT batch_date FROM batch_date"

      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        data_type: timestamp
        transformation_logic: "current_datetime()"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        data_type: timestamp
        transformation_logic: "current_datetime()"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2024-03-19"
      depends_on:
        - "V_Master_Event_DW"      # [V_Master_Event_DW](/#!/model/model.Analytics.V_Master_Event_DW)
        - "D_Event"                # [D_Event](/#!/model/model.Analytics.D_Event)
        - "D_Group"                # [D_Group](/#!/model/model.Analytics.D_Group)
        - "V_Billing_Partner"      # [V_Billing_Partner](/#!/model/model.Analytics.V_Billing_Partner)
        - "D_LOB"                  # [D_LOB](/#!/model/model.Analytics.D_LOB)
        - "D_TPA"                  # [D_TPA](/#!/model/model.Analytics.D_TPA)
        - "D_Event_Region"         # [D_Event_Region](/#!/model/model.Analytics.D_Event_Region)
        - "D_Time_Of_Day"          # [D_Time_Of_Day](/#!/model/model.Analytics.D_Time_Of_Day)
      table_type: "table"
      temporal_type: "scd_type_1"
      refresh_frequency: "incremental"