version: 2

models:
  - name: V_F_Member_Metrics_L3_Counts
    description: |
      Purpose:
      This view aggregates member metrics to provide counts of subscribers and dependents,
      including registration status and re-engagement metrics at various time intervals
      (daily, MTD, YTD, LTD).

      Data Grain:
      One record per metric type per group and billing partner combination.

      Direct Sources:
      - [V_F_Member_Metrics_L3](/#!/model/model.Analytics.V_F_Member_Metrics_L3) (base member metrics)
      - [D_Metric](/#!/model/model.Analytics.D_Metric) (metric definitions)
      - T_Batch_Control (processing control dates)

      Indirect Sources:
      - [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2) (through L3)
      - [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1) (through L2)

      Key Business Rules:
      - Filters out duplicate SSNs within same Group and Billing Partner
      - Separates subscriber and dependent metrics
      - Uses batch control date for temporal validity
      - <PERSON>les special case SSN '999999999' separately

    config:
      materialized: view
      tags: ['silver', 'member', 'view', 'MxDataMartDailyBuild']

    transformations:
      - name: metric_selection
        description: |
          Selects relevant metrics from D_Metric based on batch date:
          - Total subscriber and dependent counts
          - Registered subscriber and dependent counts
          - Re-engagement metrics (Daily, MTD, YTD, LTD)
          - Unique and total engagement counts

      - name: distinct_member_processing
        description: |
          Processes distinct members based on SSN:
          - Handles subscribers and dependents separately
          - Special handling for SSN '999999999'
          - Deduplicates based on Group and Billing Partner
          - Filters subscribers where Subscriber_SSN != '999999999'
          - Deduplicates using ROW_NUMBER() over SSN, Group, Partner
          - Unions with '999999999' SSN records
          - Repeats process for dependents

      - name: registration_status
        description: |
          Processes registration status metrics:
          - Filters for REGISTERED_ELIG = 1
          - Maintains distinct member handling
          - Separates subscriber and dependent counts

      - name: reengagement_metrics
        description: |
          Calculates re-engagement metrics:
          - Daily total and unique counts
          - Month-to-date aggregations
          - Year-to-date unique counts
          - Lifetime-to-date unique counts
          - Separate tracking for subscribers and dependents

    columns:
      # Key Columns
      - name: Group_Id
        description: "Identifier for the group"
        type: string
        source_column: "Group_Id"
        source_table: "V_F_Member_Metrics_L3"

      - name: Billing_Partner_Id
        description: "Identifier for the billing partner"
        type: string
        source_column: "Billing_Partner_Id"
        source_table: "V_F_Member_Metrics_L3"

      # Metric Columns
      - name: Metric_SK
        description: "Surrogate key for the metric"
        type: integer
        source_column: "Metric_SK"
        source_table: "D_Metric"

      - name: Metric_Value
        description: "Calculated value for the metric"
        type: integer
        transformation_logic: "COUNT() or COUNT(DISTINCT) based on metric type"

      # Temporal Columns
      - name: Effective_Date
        description: "Date for which the metrics are effective"
        type: date
        source_column: "batch_control_dt"
        source_table: "T_Batch_Control"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_F_Member_Metrics_L3" # [V_F_Member_Metrics_L3](/#!/model/model.Analytics.V_F_Member_Metrics_L3)
        - "D_Metric" # [D_Metric](/#!/model/model.Analytics.D_Metric)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"
