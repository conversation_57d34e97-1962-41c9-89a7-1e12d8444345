{{ config(
    materialized='view'
) }}


WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)


,d_metric as (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Billable_Cnt"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)

,distinct_ssn as (
  SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Subscriber_SSN <> '999999999'
    AND SUBSCRIBER_CNT = 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Subscriber_SSN,Group_Id,Billing_Partner_Id ORDER BY Member_Number DESC) = 1
    
)

SELECT 

 CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
    ELSE vbp.Billing_Partner_SK
    END AS Billing_Partner_SK
,CASE WHEN dg.Group_SK IS NULL THEN "-1"
    ELSE dg.Group_SK
    END AS Group_SK
,(SELECT d_metric.Metric_SK FROM d_metric) as Metric_SK
,l3.As_Of_Date
,COUNT(DISTINCT CONCAT(l3.Member_Number,'|',l3.Person_Number,'|',l3.Elig_Group_Number)) as Billing_Count

FROM distinct_ssn l3

LEFT JOIN {{ ref('D_Group') }} dg
ON l3.Group_Id = dg.Group_Id
AND dg.Effective_From_Dt <= l3.As_Of_Date
AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
AND vbp.Effective_From_Dt <= l3.As_Of_Date
AND vbp.Effective_To_Dt >= l3.As_Of_Date

GROUP BY 
vbp.Billing_Partner_SK
,dg.Group_SK
,l3.As_Of_Date
,Metric_SK

UNION ALL

SELECT 

 CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
    ELSE vbp.Billing_Partner_SK
    END AS Billing_Partner_SK
,CASE WHEN dg.Group_SK IS NULL THEN "-1"
    ELSE dg.Group_SK
    END AS Group_SK
,(SELECT d_metric.Metric_SK FROM d_metric) as Metric_SK
,l3.As_Of_Date
,COUNT(DISTINCT CONCAT(l3.Member_Number,'|',l3.Person_Number,'|',l3.Elig_Group_Number)) as Billing_Count

FROM {{ ref('V_F_Member_Metrics_L3') }} l3

LEFT JOIN {{ ref('D_Group') }} dg
ON l3.Group_Id = dg.Group_Id
AND dg.Effective_From_Dt <= l3.As_Of_Date
AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
AND vbp.Effective_From_Dt <= l3.As_Of_Date
AND vbp.Effective_To_Dt >= l3.As_Of_Date

WHERE Subscriber_SSN = '999999999'
AND SUBSCRIBER_CNT = 1

GROUP BY 
vbp.Billing_Partner_SK
,dg.Group_SK
,l3.As_Of_Date
,Metric_SK