version: 2

models:
  - name: F_Member_Metrics
    description: |
      Purpose:
      This fact table consolidates member metrics from billing and count sources,
      providing a unified view of member-related metrics across different dimensions
      including plans, groups, partners, and demographics.

      Data Grain:
      One record per unique combination of dimension keys (Plan, Group, Partners, State, Age, TPA, LOB)
      per metric type per as-of date.

      Direct Sources:
      - [V_F_Member_Metrics_L3_Billing](/#!/model/model.Analytics.V_F_Member_Metrics_L3_Billing) (billing metrics)
      - [V_F_Member_Metrics_L3_Counts](/#!/model/model.Analytics.V_F_Member_Metrics_L3_Counts) (member count metrics)
      - T_Batch_Control (processing control dates)

      Indirect Sources:
      - [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2) (through L3 views)
      - [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1) (through L2)

      Key Business Rules:
      - Pre-deletes existing records for the current batch date
      - Combines billing and member count metrics
      - Uses default SK (-1) for undefined dimension relationships
      - Maintains audit columns for tracking

    config:
      materialized: incremental
      unique_key: ['Plan_SK', 'Group_SK', 'Billing_Partner_SK', 'Engage_Partner_SK',
                  'State_SK', 'Age_SK', 'TPA_SK', 'LOB_SK', 'Metric_SK', 'As_Of_Date']
      tags: ['silver', 'member', 'fact', 'MxDataMartDailyBuild']

    transformations:
      - name: batch_date_processing
        description: |
          Retrieves and processes the current batch date:
          - Gets batch_control_dt from T_Batch_Control
          - Uses MxDataMartDailyBuild batch_id
          - Pre-deletes existing records for the batch date

      - name: billing_metrics_processing
        description: |
          Processes billing-related metrics:
          - Uses default (-1) SK for undefined dimensions
          - Includes Group and Billing Partner relationships
          - Sets current timestamp for audit columns
        joins:
          - join: V_F_Member_Metrics_L3_Billing
            type: source
            relationship: one_to_one

      - name: member_count_processing
        description: |
          Processes member count metrics:
          - Includes Age and LOB dimension relationships
          - Uses default (-1) SK for undefined dimensions
          - Sets current timestamp for audit columns
        joins:
          - join: V_F_Member_Metrics_L3_Counts
            type: source
            relationship: one_to_one

    columns:
      # Key Columns
      - name: Plan_SK
        description: "Surrogate key for plan dimension"
        type: string
        transformation_logic: "Default to '-1' for undefined relationships"

      - name: Group_SK
        description: "Surrogate key for group dimension"
        type: string
        source_column: "Group_SK"
        source_table: ["V_F_Member_Metrics_L3_Billing", "V_F_Member_Metrics_L3_Counts"]

      - name: Billing_Partner_SK
        description: "Surrogate key for billing partner dimension"
        type: string
        source_column: "Billing_Partner_SK"
        source_table: ["V_F_Member_Metrics_L3_Billing", "V_F_Member_Metrics_L3_Counts"]

      - name: Engage_Partner_SK
        description: "Surrogate key for engagement partner dimension"
        type: string
        transformation_logic: "Default to '-1' for undefined relationships"

      - name: State_SK
        description: "Surrogate key for state dimension"
        type: string
        transformation_logic: "Default to '-1' for undefined relationships"

      - name: Age_SK
        description: "Surrogate key for age dimension"
        type: string
        source_column: "Age_SK"
        source_table: "V_F_Member_Metrics_L3_Counts"

      - name: TPA_SK
        description: "Surrogate key for TPA dimension"
        type: string
        transformation_logic: "Default to '-1' for undefined relationships"

      - name: LOB_SK
        description: "Surrogate key for line of business dimension"
        type: string
        source_column: "LOB_SK"
        source_table: "V_F_Member_Metrics_L3_Counts"

      # Metric Columns
      - name: Metric_SK
        description: "Surrogate key identifying the type of metric"
        type: string
        source_column: "Metric_SK"
        source_table: ["V_F_Member_Metrics_L3_Billing", "V_F_Member_Metrics_L3_Counts"]

      - name: Metric_Value
        description: "Numeric value of the metric"
        type: integer
        source_column: ["Billing_Count", "Metric_Value"]
        source_table: ["V_F_Member_Metrics_L3_Billing", "V_F_Member_Metrics_L3_Counts"]

      - name: As_Of_Date
        description: "Date for which the metrics are calculated"
        type: date
        source_column: "As_Of_Date"
        source_table: ["V_F_Member_Metrics_L3_Billing", "V_F_Member_Metrics_L3_Counts"]

      # Audit Columns
      - name: Created_dt_ts
        description: "Timestamp when the record was created"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Created_By
        description: "User who created the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

      - name: Updated_dt_ts
        description: "Timestamp when the record was last updated"
        type: timestamp
        transformation_logic: "CURRENT_DATETIME()"

      - name: Updated_By
        description: "User who last updated the record"
        type: string
        transformation_logic: "CAST(NULL AS STRING)"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_F_Member_Metrics_L3_Billing" # [V_F_Member_Metrics_L3_Billing](/#!/model/model.Analytics.V_F_Member_Metrics_L3_Billing)
        - "V_F_Member_Metrics_L3_Counts" # [V_F_Member_Metrics_L3_Counts](/#!/model/model.Analytics.V_F_Member_Metrics_L3_Counts)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "table"
      temporal_type: "snapshot"
      refresh_frequency: "daily"