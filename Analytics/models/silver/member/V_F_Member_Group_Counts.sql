{{ config(
    materialized='view'
) }}

WITH distinct_ssn_subs AS (
    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Subscriber_SSN <> '999999999'
    AND SUBSCRIBER_CNT = 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Subscriber_SSN, Group_Id, Billing_Partner_Id ORDER BY Member_Number DESC) = 1

    UNION ALL

    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Subscriber_SSN = '999999999'
    AND SUBSCRIBER_CNT = 1
),

distinct_ssn_deps AS (
    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Member_SSN <> '999999999'
    AND SUBSCRIBER_CNT <> 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Member_SSN, Group_Id, Billing_Partner_Id ORDER BY Member_Number DESC) = 1

    UNION ALL

    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Member_SSN = '999999999'
    AND SUBSCRIBER_CNT <> 1
)

SELECT 
    subs.Group_Id,
    COUNT(DISTINCT CONCAT(subs.Member_Number,'|',subs.Person_Number,'|',subs.Elig_Group_Number)) as Total_Elig_Subscriber_Cnt,
    COUNT(DISTINCT CONCAT(deps.Member_Number,'|',deps.Person_Number,'|',deps.Elig_Group_Number)) as Total_Elig_Dependent_Cnt
FROM distinct_ssn_subs subs
LEFT JOIN distinct_ssn_deps deps
    ON subs.Group_Id = deps.Group_Id
    AND subs.As_Of_Date = deps.As_Of_Date
GROUP BY 
    subs.Group_Id