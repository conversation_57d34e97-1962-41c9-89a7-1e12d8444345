{{ 
    config(
        materialized='incremental',
        unique_key=['Event_Id'],
        pre_hook="""
                DECLARE table_exists BOOL;

                -- Check if the table 'F_Event_Metrics' exists in the dataset
                SET table_exists = (
                    SELECT COUNT(1) > 0
                    FROM `{{ env_var('PROJECT_ID') }}.Silver.INFORMATION_SCHEMA.TABLES`
                    WHERE table_name = 'F_Event_Metrics'
                );

                -- If the table exists, perform the DELETE operation
                IF table_exists THEN
                    BEGIN
                        -- Retrieve the batch date
                        DECLARE batch_date DATE;

                        SET batch_date = (
                            SELECT CAST(batch_control_dt AS DATE)
                            FROM `{{ env_var('PROJECT_ID') }}.Warehouse.T_Batch_Control`
                            WHERE batch_id = 'MxDataMartDailyBuild'
                        );

                        -- Delete data for both the batch date and the previous day
                        DELETE FROM `{{ env_var('PROJECT_ID') }}.Silver.F_Event_Metrics`
                        WHERE DATE(Event_Time) = batch_date 
                           OR DATE(Event_Time) = DATE_SUB(batch_date, INTERVAL 1 DAY);
                    END;
                END IF;
                """
    )
}}

WITH batch_date AS (
    SELECT CAST(batch_control_dt AS DATE) AS batch_date
    FROM `{{ env_var("PROJECT_ID") }}.Warehouse.T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)

SELECT 
    CASE WHEN dg.Group_SK IS NULL THEN "-1"
        ELSE dg.Group_SK
        END AS Group_SK,
    CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
        ELSE vbp.Billing_Partner_SK
        END AS Billing_Partner_SK,
    CASE WHEN lob.LOB_SK IS NULL THEN "-1"
        ELSE lob.LOB_SK
        END AS LOB_SK,
    CASE WHEN dt.TPA_SK IS NULL THEN "-1"
        ELSE dt.TPA_SK
        END AS TPA_SK,
    CASE WHEN dr.Region_SK IS NULL THEN "-1"
        ELSE dr.Region_SK
        END AS Region_SK,
    CASE WHEN dtod.Time_Of_Day_SK IS NULL THEN "-1"
        ELSE dtod.Time_Of_Day_SK
        END AS Time_Of_Day_SK,
    CASE WHEN dmc.Module_Config_SK IS NULL THEN "-1"
        ELSE dmc.Module_Config_SK
        END AS Module_Config_SK,
    CASE WHEN dam.Access_Mode_SK IS NULL THEN "-1"
    ELSE dam.Access_Mode_SK
    END AS Access_Mode_SK,
    CASE WHEN de.Event_SK IS NULL THEN "-1"
    ELSE de.Event_SK
    END AS Event_SK,
    me.Event_Id,
    me.sessionid AS Session_Id,
    me.session_length AS Session_Length_Mins,
    me.Termination_Status,
    me.Days_Since_Registered,
    me.Days_Since_Terminated,
    me.Event_Time,
    me.Elig_Group_Number AS Group_Number,
    me.name AS Raw_Name,
    me.title AS Raw_Title,
    me.userid AS User_Id,
    me.Elig_Id,
    me.Member_Type,
    1 AS Event_Count,
    DATE(me.Event_Time) AS As_Of_Date,
    current_datetime() AS Created_dt_ts,
    current_datetime() AS Updated_dt_ts,
    CAST(NULL AS STRING) AS Created_By,
    CAST(NULL AS STRING) AS Updated_By

FROM {{ ref('V_Master_Event_DW') }} me

JOIN {{ ref('D_Event') }} de
    ON me.Event_Name = de.Event_Name
    AND me.Event_Category_1 = de.Event_Category_1
    AND me.Event_Category_2 = de.Event_Category_2
    AND me.Event_Action = de.Event_Action
    AND me.Event_Action_Qualifier = de.Event_Action_Qualifier

LEFT JOIN {{ ref('D_Group') }} dg
    ON me.Group_Id = dg.Group_Id
    AND dg.Effective_From_Dt <= DATE(me.Event_Time)
    AND dg.Effective_To_Dt >= DATE(me.Event_Time)

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
    ON me.Billing_Partner_Id = vbp.Billing_Partner_Id
    AND vbp.Effective_From_Dt <= DATE(me.Event_Time)
    AND vbp.Effective_To_Dt >= DATE(me.Event_Time)

LEFT JOIN {{ ref('D_LOB') }} lob
    ON me.Lob_Cd = lob.Lob_Cd

LEFT JOIN {{ ref('D_TPA') }} dt
    ON me.TPA_Name = dt.TPA_Name

LEFT JOIN {{ ref('D_Event_Region') }} dr
    ON me.mp_city  = dr.Event_City_Name
    AND me.mp_region = dr.Event_State_Name

LEFT JOIN {{ ref('D_Time_Of_Day') }} dtod
    ON CAST(me.Time_Of_Day AS STRING)= dtod.Time

LEFT JOIN {{ ref('D_Module_Config') }} dmc
    ON me.Module_Config_Id = CAST(dmc.Module_Config_Id AS STRING)
    AND dmc.Effective_From_Dt <= DATE(me.Event_Time)
    AND dmc.Effective_To_Dt >= DATE(me.Event_Time)

LEFT JOIN {{ ref('D_Access_Mode') }} dam
    ON me.mp_os = dam.OS_Name
    AND me.mp_browser = dam.Browser_Name
    AND me.mp_browser_version = dam.Browser_Version
    AND me.Portal_Name = dam.Portal_Name
    AND me.mp_os_version = dam.OS_Version