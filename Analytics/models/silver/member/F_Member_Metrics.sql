{{ 
    config(
        materialized='incremental',
        unique_key=['Plan_SK', 'Group_SK', 'Billing_Partner_SK', 'Engage_Partner_SK', 'State_SK', 'Age_SK', 'TPA_SK', 'LOB_SK', 'Metric_SK', 'As_Of_Date'],
        pre_hook="""
                DECLARE table_exists BOOL;

                -- Check if the table 'F_Member_Metrics' exists in the dataset
                SET table_exists = (
                    SELECT COUNT(1) > 0
                    FROM `{{ env_var('PROJECT_ID') }}.Silver.INFORMATION_SCHEMA.TABLES`
                    WHERE table_name = 'F_Member_Metrics'
                );

                -- If the table exists, perform the DELETE operation
                IF table_exists THEN
                    BEGIN
                        -- Retrieve the batch date
                        DECLARE batch_date DATE;

                        SET batch_date = (
                            SELECT CAST(batch_control_dt AS DATE)
                            FROM `{{ env_var('PROJECT_ID') }}.Warehouse.T_Batch_Control`
                            WHERE batch_id = 'MxDataMartDailyBuild'
                        );

                        DELETE FROM `{{ env_var('PROJECT_ID') }}.Silver.F_Member_Metrics`
                        WHERE As_Of_Date = batch_date;
                    END;
                END IF;

                """
    )
}}

WITH batch_date AS (
    SELECT CAST(batch_control_dt AS DATE) AS batch_date
    FROM `{{ env_var("PROJECT_ID") }}.Warehouse.T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
),


billing_metrics AS (
    SELECT 
        "-1" AS Plan_SK,
        Group_SK,
        Billing_Partner_SK,
        "-1" AS Engage_Partner_SK,
        "-1" AS State_SK,
        "-1" AS Age_SK,
        "-1" AS TPA_SK,
        "-1" AS LOB_SK,
        As_Of_Date,
        Billing_Count AS Metric_Value,
        Metric_SK,
        CURRENT_DATETIME() AS Created_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CURRENT_DATETIME() AS Updated_dt_ts,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref("V_F_Member_Metrics_L3_Billing") }}
    WHERE As_Of_Date = (SELECT batch_date.batch_date FROM batch_date)
),

member_count_metrics AS (
    SELECT 
        "-1" AS Plan_SK,
        Group_SK,
        Billing_Partner_SK,
        "-1" AS Engage_Partner_SK,
        "-1" AS State_SK,
        Age_SK,
        "-1" AS TPA_SK,
        LOB_SK,
        As_Of_Date,
        Metric_Value,
        Metric_SK,
        CURRENT_DATETIME() AS Created_dt_ts,
        CAST(NULL AS STRING) AS Created_By,
        CURRENT_DATETIME() AS Updated_dt_ts,
        CAST(NULL AS STRING) AS Updated_By
    FROM {{ ref("V_F_Member_Metrics_L3_Counts") }}
    WHERE As_Of_Date = (SELECT batch_date.batch_date FROM batch_date)
)


SELECT *
FROM billing_metrics

UNION ALL

SELECT *
FROM member_count_metrics




