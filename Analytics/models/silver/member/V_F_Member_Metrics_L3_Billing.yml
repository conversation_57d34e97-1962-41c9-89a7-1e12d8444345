version: 2

models:
  - name: V_F_Member_Metrics_L3_Billing
    description: |
      Purpose:
      This view calculates billable member counts by combining subscriber and dependent
      information, providing billing metrics at the group and billing partner level.

      Data Grain:
      One record per group per billing partner per as-of date.

      Direct Sources:
      - [V_F_Member_Metrics_L3](/#!/model/model.Analytics.V_F_Member_Metrics_L3) (base member metrics)
      - [D_Group](/#!/model/model.Analytics.D_Group) (group dimension)
      - [V_Billing_Partner](/#!/model/model.Analytics.V_Billing_Partner) (billing partner information)
      - [D_Metric](/#!/model/model.Analytics.D_Metric) (metric definitions)
      - T_Batch_Control (processing control dates)

      Indirect Sources:
      - [V_F_Member_Metrics_L2](/#!/model/model.Analytics.V_F_Member_Metrics_L2) (through L3)
      - [V_F_Member_Metrics_L1](/#!/model/model.Analytics.V_F_Member_Metrics_L1) (through L2)

      Key Business Rules:
      - Excludes SSN '999999999' from primary count
      - Only includes records where SUBSCRIBER_CNT = 1
      - Deduplicates members within Group and Billing Partner
      - Processes SSN '999999999' records separately

    config:
      materialized: view
      tags: ['silver', 'member', 'view', 'MxDataMartDailyBuild']

    transformations:
      - name: batch_date_selection
        description: |
          Retrieves the processing date from batch control:
          - Uses MxDataMartDailyBuild batch_id
          - Converts batch_control_dt to DATE type

      - name: metric_selection
        description: |
          Selects the Total_Billable_Cnt metric:
          - Filters for current effective dates
          - Uses batch date for temporal validity

      - name: distinct_subscriber_processing
        description: |
          Processes distinct subscribers:
          - Filters out SSN '999999999'
          - Ensures SUBSCRIBER_CNT = 1
          - Deduplicates using ROW_NUMBER()
        joins:
          - join: D_Group
            type: left
            relationship: many_to_one
            sql: "l3.Group_Id = dg.Group_Id"

          - join: V_Billing_Partner
            type: left
            relationship: many_to_one
            sql: "l3.Billing_Partner_Id = vbp.Billing_Partner_Id"

    columns:
      # Key Columns
      - name: Billing_Partner_SK
        description: "Surrogate key for billing partner, -1 if not found"
        type: string
        source_column: "Billing_Partner_SK"
        source_table: "V_Billing_Partner"
        transformation_logic: "CASE WHEN vbp.Billing_Partner_SK IS NULL THEN '-1' ELSE vbp.Billing_Partner_SK END"

      - name: Group_SK
        description: "Surrogate key for group, -1 if not found"
        type: string
        source_column: "Group_SK"
        source_table: "D_Group"
        transformation_logic: "CASE WHEN dg.Group_SK IS NULL THEN '-1' ELSE dg.Group_SK END"

      - name: Metric_SK
        description: "Surrogate key for Total_Billable_Cnt metric"
        type: string
        source_column: "Metric_SK"
        source_table: "D_Metric"

      - name: As_Of_Date
        description: "Date for which the billing metrics are calculated"
        type: date
        source_column: "As_Of_Date"
        source_table: "V_F_Member_Metrics_L3"

      # Metric Columns
      - name: Billing_Count
        description: "Count of distinct billable members"
        type: integer
        transformation_logic: "COUNT(DISTINCT CONCAT(l3.Member_Number,'|',l3.Person_Number,'|',l3.Elig_Group_Number))"

    meta:
      owner: "Data Engineering Team"
      updated_at: "2023-12-31"
      depends_on:
        - "V_F_Member_Metrics_L3" # [V_F_Member_Metrics_L3](/#!/model/model.Analytics.V_F_Member_Metrics_L3)
        - "D_Group" # [D_Group](/#!/model/model.Analytics.D_Group)
        - "V_Billing_Partner" # [V_Billing_Partner](/#!/model/model.Analytics.V_Billing_Partner)
        - "D_Metric" # [D_Metric](/#!/model/model.Analytics.D_Metric)
        - "T_Batch_Control" # Database table, not a dbt model
      table_type: "view"
      temporal_type: "current"
      refresh_frequency: "daily"