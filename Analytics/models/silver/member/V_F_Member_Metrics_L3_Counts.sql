{{ config(
    materialized='view'
) }}


WITH batch_date AS (
    SELECT 
        CAST(batch_control_dt AS DATE) AS batch_date -- Assuming `batch_date` is the column name
    FROM `{{ env_var("PROJECT_ID") }}`.`Warehouse`.`T_Batch_Control`
    WHERE batch_id = 'MxDataMartDailyBuild'
)


,d_metric_elig_sub as (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Elig_Subscriber_Cnt"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)
,d_metric_elig_reg_sub as (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Elig_Registered_Subscriber_Cnt"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)
,d_metric_elig_dep as (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Elig_Dependent_Cnt"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)
,d_metric_elig_reg_dep as (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Elig_Registered_Dependent_Cnt"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)
,d_metric_sub_reeng_daily_total AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Subscriber_ReEngagements_Daily"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)

,d_metric_sub_reeng_daily_unique AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Subscriber_ReEngagements_Daily_Unique"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)

,d_metric_sub_reeng_mtd_total AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Subscriber_ReEngagements_MTD"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)

,d_metric_sub_reeng_mtd_unique AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Subscriber_ReEngagements_MTD_Unique"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)
,d_metric_dep_reeng_daily_total AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Dependent_ReEngagements_Daily"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)
,d_metric_dep_reeng_daily_unique AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Dependent_ReEngagements_Daily_Unique"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)
,d_metric_dep_reeng_mtd_total AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Dependent_ReEngagements_MTD"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)
,d_metric_dep_reeng_mtd_unique AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Dependent_ReEngagements_MTD_Unique"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)
,d_metric_sub_reeng_ltd_unique AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Subscriber_ReEngagements_LTD_Unique"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)

,d_metric_dep_reeng_ltd_unique AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Dependent_ReEngagements_LTD_Unique"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)

,d_metric_sub_reeng_ytd_unique AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Subscriber_ReEngagements_YTD_Unique"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)

,d_metric_dep_reeng_ytd_unique AS (
    SELECT Metric_SK FROM {{ ref('D_Metric') }}
    WHERE Metric_Name = "Total_Dependent_ReEngagements_YTD_Unique"
    AND Effective_From_Dt <= (SELECT batch_date.batch_date FROM batch_date)
    AND Effective_To_Dt >= (SELECT batch_date.batch_date FROM batch_date)
)

,distinct_ssn_subs as (
  SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Subscriber_SSN <> '999999999'
    AND SUBSCRIBER_CNT = 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Subscriber_SSN,Group_Id,Billing_Partner_Id ORDER BY Member_Number DESC) = 1

  UNION ALL

    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Subscriber_SSN = '999999999'
    AND SUBSCRIBER_CNT = 1
)

,distinct_ssn_deps as (
  SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Member_SSN <> '999999999'
    AND SUBSCRIBER_CNT <> 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Member_SSN,Group_Id,Billing_Partner_Id ORDER BY Member_Number DESC) = 1

  UNION ALL

    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Member_SSN = '999999999'
    AND SUBSCRIBER_CNT <> 1
)

,distinct_ssn_reg_subs as (
  SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Subscriber_SSN <> '999999999'
    AND SUBSCRIBER_CNT = 1
    AND REGISTERED_ELIG = 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Subscriber_SSN,Group_Id,Billing_Partner_Id ORDER BY Member_Number DESC) = 1

  UNION ALL

    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Subscriber_SSN = '999999999'
    AND SUBSCRIBER_CNT = 1
    AND REGISTERED_ELIG = 1
)

,distinct_ssn_reg_deps as (
  SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Member_SSN <> '999999999'
    AND SUBSCRIBER_CNT <> 1
    AND REGISTERED_ELIG = 1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY Member_SSN,Group_Id,Billing_Partner_Id ORDER BY Member_Number DESC) = 1

  UNION ALL

    SELECT * 
    FROM {{ ref('V_F_Member_Metrics_L3') }} 
    WHERE Member_SSN = '999999999'
    AND SUBSCRIBER_CNT <> 1
    AND REGISTERED_ELIG = 1
)


SELECT 

 CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
    ELSE vbp.Billing_Partner_SK
    END AS Billing_Partner_SK
,CASE WHEN dg.Group_SK IS NULL THEN "-1"
    ELSE dg.Group_SK
    END AS Group_SK
,CASE WHEN lob.LOB_SK IS NULL THEN "-1"
    ELSE lob.LOB_SK
    END AS LOB_SK
,CASE WHEN da.Age_SK IS NULL THEN "-1"
    ELSE da.Age_SK
    END AS Age_SK
,(SELECT d_metric_elig_sub.Metric_SK FROM d_metric_elig_sub) as Metric_SK
,l3.As_Of_Date
,COUNT(DISTINCT CONCAT(l3.Member_Number,'|',l3.Person_Number,'|',l3.Elig_Group_Number)) as Metric_Value

FROM distinct_ssn_subs l3

LEFT JOIN {{ ref('D_Group') }} dg
ON l3.Group_Id = dg.Group_Id
AND dg.Effective_From_Dt <= l3.As_Of_Date
AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
AND vbp.Effective_From_Dt <= l3.As_Of_Date
AND vbp.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('D_LOB') }} lob
ON l3.Lob_Cd = lob.Lob_Cd

LEFT JOIN {{ ref('D_Age') }} da
ON da.Age = l3.Age

GROUP BY 
vbp.Billing_Partner_SK
,dg.Group_SK
,lob.LOB_SK
,da.Age_SK
,l3.As_Of_Date
,Metric_SK

UNION ALL

SELECT 

 CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
    ELSE vbp.Billing_Partner_SK
    END AS Billing_Partner_SK
,CASE WHEN dg.Group_SK IS NULL THEN "-1"
    ELSE dg.Group_SK
    END AS Group_SK
,CASE WHEN lob.LOB_SK IS NULL THEN "-1"
    ELSE lob.LOB_SK
    END AS LOB_SK
,CASE WHEN da.Age_SK IS NULL THEN "-1"
    ELSE da.Age_SK
    END AS Age_SK
,(SELECT d_metric_elig_reg_sub.Metric_SK FROM d_metric_elig_reg_sub) as Metric_SK
,l3.As_Of_Date
,COUNT(DISTINCT CONCAT(l3.Member_Number,'|',l3.Person_Number,'|',l3.Elig_Group_Number)) as Metric_Value

FROM distinct_ssn_reg_subs l3

LEFT JOIN {{ ref('D_Group') }} dg
ON l3.Group_Id = dg.Group_Id
AND dg.Effective_From_Dt <= l3.As_Of_Date
AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
AND vbp.Effective_From_Dt <= l3.As_Of_Date
AND vbp.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('D_LOB') }} lob
ON l3.Lob_Cd = lob.Lob_Cd

LEFT JOIN {{ ref('D_Age') }} da
ON da.Age = l3.Age

GROUP BY 
vbp.Billing_Partner_SK
,dg.Group_SK
,lob.LOB_SK
,da.Age_SK
,l3.As_Of_Date
,Metric_SK

UNION ALL

SELECT 

 CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
    ELSE vbp.Billing_Partner_SK
    END AS Billing_Partner_SK
,CASE WHEN dg.Group_SK IS NULL THEN "-1"
    ELSE dg.Group_SK
    END AS Group_SK
,CASE WHEN lob.LOB_SK IS NULL THEN "-1"
    ELSE lob.LOB_SK
    END AS LOB_SK
,CASE WHEN da.Age_SK IS NULL THEN "-1"
    ELSE da.Age_SK
    END AS Age_SK
,(SELECT d_metric_elig_dep.Metric_SK FROM d_metric_elig_dep) as Metric_SK
,l3.As_Of_Date
,COUNT(DISTINCT CONCAT(l3.Member_Number,'|',l3.Person_Number,'|',l3.Elig_Group_Number)) as Metric_Value

FROM distinct_ssn_deps l3

LEFT JOIN {{ ref('D_Group') }} dg
ON l3.Group_Id = dg.Group_Id
AND dg.Effective_From_Dt <= l3.As_Of_Date
AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
AND vbp.Effective_From_Dt <= l3.As_Of_Date
AND vbp.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('D_LOB') }} lob
ON l3.Lob_Cd = lob.Lob_Cd

LEFT JOIN {{ ref('D_Age') }} da
ON da.Age = l3.Age

GROUP BY 
vbp.Billing_Partner_SK
,dg.Group_SK
,lob.LOB_SK
,da.Age_SK
,l3.As_Of_Date
,Metric_SK

UNION ALL

SELECT 

 CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
    ELSE vbp.Billing_Partner_SK
    END AS Billing_Partner_SK
,CASE WHEN dg.Group_SK IS NULL THEN "-1"
    ELSE dg.Group_SK
    END AS Group_SK
,CASE WHEN lob.LOB_SK IS NULL THEN "-1"
    ELSE lob.LOB_SK
    END AS LOB_SK
,CASE WHEN da.Age_SK IS NULL THEN "-1"
    ELSE da.Age_SK
    END AS Age_SK
,(SELECT d_metric_elig_reg_dep.Metric_SK FROM d_metric_elig_reg_dep) as Metric_SK
,l3.As_Of_Date
,COUNT(DISTINCT CONCAT(l3.Member_Number,'|',l3.Person_Number,'|',l3.Elig_Group_Number)) as Metric_Value

FROM distinct_ssn_reg_deps l3

LEFT JOIN {{ ref('D_Group') }} dg
ON l3.Group_Id = dg.Group_Id
AND dg.Effective_From_Dt <= l3.As_Of_Date
AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
AND vbp.Effective_From_Dt <= l3.As_Of_Date
AND vbp.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('D_LOB') }} lob
ON l3.Lob_Cd = lob.Lob_Cd

LEFT JOIN {{ ref('D_Age') }} da
ON da.Age = l3.Age


GROUP BY 
vbp.Billing_Partner_SK
,dg.Group_SK
,lob.LOB_SK
,da.Age_SK
,l3.As_Of_Date
,Metric_SK

UNION ALL

SELECT
 CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
    ELSE vbp.Billing_Partner_SK
    END AS Billing_Partner_SK
,CASE WHEN dg.Group_SK IS NULL THEN "-1"
    ELSE dg.Group_SK
    END AS Group_SK
,CASE WHEN lob.LOB_SK IS NULL THEN "-1"
    ELSE lob.LOB_SK
    END AS LOB_SK
,CASE WHEN da.Age_SK IS NULL THEN "-1"
    ELSE da.Age_SK
    END AS Age_SK
,(SELECT d_metric_sub_reeng_daily_total.Metric_SK FROM d_metric_sub_reeng_daily_total) as Metric_SK
,l3.As_Of_Date
,SUM(v_reeng.Daily_Total_ReEngagements) AS Metric_Value

FROM distinct_ssn_subs l3

LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
ON l3.Elig_Id = v_reeng.Elig_Id

LEFT JOIN {{ ref('D_Group') }} dg
ON l3.Group_Id = dg.Group_Id
AND dg.Effective_From_Dt <= l3.As_Of_Date
AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
AND vbp.Effective_From_Dt <= l3.As_Of_Date
AND vbp.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('D_LOB') }} lob
ON l3.Lob_Cd = lob.Lob_Cd

LEFT JOIN {{ ref('D_Age') }} da
ON da.Age = l3.Age


GROUP BY 
vbp.Billing_Partner_SK
,dg.Group_SK
,lob.LOB_SK
,da.Age_SK
,l3.As_Of_Date
,Metric_SK

UNION ALL

SELECT
 CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
    ELSE vbp.Billing_Partner_SK
    END AS Billing_Partner_SK
,CASE WHEN dg.Group_SK IS NULL THEN "-1"
    ELSE dg.Group_SK
    END AS Group_SK
,CASE WHEN lob.LOB_SK IS NULL THEN "-1"
    ELSE lob.LOB_SK
    END AS LOB_SK
,CASE WHEN da.Age_SK IS NULL THEN "-1"
    ELSE da.Age_SK
    END AS Age_SK
,(SELECT d_metric_sub_reeng_daily_unique.Metric_SK FROM d_metric_sub_reeng_daily_unique) as Metric_SK
,l3.As_Of_Date
,SUM(v_reeng.Daily_Unique_ReEngagements) AS Metric_Value

FROM distinct_ssn_subs l3

LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
ON l3.Elig_Id = v_reeng.Elig_Id

LEFT JOIN {{ ref('D_Group') }} dg
ON l3.Group_Id = dg.Group_Id
AND dg.Effective_From_Dt <= l3.As_Of_Date
AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
AND vbp.Effective_From_Dt <= l3.As_Of_Date
AND vbp.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('D_LOB') }} lob
ON l3.Lob_Cd = lob.Lob_Cd

LEFT JOIN {{ ref('D_Age') }} da
ON da.Age = l3.Age


GROUP BY 
vbp.Billing_Partner_SK
,dg.Group_SK
,lob.LOB_SK
,da.Age_SK
,l3.As_Of_Date
,Metric_SK

UNION ALL

SELECT
    CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
        ELSE vbp.Billing_Partner_SK
        END AS Billing_Partner_SK,
    CASE WHEN dg.Group_SK IS NULL THEN "-1"
        ELSE dg.Group_SK
        END AS Group_SK,
    CASE WHEN lob.LOB_SK IS NULL THEN "-1"
        ELSE lob.LOB_SK
        END AS LOB_SK,
    CASE WHEN da.Age_SK IS NULL THEN "-1"
        ELSE da.Age_SK
        END AS Age_SK,
    (SELECT d_metric_sub_reeng_mtd_total.Metric_SK FROM d_metric_sub_reeng_mtd_total) AS Metric_SK,
    l3.As_Of_Date,
    SUM(v_reeng.MTD_Total_ReEngagements) AS Metric_Value

FROM distinct_ssn_subs l3

LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
    ON l3.Elig_Id = v_reeng.Elig_Id

LEFT JOIN {{ ref('D_Group') }} dg
    ON l3.Group_Id = dg.Group_Id
    AND dg.Effective_From_Dt <= l3.As_Of_Date
    AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
    ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
    AND vbp.Effective_From_Dt <= l3.As_Of_Date
    AND vbp.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('D_LOB') }} lob
    ON l3.Lob_Cd = lob.Lob_Cd

LEFT JOIN {{ ref('D_Age') }} da
    ON da.Age = l3.Age

GROUP BY 
    vbp.Billing_Partner_SK,
    dg.Group_SK,
    lob.LOB_SK,
    da.Age_SK,
    l3.As_Of_Date,
    Metric_SK

UNION ALL

SELECT
    CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
        ELSE vbp.Billing_Partner_SK
        END AS Billing_Partner_SK,
    CASE WHEN dg.Group_SK IS NULL THEN "-1"
        ELSE dg.Group_SK
        END AS Group_SK,
    CASE WHEN lob.LOB_SK IS NULL THEN "-1"
        ELSE lob.LOB_SK
        END AS LOB_SK,
    CASE WHEN da.Age_SK IS NULL THEN "-1"
        ELSE da.Age_SK
        END AS Age_SK,
    (SELECT d_metric_sub_reeng_mtd_unique.Metric_SK FROM d_metric_sub_reeng_mtd_unique) AS Metric_SK,
    l3.As_Of_Date,
    SUM(v_reeng.MTD_Unique_ReEngagements) AS Metric_Value

FROM distinct_ssn_subs l3

LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
    ON l3.Elig_Id = v_reeng.Elig_Id

LEFT JOIN {{ ref('D_Group') }} dg
    ON l3.Group_Id = dg.Group_Id
    AND dg.Effective_From_Dt <= l3.As_Of_Date
    AND dg.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
    ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
    AND vbp.Effective_From_Dt <= l3.As_Of_Date
    AND vbp.Effective_To_Dt >= l3.As_Of_Date

LEFT JOIN {{ ref('D_LOB') }} lob
    ON l3.Lob_Cd = lob.Lob_Cd

LEFT JOIN {{ ref('D_Age') }} da
    ON da.Age = l3.Age

GROUP BY 
    vbp.Billing_Partner_SK,
    dg.Group_SK,
    lob.LOB_SK,
    da.Age_SK,
    l3.As_Of_Date,
    Metric_SK

    UNION ALL

    SELECT
        CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
            ELSE vbp.Billing_Partner_SK
            END AS Billing_Partner_SK,
        CASE WHEN dg.Group_SK IS NULL THEN "-1"
            ELSE dg.Group_SK
            END AS Group_SK,
        CASE WHEN lob.LOB_SK IS NULL THEN "-1"
            ELSE lob.LOB_SK
            END AS LOB_SK,
        CASE WHEN da.Age_SK IS NULL THEN "-1"
            ELSE da.Age_SK
            END AS Age_SK,
        (SELECT d_metric_dep_reeng_daily_total.Metric_SK FROM d_metric_dep_reeng_daily_total) AS Metric_SK,
        l3.As_Of_Date,
        SUM(v_reeng.Daily_Total_ReEngagements) AS Metric_Value

    FROM distinct_ssn_deps l3

    LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
        ON l3.Elig_Id = v_reeng.Elig_Id

    LEFT JOIN {{ ref('D_Group') }} dg
        ON l3.Group_Id = dg.Group_Id
        AND dg.Effective_From_Dt <= l3.As_Of_Date
        AND dg.Effective_To_Dt >= l3.As_Of_Date

    LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
        ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
        AND vbp.Effective_From_Dt <= l3.As_Of_Date
        AND vbp.Effective_To_Dt >= l3.As_Of_Date

    LEFT JOIN {{ ref('D_LOB') }} lob
        ON l3.Lob_Cd = lob.Lob_Cd

    LEFT JOIN {{ ref('D_Age') }} da
        ON da.Age = l3.Age

    GROUP BY 
        vbp.Billing_Partner_SK,
        dg.Group_SK,
        lob.LOB_SK,
        da.Age_SK,
        l3.As_Of_Date,
        Metric_SK

    UNION ALL

    SELECT
        CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
            ELSE vbp.Billing_Partner_SK
            END AS Billing_Partner_SK,
        CASE WHEN dg.Group_SK IS NULL THEN "-1"
            ELSE dg.Group_SK
            END AS Group_SK,
        CASE WHEN lob.LOB_SK IS NULL THEN "-1"
            ELSE lob.LOB_SK
            END AS LOB_SK,
        CASE WHEN da.Age_SK IS NULL THEN "-1"
            ELSE da.Age_SK
            END AS Age_SK,
        (SELECT d_metric_dep_reeng_daily_unique.Metric_SK FROM d_metric_dep_reeng_daily_unique) AS Metric_SK,
        l3.As_Of_Date,
        SUM(v_reeng.Daily_Unique_ReEngagements) AS Metric_Value

    FROM distinct_ssn_deps l3

    LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
        ON l3.Elig_Id = v_reeng.Elig_Id

    LEFT JOIN {{ ref('D_Group') }} dg
        ON l3.Group_Id = dg.Group_Id
        AND dg.Effective_From_Dt <= l3.As_Of_Date
        AND dg.Effective_To_Dt >= l3.As_Of_Date

    LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
        ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
        AND vbp.Effective_From_Dt <= l3.As_Of_Date
        AND vbp.Effective_To_Dt >= l3.As_Of_Date

    LEFT JOIN {{ ref('D_LOB') }} lob
        ON l3.Lob_Cd = lob.Lob_Cd

    LEFT JOIN {{ ref('D_Age') }} da
        ON da.Age = l3.Age

    GROUP BY 
        vbp.Billing_Partner_SK,
        dg.Group_SK,
        lob.LOB_SK,
        da.Age_SK,
        l3.As_Of_Date,
        Metric_SK

    UNION ALL

    SELECT
        CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
            ELSE vbp.Billing_Partner_SK
            END AS Billing_Partner_SK,
        CASE WHEN dg.Group_SK IS NULL THEN "-1"
            ELSE dg.Group_SK
            END AS Group_SK,
        CASE WHEN lob.LOB_SK IS NULL THEN "-1"
            ELSE lob.LOB_SK
            END AS LOB_SK,
        CASE WHEN da.Age_SK IS NULL THEN "-1"
            ELSE da.Age_SK
            END AS Age_SK,
        (SELECT d_metric_dep_reeng_mtd_total.Metric_SK FROM d_metric_dep_reeng_mtd_total) AS Metric_SK,
        l3.As_Of_Date,
        SUM(v_reeng.MTD_Total_ReEngagements) AS Metric_Value

    FROM distinct_ssn_deps l3

    LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
        ON l3.Elig_Id = v_reeng.Elig_Id

    LEFT JOIN {{ ref('D_Group') }} dg
        ON l3.Group_Id = dg.Group_Id
        AND dg.Effective_From_Dt <= l3.As_Of_Date
        AND dg.Effective_To_Dt >= l3.As_Of_Date

    LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
        ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
        AND vbp.Effective_From_Dt <= l3.As_Of_Date
        AND vbp.Effective_To_Dt >= l3.As_Of_Date

    LEFT JOIN {{ ref('D_LOB') }} lob
        ON l3.Lob_Cd = lob.Lob_Cd

    LEFT JOIN {{ ref('D_Age') }} da
        ON da.Age = l3.Age

    GROUP BY 
        vbp.Billing_Partner_SK,
        dg.Group_SK,
        lob.LOB_SK,
        da.Age_SK,
        l3.As_Of_Date,
        Metric_SK

    UNION ALL

    SELECT
        CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
            ELSE vbp.Billing_Partner_SK
            END AS Billing_Partner_SK,
        CASE WHEN dg.Group_SK IS NULL THEN "-1"
            ELSE dg.Group_SK
            END AS Group_SK,
        CASE WHEN lob.LOB_SK IS NULL THEN "-1"
            ELSE lob.LOB_SK
            END AS LOB_SK,
        CASE WHEN da.Age_SK IS NULL THEN "-1"
            ELSE da.Age_SK
            END AS Age_SK,
        (SELECT d_metric_dep_reeng_mtd_unique.Metric_SK FROM d_metric_dep_reeng_mtd_unique) AS Metric_SK,
        l3.As_Of_Date,
        SUM(v_reeng.MTD_Unique_ReEngagements) AS Metric_Value

    FROM distinct_ssn_deps l3

    LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
        ON l3.Elig_Id = v_reeng.Elig_Id

    LEFT JOIN {{ ref('D_Group') }} dg
        ON l3.Group_Id = dg.Group_Id
        AND dg.Effective_From_Dt <= l3.As_Of_Date
        AND dg.Effective_To_Dt >= l3.As_Of_Date

    LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
        ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
        AND vbp.Effective_From_Dt <= l3.As_Of_Date
        AND vbp.Effective_To_Dt >= l3.As_Of_Date

    LEFT JOIN {{ ref('D_LOB') }} lob
        ON l3.Lob_Cd = lob.Lob_Cd

    LEFT JOIN {{ ref('D_Age') }} da
        ON da.Age = l3.Age

    GROUP BY 
        vbp.Billing_Partner_SK,
        dg.Group_SK,
        lob.LOB_SK,
        da.Age_SK,
        l3.As_Of_Date,
        Metric_SK

        UNION ALL

        SELECT
            CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
                ELSE vbp.Billing_Partner_SK
                END AS Billing_Partner_SK,
            CASE WHEN dg.Group_SK IS NULL THEN "-1"
                ELSE dg.Group_SK
                END AS Group_SK,
            CASE WHEN lob.LOB_SK IS NULL THEN "-1"
                ELSE lob.LOB_SK
                END AS LOB_SK,
            CASE WHEN da.Age_SK IS NULL THEN "-1"
                ELSE da.Age_SK
                END AS Age_SK,
            (SELECT d_metric_sub_reeng_ltd_unique.Metric_SK FROM d_metric_sub_reeng_ltd_unique) AS Metric_SK,
            l3.As_Of_Date,
            SUM(v_reeng.LTD_Unique_ReEngagements) AS Metric_Value

        FROM distinct_ssn_subs l3

        LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
            ON l3.Elig_Id = v_reeng.Elig_Id

        LEFT JOIN {{ ref('D_Group') }} dg
            ON l3.Group_Id = dg.Group_Id
            AND dg.Effective_From_Dt <= l3.As_Of_Date
            AND dg.Effective_To_Dt >= l3.As_Of_Date

        LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
            ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
            AND vbp.Effective_From_Dt <= l3.As_Of_Date
            AND vbp.Effective_To_Dt >= l3.As_Of_Date

        LEFT JOIN {{ ref('D_LOB') }} lob
            ON l3.Lob_Cd = lob.Lob_Cd

        LEFT JOIN {{ ref('D_Age') }} da
            ON da.Age = l3.Age

        GROUP BY 
            vbp.Billing_Partner_SK,
            dg.Group_SK,
            lob.LOB_SK,
            da.Age_SK,
            l3.As_Of_Date,
            Metric_SK

        UNION ALL

        SELECT
            CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
                ELSE vbp.Billing_Partner_SK
                END AS Billing_Partner_SK,
            CASE WHEN dg.Group_SK IS NULL THEN "-1"
                ELSE dg.Group_SK
                END AS Group_SK,
            CASE WHEN lob.LOB_SK IS NULL THEN "-1"
                ELSE lob.LOB_SK
                END AS LOB_SK,
            CASE WHEN da.Age_SK IS NULL THEN "-1"
                ELSE da.Age_SK
                END AS Age_SK,
            (SELECT d_metric_dep_reeng_ltd_unique.Metric_SK FROM d_metric_dep_reeng_ltd_unique) AS Metric_SK,
            l3.As_Of_Date,
            SUM(v_reeng.LTD_Unique_ReEngagements) AS Metric_Value

        FROM distinct_ssn_deps l3

        LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
            ON l3.Elig_Id = v_reeng.Elig_Id

        LEFT JOIN {{ ref('D_Group') }} dg
            ON l3.Group_Id = dg.Group_Id
            AND dg.Effective_From_Dt <= l3.As_Of_Date
            AND dg.Effective_To_Dt >= l3.As_Of_Date

        LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
            ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
            AND vbp.Effective_From_Dt <= l3.As_Of_Date
            AND vbp.Effective_To_Dt >= l3.As_Of_Date

        LEFT JOIN {{ ref('D_LOB') }} lob
            ON l3.Lob_Cd = lob.Lob_Cd

        LEFT JOIN {{ ref('D_Age') }} da
            ON da.Age = l3.Age

        GROUP BY 
            vbp.Billing_Partner_SK,
            dg.Group_SK,
            lob.LOB_SK,
            da.Age_SK,
            l3.As_Of_Date,
            Metric_SK

        UNION ALL

        SELECT
            CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
                ELSE vbp.Billing_Partner_SK
                END AS Billing_Partner_SK,
            CASE WHEN dg.Group_SK IS NULL THEN "-1"
                ELSE dg.Group_SK
                END AS Group_SK,
            CASE WHEN lob.LOB_SK IS NULL THEN "-1"
                ELSE lob.LOB_SK
                END AS LOB_SK,
            CASE WHEN da.Age_SK IS NULL THEN "-1"
                ELSE da.Age_SK
                END AS Age_SK,
            (SELECT d_metric_sub_reeng_ytd_unique.Metric_SK FROM d_metric_sub_reeng_ytd_unique) AS Metric_SK,
            l3.As_Of_Date,
            SUM(v_reeng.YTD_Unique_ReEngagements) AS Metric_Value

        FROM distinct_ssn_subs l3

        LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
            ON l3.Elig_Id = v_reeng.Elig_Id

        LEFT JOIN {{ ref('D_Group') }} dg
            ON l3.Group_Id = dg.Group_Id
            AND dg.Effective_From_Dt <= l3.As_Of_Date
            AND dg.Effective_To_Dt >= l3.As_Of_Date

        LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
            ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
            AND vbp.Effective_From_Dt <= l3.As_Of_Date
            AND vbp.Effective_To_Dt >= l3.As_Of_Date

        LEFT JOIN {{ ref('D_LOB') }} lob
            ON l3.Lob_Cd = lob.Lob_Cd

        LEFT JOIN {{ ref('D_Age') }} da
            ON da.Age = l3.Age

        GROUP BY 
            vbp.Billing_Partner_SK,
            dg.Group_SK,
            lob.LOB_SK,
            da.Age_SK,
            l3.As_Of_Date,
            Metric_SK

        UNION ALL

        SELECT
            CASE WHEN vbp.Billing_Partner_SK IS NULL THEN "-1"
                ELSE vbp.Billing_Partner_SK
                END AS Billing_Partner_SK,
            CASE WHEN dg.Group_SK IS NULL THEN "-1"
                ELSE dg.Group_SK
                END AS Group_SK,
            CASE WHEN lob.LOB_SK IS NULL THEN "-1"
                ELSE lob.LOB_SK
                END AS LOB_SK,
            CASE WHEN da.Age_SK IS NULL THEN "-1"
                ELSE da.Age_SK
                END AS Age_SK,
            (SELECT d_metric_dep_reeng_ytd_unique.Metric_SK FROM d_metric_dep_reeng_ytd_unique) AS Metric_SK,
            l3.As_Of_Date,
            SUM(v_reeng.YTD_Unique_ReEngagements) AS Metric_Value

        FROM distinct_ssn_deps l3

        LEFT JOIN {{ ref('V_F_Member_ReEngagements') }} v_reeng
            ON l3.Elig_Id = v_reeng.Elig_Id

        LEFT JOIN {{ ref('D_Group') }} dg
            ON l3.Group_Id = dg.Group_Id
            AND dg.Effective_From_Dt <= l3.As_Of_Date
            AND dg.Effective_To_Dt >= l3.As_Of_Date

        LEFT JOIN {{ ref('V_Billing_Partner') }} vbp
            ON l3.Billing_Partner_Id = vbp.Billing_Partner_Id
            AND vbp.Effective_From_Dt <= l3.As_Of_Date
            AND vbp.Effective_To_Dt >= l3.As_Of_Date

        LEFT JOIN {{ ref('D_LOB') }} lob
            ON l3.Lob_Cd = lob.Lob_Cd

        LEFT JOIN {{ ref('D_Age') }} da
            ON da.Age = l3.Age

        GROUP BY 
            vbp.Billing_Partner_SK,
            dg.Group_SK,
            lob.LOB_SK,
            da.Age_SK,
            l3.As_Of_Date,
            Metric_SK